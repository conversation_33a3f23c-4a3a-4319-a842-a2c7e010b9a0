﻿/*
 * Description:             CompareShareBoolNode.cs
 * Author:                  TONYTANG
 * Create Date:             2023/10/04
 */

using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// CompareShareBoolNode.cs
    /// 比较黑板Bool数据条件节点
    /// </summary>
    public class CompareShareBoolNode : BaseCompareShareNode
    {
        /// <summary>
        /// 比较数据
        /// </summary>
        [Header("比较数据")]
        public bool TargetValue;

        /// <summary>
        /// 值是否相等
        /// </summary>
        /// <returns></returns>
        protected override bool IsValueEqual()
        {
            var value = mOwnerTreeData.Blackboard.GetValue<bool>(VariableName);
            return TargetValue == value;
        }

        /// <summary>
        /// 值是否大于
        /// </summary>
        /// <returns></returns>
        protected override bool IsValueGreater()
        {
            Debug.LogError($"CompareShareBool不支持比较大于，请检查配置！");
            return false;
        }
    }
}