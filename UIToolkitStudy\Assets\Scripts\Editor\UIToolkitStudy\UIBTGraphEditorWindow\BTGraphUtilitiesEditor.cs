﻿/*
 * Description:     BTGraphUtilitiesEditor.cs
 * Author:          TonyT<PERSON>
 * Create Date:     2023/06/19
 */

using System;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEngine;
using Debug = UnityEngine.Debug;

namespace TCommonGraph
{
    /// <summary>
    /// BTGraphUtilitiesEditor.cs
    /// 行为树节点编辑器工具类
    /// </summary>
    public static class BTGraphUtilitiesEditor
    {
        /// <summary>
        /// 节点类型和对应节点基类类型信息Map<节点类型, 对应节点基类类型信息>
        /// </summary>
        private static readonly Dictionary<NodeType, Type> NodeTypeBaseTypeMap = new Dictionary<NodeType, Type>()
        {
            {NodeType.Composition, BTGraphConstEditor.BaseCompositionNodeType},
            {NodeType.Decoration, BTGraphConstEditor.BaseDecorationNodeType},
            {NodeType.Condition, BTGraphConstEditor.BaseConditionNodeType},
            {NodeType.Action, BTGraphConstEditor.BaseActionNodeType},
        };

        /// <summary>
        /// Node节点显示节点类型Map<Node数据类型信息, Node显示类型信息>
        /// Note:
        /// 没有特殊NodeView的不用定义
        /// 默认创建规则见GetNodeViewType(Type nodeType)
        /// </summary>
        private static readonly Dictionary<Type, Type> NodeViewTypeMap = new Dictionary<Type, Type>()
        {
            {BTGraphConstEditor.RootNodeType,BTGraphConstEditor.RootNodeViewType},
            {BTGraphConstEditor.ParalNodeType, BTGraphConstEditor.ParalNodeViewType},
            {BTGraphConstEditor.CompareShareBoolNodeType, BTGraphConstEditor.CompareShareBoolNodeViewType},
            {BTGraphConstEditor.CompareShareIntNodeViewType, BTGraphConstEditor.CompareShareIntNodeViewType},
            {BTGraphConstEditor.CompareShareFloatNodeType, BTGraphConstEditor.CompareShareFloatNodeViewType},
            {BTGraphConstEditor.CompareShareStringNodeType, BTGraphConstEditor.CompareShareStringNodeViewType},
            {BTGraphConstEditor.SetShareBoolNodeType, BTGraphConstEditor.SetShareBoolNodeViewType},
            {BTGraphConstEditor.SetShareIntNodeType, BTGraphConstEditor.SetShareIntNodeViewType},
            {BTGraphConstEditor.SetShareFloatNodeType, BTGraphConstEditor.SetShareFloatNodeViewType},
            {BTGraphConstEditor.SetShareStringNodeType, BTGraphConstEditor.SetShareStringNodeViewType},
            {BTGraphConstEditor.LogNodeType, BTGraphConstEditor.LogNodeViewType},
        };

        /// <summary>
        /// 节点类型和标题Map
        /// </summary>
        private static readonly Dictionary<NodeType, string> NodeTypeTitleMap = new Dictionary<NodeType, string>()
        {
            {NodeType.Composition, "组合节点" },
            {NodeType.Decoration, "装饰节点" },
            {NodeType.Condition, "条件节点" },
            {NodeType.Action, "行为节点" },
        };

        /// <summary>
        /// 节点类型和折叠ViewDataKey Map
        /// </summary>
        private static readonly Dictionary<NodeType, string> NodeTypeFoldViewDataKeyMap = new Dictionary<NodeType, string>()
        {
            { NodeType.Composition, BTGraphDataKeys.CompositionNodeFoldOutViewDataKeyName },
            { NodeType.Decoration, BTGraphDataKeys.DecorationNodeFoldOutViewDataKeyName },
            { NodeType.Condition, BTGraphDataKeys.ConditionNodeFoldOutViewDataKeyName },
            { NodeType.Action, BTGraphDataKeys.ActionNodeFoldOutViewDataKeyName },
        };

        /// <summary>
        /// 节点类型和颜色背景Map
        /// </summary>
        private static readonly Dictionary<NodeType, Color> NodeTypeBackgroundColorMap = new Dictionary<NodeType, Color>
        {
            {NodeType.Composition, Color.blue},
            {NodeType.Decoration, Color.cyan},
            {NodeType.Condition, Color.yellow},
            {NodeType.Action, Color.red},
        };

        /// <summary>
        /// 节点状态和颜色背景Map
        /// </summary>
        private static readonly Dictionary<NodeState, Color> NodeStateColorMap = new Dictionary<NodeState, Color>
        {
            {NodeState.Suspend, Color.grey},
            {NodeState.Running, Color.yellow},
            {NodeState.Abort, Color.magenta},
            {NodeState.Success, Color.green},
            {NodeState.Failed, Color.red},
        };

        /// <summary>
        /// 变量类型和折叠标题文本Map
        /// </summary>
        private static readonly Dictionary<BlackboardDataType, string> VariableTypeFoldTitleMap = new Dictionary<BlackboardDataType, string>()
        {
            {BlackboardDataType.Bool, "布尔" },
            {BlackboardDataType.Int, "整形" },
            {BlackboardDataType.Float, "浮点数" },
            {BlackboardDataType.String, "字符串" },
        };

        /// <summary>
        /// 变量类型和折叠ViewDataKey Map
        /// </summary>
        private static readonly Dictionary<BlackboardDataType, string> VariableTypeFoldViewDataKeyMap = new Dictionary<BlackboardDataType, string>()
        {
            { BlackboardDataType.Bool, BTGraphDataKeys.BoolVariableTypeFoldOutViewDataKeyName },
            { BlackboardDataType.Int, BTGraphDataKeys.IntVariableTypeFoldOutViewDataKeyName },
            { BlackboardDataType.Float, BTGraphDataKeys.FloatVariableTypeFoldOutViewDataKeyName },
            { BlackboardDataType.String, BTGraphDataKeys.StringVariableTypeFoldOutViewDataKeyName },
        };

        /// <summary>
        /// 获取指定节点类型对应的基类节点类型信息
        /// </summary>
        /// <param name="nodeType"></param>
        /// <returns></returns>
        public static Type GetBaseTypeByNodeType(NodeType nodeType)
        {
            Type baseType;
            if (NodeTypeBaseTypeMap.TryGetValue(nodeType, out baseType))
            {
                return baseType;
            }
            Debug.LogError($"未定义节点类型:{nodeType}的节点基类类型信息！");
            return BTGraphConstEditor.BaseNodeType;
        }

        /// <summary>
        /// 获取指定节点类型的NodeView类型信息
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static Type GetNodeViewType(Type type)
        {
            Type nodeViewType;
            if (NodeViewTypeMap.TryGetValue(type, out nodeViewType))
            {
                return nodeViewType;
            }
            // 类层级越低的放前面，避免提前返回问题
            if (type.IsSubclassOf(BTGraphConstEditor.BaseConditionNodeType))
            {
                return BTGraphConstEditor.BaseConditionNodeViewType;
            }
            else if (type.IsSubclassOf(BTGraphConstEditor.BaseCompositionNodeType))
            {
                return BTGraphConstEditor.BaseCompositionNodeViewType;
            }
            else if (type.IsSubclassOf(BTGraphConstEditor.BaseSetShareNodeType))
            {
                return BTGraphConstEditor.BaseSetShareNodeViewType;
            }
            else if (type.IsSubclassOf(BTGraphConstEditor.BaseActionNodeType))
            {
                return BTGraphConstEditor.BaseActionNodeViewType;
            }
            else if (type.IsSubclassOf(BTGraphConstEditor.BaseCompareShareNodeType))
            {
                return BTGraphConstEditor.BaseCompareShareNodeViewType;
            }
            else if (type.IsSubclassOf(BTGraphConstEditor.BaseObservingDecorationNodeType))
            {
                return BTGraphConstEditor.BaseObservingDecorationNodeViewType;
            }
            else if (type.IsSubclassOf(BTGraphConstEditor.BaseDecorationNodeType))
            {
                return BTGraphConstEditor.BaseDecorationNodeViewType;
            }
            else
            {
                return BTGraphConstEditor.NodeViewType;
            }
        }

        /// <summary>
        /// 获取指定节点类型的标题
        /// </summary>
        /// <param name="nodeType"></param>
        /// <returns></returns>
        public static string GetNodeTypeTitle(NodeType nodeType)
        {
            string nodeTypeTitle;
            if (NodeTypeTitleMap.TryGetValue(nodeType, out nodeTypeTitle))
            {
                return nodeTypeTitle;
            }
            Debug.LogError($"未配置节点类型:{nodeType}的标题文本！");
            return "未知";
        }

        /// <summary>
        /// 获取指定节点类型的折叠ViewDataKey
        /// </summary>
        /// <param name="nodeType"></param>
        /// <returns></returns>
        public static string GetNodeTypeFoldOutViewDataKeyName(NodeType nodeType)
        {
            string nodeTypeFoldViewDataKeyName;
            if (NodeTypeFoldViewDataKeyMap.TryGetValue(nodeType, out nodeTypeFoldViewDataKeyName))
            {
                return nodeTypeFoldViewDataKeyName;
            }
            Debug.LogError($"未配置节点类型:{nodeType}的折叠ViewDataKey！");
            return nodeType.ToString();
        }

        /// <summary>
        /// 获取制定节点数据的背景颜色
        /// </summary>
        /// <param name="nodeData"></param>
        /// <returns></returns>
        public static Color GetBackgroundColorByNodeData(BaseNode nodeData)
        {
            if (!nodeData.EntryPoint)
            {
                return GetBackgroundColorByNodeType(nodeData.NodeType);
            }
            return BTGraphConstEditor.RootNodeColor;
        }

        /// <summary>
        /// 获取指定节点类型的背景颜色
        /// </summary>
        /// <param name="nodeType"></param>
        /// <returns></returns>
        public static Color GetBackgroundColorByNodeType(NodeType nodeType)
        {
            Color nodeTypeBackgroundColor;
            if (NodeTypeBackgroundColorMap.TryGetValue(nodeType, out nodeTypeBackgroundColor))
            {
                return nodeTypeBackgroundColor;
            }
            Debug.LogError($"未配置节点类型:{nodeType}的背景颜色！");
            return Color.grey;
        }

        /// <summary>
        /// 获取指定节点类型的背景颜色
        /// </summary>
        /// <param name="nodeState"></param>
        /// <returns></returns>
        public static Color GetColorByNodeState(NodeState nodeState)
        {
            Color nodeStateColor;
            if (NodeStateColorMap.TryGetValue(nodeState, out nodeStateColor))
            {
                return nodeStateColor;
            }
            Debug.LogError($"未配置节点状态:{nodeState}的颜色！");
            return Color.grey;
        }

        /// <summary>
        /// 获取指定节点状态类型对应的边颜色
        /// </summary>
        /// <param name="nodeState"></param>
        /// <returns></returns>
        public static Color GetEdgeColorByNodeState(NodeState nodeState)
        {
            // 未运行完成按照节点状态颜色显示边颜色，运行完成统一显示绿色
            if(nodeState == NodeState.Suspend || nodeState == NodeState.Running || nodeState == NodeState.Abort)
            {
                return GetColorByNodeState(nodeState);
            }
            return Color.green;
        }

        /// <summary>
        /// 创建指定Node类型信息实例对象
        /// </summary>
        /// <param name="typeInfo"></param>
        /// <returns></returns>
        public static BaseNode CreateNodeInstance(Type typeInfo)
        {
            if (typeInfo == null)
            {
                Debug.LogError($"不支持创建空类型信息的节点对象！");
                return null;
            }
            if (typeInfo != BTGraphConstEditor.BaseNodeType && !typeInfo.IsSubclassOf(BTGraphConstEditor.BaseNodeType))
            {
                Debug.LogError($"不允许创建未继承BaseNode的类型:{typeInfo.Name}信息节点对象！");
                return null;
            }
            // TODO: 硬编码类型构建避免反射
            return ScriptableObject.CreateInstance(typeInfo) as BaseNode;
        }

        /// <summary>
        /// 创建指定Node类型信息的NodeView实例对象
        /// </summary>
        /// <param name="typeInfo"></param>
        /// <returns></returns>
        public static NodeView CreateNodeViewInstance(Type typeInfo)
        {
            if (typeInfo == null)
            {
                Debug.LogError($"不支持创建空类型信息的节点View对象！");
                return null;
            }
            var nodeViewType = GetNodeViewType(typeInfo);
            // TODO: 硬编码类型构建避免反射
            if (nodeViewType != BTGraphConstEditor.NodeViewType && !nodeViewType.IsSubclassOf(BTGraphConstEditor.NodeViewType))
            {
                Debug.LogError($"不允许创建未继承BaseNode的类型:{nodeViewType.Name}信息节点View对象！");
                return null;
            }
            return Activator.CreateInstance(nodeViewType) as NodeView;
        }

        /// <summary>
        /// 获取制定变量类型的折叠标题文本
        /// </summary>
        /// <param name="variableType"></param>
        /// <returns></returns>
        public static string GetVariableTypeFoldTitle(BlackboardDataType variableType)
        {
            string foldTitle = string.Empty;
            if (!VariableTypeFoldTitleMap.TryGetValue(variableType, out foldTitle))
            {
                Debug.LogError($"未配置变量类型:{variableType.ToString()}的折叠标题！");
            }
            return foldTitle;
        }

        /// <summary>
        /// 获取指定变量类型的折叠ViewDataKey
        /// </summary>
        /// <param name="variableType"></param>
        /// <returns></returns>
        public static string GetVariableTypeFoldOutViewDataKeyName(BlackboardDataType variableType)
        {
            string variableTypeFoldViewDataKeyName;
            if (!VariableTypeFoldViewDataKeyMap.TryGetValue(variableType, out variableTypeFoldViewDataKeyName))
            {
                Debug.LogError($"未配置变量类型:{variableType.ToString()}的折叠ViewDataKey！");
            }
            return variableTypeFoldViewDataKeyName;
        }
    }
}