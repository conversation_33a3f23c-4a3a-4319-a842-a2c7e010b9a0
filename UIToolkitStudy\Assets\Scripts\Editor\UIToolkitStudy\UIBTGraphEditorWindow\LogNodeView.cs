﻿/*
 * Description:             LogNodeView.cs
 * Author:                  TONYTANG
 * Create Date:             2024/04/06
 */

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;

namespace TCommonGraph
{
    /// <summary>
    /// LogNodeView.cs
    /// </summary>
    public class LogNodeView : BaseActionNodeView
    {
        /// <summary>
        /// 创建节点自定义UI
        /// </summary>
        protected override void CreateCustomUI()
        {
            base.CreateCustomUI();
            var nodeVerticalUIContainer = this.Q<VisualElement>(BTGraphElementNames.NodeUIVerticalContainerName);
            var nodeDesDivider = UIToolkitUtilities.CreateHorizontalDivider(BTGraphConstEditor.DividerColor);
            nodeVerticalUIContainer.Add(nodeDesDivider);

            UIToolkitUtilities.CreateBindSOPropertyField(nodeVerticalUIContainer, NodeData, "LogContent");
        }
    }
}