﻿/*
 * Description:     DialogueNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/06/19
 */

using System;
using System.Collections.Generic;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// DialogueNode.cs
    /// 对话节点
    /// </summary>
    [Serializable]
    public class DialogueNode : BaseActionNode
    {
        /// <summary>
        /// 对话文本组
        /// </summary>
        [Header("对话文本组")]
        public List<string> DialogueTexts;

        /// <summary>
        /// 执行打断
        /// </summary>
        protected override void DoAbort()
        {
            base.DoAbort();
            Stop(false);
        }
    }
}