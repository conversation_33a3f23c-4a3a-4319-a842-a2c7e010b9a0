﻿/*
 * Description:     HealthValueNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/12/06
 */

using System;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// HealthValueNode.cs
    /// 生命值条件检查修饰节点类
    /// </summary>
    [Serializable]
    public class HealthValueNode : BaseConditionDecorationNode
    {
        /// <summary>
        /// 比较类型
        /// </summary>
        [Header("比较类型")]
        public ComparisonType ComparisonType;

        /// <summary>
        /// 生命值
        /// </summary>
        [Header("生命值")]
        public float HealthValue = 0f;

        /// <summary>
        /// 条件检查
        /// </summary>
        /// <returns></returns>
        protected override bool ConditionCheck()
        {
            var bindUID = mOwnerTreeData.OwnerGraphData.OwnerBT.BindUID;
            var bindActor = ActorManager.Singleton.GetActorByUID(bindUID);
            if(bindActor == null)
            {
                Debug.LogError($"找不到UID:{bindUID}对象，生命值检查比较失败！");
                return false;
            }
            if(ComparisonType == ComparisonType.LESS)
            {
                return bindActor.Health < HealthValue;
            }
            else if (ComparisonType == ComparisonType.GREATER)
            {
                return bindActor.Health > HealthValue;
            }
            else if (ComparisonType == ComparisonType.LESS_AND_EQUAL)
            {
                return bindActor.Health <= HealthValue;
            }
            else if (ComparisonType == ComparisonType.GREATER_AND_EQUAL)
            {
                return bindActor.Health >= HealthValue;
            }
            else if (ComparisonType == ComparisonType.EQUAL)
            {
                return bindActor.Health == HealthValue;
            }
            else
            {
                Debug.LogError($"不支持的比较运算类型:{ComparisonType},比较对象UID:{bindUID}的生命值失败！");
                return false;
            }
        }
    }
}
