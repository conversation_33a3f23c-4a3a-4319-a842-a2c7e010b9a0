﻿/*
 * Description:             BaseObservingDecorationNodeView.cs
 * Author:                  TONYTANG
 * Create Date:             2023/12/05
 */

using UnityEngine;
using UnityEngine.UIElements;

namespace TCommonGraph
{
    /// <summary>
    /// BaseObservingDecorationNodeView.cs
    /// 监听修饰节点NodeView基类
    /// </summary>
    public abstract class BaseObservingDecorationNodeView : BaseDecorationNodeView
    {
        /// <summary>
        /// 创建节点自定义UI
        /// </summary>
        protected override void CreateCustomUI()
        {
            base.CreateCustomUI();
            var nodeVerticalUIContainer = this.Q<VisualElement>(BTGraphElementNames.NodeUIVerticalContainerName);
            var nodeDesDivider = UIToolkitUtilities.CreateHorizontalDivider(BTGraphConstEditor.DividerColor);
            nodeVerticalUIContainer.Add(nodeDesDivider);

            UIToolkitUtilities.CreateBindSOPropertyField(nodeVerticalUIContainer, NodeData, "AbortType");
        }
    }
}
