﻿/*
 * Description:     BTGraphConstEditor.cs
 * Author:          TonyTang
 * Create Date:     2023/06/19
 */

using System;
using System.Reflection;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// BTGraphConstEditor.cs
    /// 通用节点编辑器常量数据
    /// </summary>
    public static class BTGraphConstEditor
    {
        /// <summary>
        /// 节点类型类型
        /// </summary>
        public static readonly Type NodeTypeType = typeof(NodeType);

        /// <summary>
        /// 节点基类类型
        /// </summary>
        public static readonly Type BaseNodeType = typeof(BaseNode);

        /// <summary>
        /// 根节点类型信息
        /// </summary>
        public static readonly Type RootNodeType = typeof(RootNode);

        /// <summary>
        /// 顺序节点类型
        /// </summary>
        public static readonly Type SequenceNodeType = typeof(SequenceNode);

        /// <summary>
        /// 比较黑板数据节点基类类型
        /// </summary>
        public static readonly Type BaseCompareShareNodeType = typeof(BaseCompareShareNode);

        /// <summary>
        /// 比较黑板bool数据节点类型信息
        /// </summary>
        public static readonly Type CompareShareBoolNodeType = typeof(CompareShareBoolNode);

        /// <summary>
        /// 比较黑板int数据节点类型信息
        /// </summary>
        public static readonly Type CompareShareIntNodeType = typeof(CompareShareIntNode);

        /// <summary>
        /// 比较黑板float数据节点类型信息
        /// </summary>
        public static readonly Type CompareShareFloatNodeType = typeof(CompareShareFloatNode);

        /// <summary>
        /// 比较黑板string数据节点基类类型
        /// </summary>
        public static readonly Type CompareShareStringNodeType = typeof(CompareShareStringNode);

        /// <summary>
        /// 比较黑板数据节点基类类型
        /// </summary>
        public static readonly Type BaseSetShareNodeType = typeof(BaseSetShareNode);

        /// <summary>
        /// 设置黑板bool数据节点类型信息
        /// </summary>
        public static readonly Type SetShareBoolNodeType = typeof(SetShareBoolNode);

        /// <summary>
        /// 设置黑板int数据节点类型信息
        /// </summary>
        public static readonly Type SetShareIntNodeType = typeof(SetShareIntNode);

        /// <summary>
        /// 设置黑板float数据节点类型信息
        /// </summary>
        public static readonly Type SetShareFloatNodeType = typeof(SetShareFloatNode);

        /// <summary>
        /// 设置黑板string数据节点类型信息
        /// </summary>
        public static readonly Type SetShareStringNodeType = typeof(SetShareStringNode);

        /// <summary>
        /// 打印Log节点类型信息
        /// </summary>
        public static readonly Type LogNodeType = typeof(LogNode);

        /// <summary>
        /// 组合节点基类类型
        /// </summary>
        public static readonly Type BaseCompositionNodeType = typeof(BaseCompositionNode);

        /// <summary>
        /// 装饰节点基类类型
        /// </summary>
        public static readonly Type BaseDecorationNodeType = typeof(BaseDecorationNode);

        /// <summary>
        /// 监听装饰节点类型信息
        /// </summary>
        public static readonly Type BaseObservingDecorationNodeType = typeof(BaseObservingDecorationNode);

        /// <summary>
        /// 行为节点基类类型
        /// </summary>
        public static readonly Type BaseActionNodeType = typeof(BaseActionNode);

        /// <summary>
        /// 条件节点基类类型
        /// </summary>
        public static readonly Type BaseConditionNodeType = typeof(BaseConditionNode);

        /// <summary>
        /// 并发节点类型信息
        /// </summary>
        public static readonly Type ParalNodeType = typeof(ParalNode);

        /// <summary>
        /// NodeView类型信息
        /// </summary>
        public static readonly Type NodeViewType = typeof(NodeView);

        /// <summary>
        /// RootNodeView类型信息
        /// </summary>
        public static readonly Type RootNodeViewType = typeof(RootNodeView);

        /// <summary>
        /// 黑板比较数据节点NodeView类型信息
        /// </summary>
        public static readonly Type BaseCompareShareNodeViewType = typeof(BaseCompareShareNodeView);

        /// <summary>
        /// 比较黑板Bool类型数据NodeView类型信息
        /// </summary>
        public static readonly Type CompareShareBoolNodeViewType = typeof(CompareShareBoolNodeView);

        /// <summary>
        /// 比较黑板Int类型数据NodeView类型信息
        /// </summary>
        public static readonly Type CompareShareIntNodeViewType = typeof(CompareShareIntNodeView);

        /// <summary>
        /// 比较黑板Float类型数据NodeView类型信息
        /// </summary>
        public static readonly Type CompareShareFloatNodeViewType = typeof(CompareShareFloatNodeView);

        /// <summary>
        /// 比较黑板String类型数据NodeView类型信息
        /// </summary>
        public static readonly Type CompareShareStringNodeViewType = typeof(CompareShareStringNodeView);

        /// <summary>
        /// 黑板设置数据节点NodeView类型信息
        /// </summary>
        public static readonly Type BaseSetShareNodeViewType = typeof(BaseSetShareNodeView);

        /// <summary>
        /// 设置黑板Bool类型数据NodeView类型信息
        /// </summary>
        public static readonly Type SetShareBoolNodeViewType = typeof(SetShareBoolNodeView);

        /// <summary>
        /// 设置黑板Int类型数据NodeView类型信息
        /// </summary>
        public static readonly Type SetShareIntNodeViewType = typeof(SetShareIntNodeView);

        /// <summary>
        /// 设置黑板Float类型数据NodeView类型信息
        /// </summary>
        public static readonly Type SetShareFloatNodeViewType = typeof(SetShareFloatNodeView);

        /// <summary>
        /// 设置黑板String类型数据NodeView类型信息
        /// </summary>
        public static readonly Type SetShareStringNodeViewType = typeof(SetShareStringNodeView);

        /// <summary>
        /// 组合节点NodeView类型信息
        /// </summary>
        public static readonly Type BaseCompositionNodeViewType = typeof(BaseCompositionNodeView);

        /// <summary>
        /// 装饰节点NodeView类型信息
        /// </summary>
        public static readonly Type BaseDecorationNodeViewType = typeof(BaseDecorationNodeView);
        
        /// <summary>
        /// 监听装饰节点NodeView类型信息
        /// </summary>
        public static readonly Type BaseObservingDecorationNodeViewType = typeof(BaseObservingDecorationNodeView);

        /// <summary>
        /// 行为节点NodeView类型信息
        /// </summary>
        public static readonly Type BaseActionNodeViewType = typeof(BaseActionNodeView);

        /// <summary>
        /// 打印Log节点NodeView类型信息
        /// </summary>
        public static readonly Type LogNodeViewType = typeof(LogNodeView);

        /// <summary>
        /// 条件节点NodeView类型信息
        /// </summary>
        public static readonly Type BaseConditionNodeViewType = typeof(BaseConditionNodeView);

        /// <summary>
        /// 并发节点NodeView类型信息
        /// </summary>
        public static readonly Type ParalNodeViewType = typeof(ParalNodeView);

        /// <summary>
        /// BehaviourTree的图数据类型新
        /// </summary>
        public static readonly Type BehaviourTreeGraphDataType = typeof(BehaviourTreeGraphData);

        /// <summary>
        /// 节点基类所在Assembly
        /// </summary>
        public static readonly Assembly BaseNodeAssembly = BaseNodeType.Assembly;

        /// <summary>
        /// float类型
        /// </summary>
        public static readonly Type FloatType = typeof(float);

        /// <summary>
        /// 根节点颜色
        /// </summary>
        public static readonly Color RootNodeColor = Color.green;

        /// <summary>
        /// 默认图保存文件夹路径
        /// </summary>
        public const string DefaultGraphSaveFolderPath = "Assets/Resources/BTGraph/BehaviourTreeGraphView";

        /// <summary>
        /// 默认图保存文件名
        /// </summary>
        public const string DefaultGraphSaveFileName = "DefaultGraph.asset";

        /// <summary>
        /// 默认输入节点名
        /// </summary>
        public const string DefaultInputPortName = "In";

        /// <summary>
        /// 默认输出节点名
        /// </summary>
        public const string DefaultOutputPortName = "Out";

        /// <summary>
        /// 节点EntryPoint属性名
        /// </summary>
        public const string NodeEntryPointPropertyName = "EntryPoint";

        /// <summary>
        /// 节点Des属性名
        /// </summary>
        public const string NodeDesPropertyName = "Des";

        /// <summary>
        /// 分隔符颜色
        /// </summary>
        public static readonly Color DividerColor = new Color(0.137f, 0.137f, 0.137f, 0.804f);

        /// <summary>
        /// 节点容器背景颜色
        /// </summary>
        public static readonly Color NodeContainerBGColor = new Color(0.247f, 0.247f, 0.247f, 0.804f);

        /// <summary>
        /// 节点默认坐标X
        /// </summary>
        public const float NodeDefaultPosX = 100f;

        /// <summary>
        /// 节点默认坐标Y
        /// </summary>
        public const float NodeDefaultPosY = 100f;

        /// <summary>
        /// 节点默认坐标
        /// </summary>
        public static readonly Vector2 NodeDefaultPos = new Vector2(NodeDefaultPosX, NodeDefaultPosY);

        /// <summary>
        /// 节点默认Rect
        /// </summary>
        public static readonly Rect NodeDefaultRect = new Rect(NodeDefaultPosX, NodeDefaultPosY, 100, 150);

        /// <summary>
        /// 常规Label字体大小
        /// </summary>
        public const float NormalLabelFontSize = 16f;

        /// <summary>
        /// 节点一行显示高度
        /// </summary>
        public const float NodeOneLineHeight = 25f;
    }
}