﻿/*
 * Description:     BaseObservingDecorationNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/12/06
 */

using System;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// BaseConditionDecorationNode.cs
    /// 可打断定时检查条件修饰节点基类
    /// </summary>
    [Serializable]
    public abstract class BaseConditionDecorationNode : BaseObservingDecorationNode
    {
        /// <summary>
        /// 条件检查时间间隔(秒)
        /// </summary>
        [Header("条件检查时间间隔(秒)")]
        public float CheckInterval = 0f;

        /// <summary>
        /// 条件检查时间随机参考值(-0.5* -- 0.5*)
        /// </summary>
        [Header("条件检查时间随机参考值")]
        public float CheckVariance = 0f;

        /// <summary>
        /// 开始条件监听
        /// </summary>
        protected override void StartObserving()
        {
            mOwnerTreeData.Clock.AddTimer(CheckInterval, CheckVariance, -1, <PERSON>luate);
        }

        /// <summary>
        /// 停止条件监听
        /// </summary>
        protected override void StopObserving()
        {
            mOwnerTreeData.Clock.RemoveTimer(Evaluate);
        }

        /// <summary>
        /// 条件是否满足
        /// </summary>
        /// <returns></returns>
        protected override bool IsConditionMet()
        {
            return ConditionCheck();
        }

        /// <summary>
        /// 条件检查(子类重写)
        /// </summary>
        /// <returns></returns>
        protected abstract bool ConditionCheck();
    }
}
