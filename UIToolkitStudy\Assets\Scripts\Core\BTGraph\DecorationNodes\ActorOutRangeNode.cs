﻿/*
 * Description:     ActorOutRangeNode.cs
 * Author:          TonyTang
 * Create Date:     2024/12/20
 */

using System;
using System.Collections.Generic;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// ActorOutRangeNode.cs
    /// 对象超出范围内条件装饰节点
    /// </summary>
    public class ActorOutRangeNode : BaseConditionDecorationNode
    {
        /// <summary>
        /// 角色判定类型
        /// </summary>
        [Header("角色判定类型")]
        public ActorType ActType = ActorType.MONSTER;

        /// <summary>
        /// 超出范围
        /// </summary>
        [Header("超出范围")]
        public float OutRange = 10f;

        /// <summary>
        /// 条件检查
        /// </summary>
        protected override bool ConditionCheck()
        {
            var bindUID = mOwnerTreeData.OwnerGraphData.OwnerBT.BindUID;
            var bindActor = ActorManager.Singleton.GetActorByUID(bindUID);
            if (bindActor == null)
            {
                Debug.LogError($"找不到UID:{bindUID}对象，判定角色类型:{ActType}在范围内失败！");
                return false;
            }

            var bindActorPosition = bindActor.GetPosition();
            var actorList = ActorManager.Singleton.GetAllActorsByType(ActType);
            foreach (var targetActor in actorList)
            {
                var targetActorPosition = targetActor.GetPosition();
                var distance = Vector3.Distance(bindActorPosition, targetActorPosition);
                if (distance >= OutRange)
                {
                    return true;
                }
            }
            return false;
        }
    }
}
