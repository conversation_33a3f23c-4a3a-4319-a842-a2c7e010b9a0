﻿/*
 * Description:     CommonGraphView.cs
 * Author:          TonyTang
 * Create Date:     2023/09/08
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.UIElements;
using Edge = UnityEditor.Experimental.GraphView.Edge;

namespace TCommonGraph
{
    /// <summary>
    /// EdgeView.cs
    /// 显示边基类
    /// </summary>
    public class EdgeView : Edge
    {
        /// <summary>
        /// 所属图数据
        /// </summary>
        public BehaviourTreeGraphData OwnerGraphData
        {
            get;
            private set;
        }

        /// <summary>
        /// 边数据
        /// </summary>
        public EdgeData EdgeData
        {
            get;
            private set;
        }
        
        /// <summary>
        /// 边索引
        /// </summary>
        public int EdgeIndex
        {
            get;
            private set;
        }

        /// <summary>
        /// 边索引Label
        /// </summary>
        private Label mEdgeIndexLabel;

        public EdgeView() : base()
        {
            mEdgeIndexLabel = new Label();
            mEdgeIndexLabel.style.color = Color.red;
            mEdgeIndexLabel.style.unityTextAlign = TextAnchor.MiddleCenter;
            mEdgeIndexLabel.style.fontSize = 20f;
            this.Add(mEdgeIndexLabel);
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="ownerGraphData"></param>
        /// <param name="edgeData"></param>
        public void Init(BehaviourTreeGraphData ownerGraphData, EdgeData edgeData)
        {
            OwnerGraphData = ownerGraphData;
            EdgeData = edgeData;
            name = edgeData.GUID;
            // viewDataKey是GraphView.GetEdgeByGUID的数据来源
            viewDataKey = EdgeData.GUID;
        }

        /// <summary>
        /// 更新显示边线状态颜色
        /// </summary>
        public void UpdateEdgeControlStateColor()
        {
            var edgeInputNode = OwnerGraphData.GetNodeByGUID(EdgeData.InputNodeGUID);
            var edgeInputColor = BTGraphUtilitiesEditor.GetEdgeColorByNodeState(edgeInputNode.NodeState);
            var edgeOutputColor = BTGraphUtilitiesEditor.GetEdgeColorByNodeState(edgeInputNode.NodeState);
            edgeControl.inputColor = edgeInputColor;
            edgeControl.outputColor = edgeOutputColor;
        }

        /// <summary>
        /// 更新边索引
        /// </summary>
        /// <param name="edgeIndex"></param>
        public void UpdateEdgeIndex(int edgeIndex)
        {
            EdgeIndex = edgeIndex;
            mEdgeIndexLabel.text = EdgeIndex.ToString();
        }

        /// <summary>
        /// 更新边控制
        /// </summary>
        /// <returns></returns>
        public override bool UpdateEdgeControl()
        {
            if (!base.UpdateEdgeControl())
            {
                return false;
            }
            UpdateEdgeIndexLabelPosition();
            return true;
        }

        /// <summary>
        /// 更新边索引文本位置
        /// </summary>
        private void UpdateEdgeIndexLabelPosition()
        {
            Vector2 midPoint = GetMidPoint();
            mEdgeIndexLabel.style.left = midPoint.x - (mEdgeIndexLabel.resolvedStyle.width / 2);
            mEdgeIndexLabel.style.top = midPoint.y - (mEdgeIndexLabel.resolvedStyle.height / 2);
        }

        /// <summary>
        /// 获取中心点位置
        /// </summary>
        /// <returns></returns>
        private Vector2 GetMidPoint()
        {
            Vector2 startPoint = edgeControl.controlPoints[0];
            Vector2 endPoint = edgeControl.controlPoints[edgeControl.controlPoints.Length - 1];
            return (startPoint + endPoint) / 2;
        }
    }
}