﻿/*
 * Description:             SetShareFloatNode.cs
 * Author:                  TONYTANG
 * Create Date:             2023/10/04
 */

using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// SetShareFloatNode.cs
    /// 设置黑板Float数据行为节点
    /// </summary>
    public class SetShareFloatNode : BaseSetShareNode
    {
        /// <summary>
        /// 设置数据
        /// </summary>
        [Header("设置数据")]
        public float TargetValue;

        /// <summary>
        /// 执行开始
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            mOwnerTreeData.Blackboard.UpdateData<float>(VariableName, TargetValue);
            Stop(true);
        }
    }
}