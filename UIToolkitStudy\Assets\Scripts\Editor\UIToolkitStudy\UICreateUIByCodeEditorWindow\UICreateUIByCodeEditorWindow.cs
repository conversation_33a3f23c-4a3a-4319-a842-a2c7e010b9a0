﻿using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;
using UnityEditor.UIElements;

/// <summary>
/// 代码创建UI的EditorWindow
/// </summary>
public class UICreateUIByCodeEditorWindow : EditorWindow
{
    [MenuItem("Window/UI Toolkit/UIToolkitStudy/UICreateUIByCodeEditorWindow")]
    public static void ShowUICodeStyleEditorWindow()
    {
        UICreateUIByCodeEditorWindow wnd = GetWindow<UICreateUIByCodeEditorWindow>();
        wnd.titleContent = new GUIContent("UICreateUIByCodeEditorWindow");
    }

    /// <summary>
    /// UIToolkit的rootVisualElement绘制方法
    /// </summary>
    public void CreateGUI()
    {
        CreateUIByCode();
    }

    /// <summary>
    /// 通过代码创建UI
    /// </summary>
    private void CreateUIByCode()
    {
        // 创建一个ui容器，用于管理我们新增的ui组件
        VisualElement uiContainer = new VisualElement();
        // 添加一个标题作为ui容器标题组件显示
        Label uiTitleLable = new Label("代码创建UI容器");
        uiContainer.Add(uiTitleLable);

        // 添加按钮组件
        Button btn1 = new Button();
        btn1.text = "代码创建按钮1";
        uiContainer.Add(btn1);

        // 必须将需要显示的组件添加到根节点才能显示
        // 将ui容器添加到根节点作为需要显示的UIElement
        rootVisualElement.Add(uiContainer);
    }
}