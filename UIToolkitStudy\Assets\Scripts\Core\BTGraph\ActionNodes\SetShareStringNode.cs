﻿/*
 * Description:             SetShareStringNode.cs
 * Author:                  TONYTANG
 * Create Date:             2023/10/04
 */

using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// SetShareStringNode.cs
    /// 设置黑板字符串数据行为节点
    /// </summary>
    public class SetShareStringNode : BaseSetShareNode
    {
        /// <summary>
        /// 设置数据
        /// </summary>
        [Header("设置数据")]
        public string TargetValue;

        /// <summary>
        /// 执行开始
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            mOwnerTreeData.Blackboard.UpdateData<string>(VariableName, TargetValue);
            Stop(true);
        }
    }
}