﻿using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;
using UnityEditor.UIElements;

/// <summary>
/// 位置和排版EditorWindow
/// </summary>
public class UIPositionAndLayoutEditorWindow : EditorWindow
{
    /// <summary>
    /// 竖向相对位置根组件
    /// </summary>
    private VisualElement mRootVerticalRelativePosContainer;

    [MenuItem("Window/UI Toolkit/UIToolkitStudy/UIPositionAndLayoutEditorWindow")]
    public static void ShowUIPositionAndLayoutEditorWindow()
    {
        UIPositionAndLayoutEditorWindow wnd = GetWindow<UIPositionAndLayoutEditorWindow>();
        wnd.titleContent = new GUIContent("UIPositionAndLayoutEditorWindow");
    }

    public void CreateGUI()
    {
        CreatePositionAndLayoutUI();
    }

    /// <summary>
    /// 创建位置和排版UI
    /// </summary>
    private void CreatePositionAndLayoutUI()
    {
        CreateRootRelativePosVerticalContainer();
        CreateHorizontalRelativePosContainer();
        CreateHorzintalLayoutContainer();
        CreateVerticalAbsolutePosContainer();
    }

    /// <summary>
    /// 创建竖向相对位置根组件
    /// </summary>
    private void CreateRootRelativePosVerticalContainer()
    {
        // 竖向相对位置排版容器
        mRootVerticalRelativePosContainer = new VisualElement();
        // 设置竖向排版
        mRootVerticalRelativePosContainer.style.flexDirection = FlexDirection.Column;
        // 设置相对位置
        mRootVerticalRelativePosContainer.style.position = Position.Relative;
        // 指定竖向容器Style
        mRootVerticalRelativePosContainer.AddToClassList("unity-box");
        // 设置竖向容器大小位置
        mRootVerticalRelativePosContainer.style.marginLeft = 10;
        mRootVerticalRelativePosContainer.style.marginRight = 10;
        mRootVerticalRelativePosContainer.style.marginTop = 10;
        mRootVerticalRelativePosContainer.style.marginBottom = 10;
        // 设置竖向容器自适应大小
        mRootVerticalRelativePosContainer.style.flexGrow = 1;

        // 添加竖向排版标签
        Label verticalLabel = new Label();
        verticalLabel.text = "竖向相对位置排版容器标题";
        // 设置文本居中显示
        verticalLabel.style.alignSelf = Align.Center;
        mRootVerticalRelativePosContainer.Add(verticalLabel);
        rootVisualElement.Add(mRootVerticalRelativePosContainer);
    }

    /// <summary>
    /// 创建横向相对位置容器
    /// </summary>
    private void CreateHorizontalRelativePosContainer()
    {
        // 横向相对位置容器
        var horizontalRelativePosContainer = new VisualElement();
        // 设置横向排版
        horizontalRelativePosContainer.style.flexDirection = FlexDirection.Row;
        // 设置相对位置
        horizontalRelativePosContainer.style.position = Position.Relative;
        // 指定横向相对位置容器Style
        horizontalRelativePosContainer.AddToClassList("unity-box");
        // 添加横向相对位置标签
        Label horizontalRelativePosLabel = new Label();
        horizontalRelativePosLabel.text = "横向相对位置排版容器标题";
        // 设置文本居中显示
        horizontalRelativePosLabel.style.alignSelf = Align.Center;
        horizontalRelativePosContainer.Add(horizontalRelativePosLabel);

        // 设置横向容器多个按钮
        Button horizontalButton1 = new Button();
        horizontalButton1.text = "横向按钮1";
        horizontalButton1.style.marginLeft = 25;
        Button horizontalButton2 = new Button();
        horizontalButton2.text = "横向按钮2";
        horizontalButton2.style.marginLeft = 50f;
        horizontalRelativePosContainer.Add(horizontalButton1);
        horizontalRelativePosContainer.Add(horizontalButton2);

        mRootVerticalRelativePosContainer.Add(horizontalRelativePosContainer);
    }

    /// <summary>
    /// 创建横向排版容器
    /// </summary>
    private void CreateHorzintalLayoutContainer()
    {
        // 横向排版容器
        var horizontalLayoutContainer = new VisualElement();
        // 设置横向排版
        horizontalLayoutContainer.style.flexDirection = FlexDirection.Row;
        // 设置内容超出后排版Style
        horizontalLayoutContainer.style.flexWrap = Wrap.Wrap;
        // 指定横向排版容器Style
        horizontalLayoutContainer.AddToClassList("unity-box");
        // 添加横向排版标签
        Label horizontalLayoutLabel = new Label();
        horizontalLayoutLabel.text = "横向排版容器标题";
        // 设置文本居中显示
        horizontalLayoutLabel.style.alignSelf = Align.Center;
        horizontalLayoutContainer.Add(horizontalLayoutLabel);

        // 设置横向容器多个按钮
        Button horizontalButton1 = new Button();
        horizontalButton1.text = "横向按钮1";
        // 设置自动扩展系数
        horizontalButton1.style.flexGrow = 1;
        Button horizontalButton2 = new Button();
        horizontalButton2.text = "横向按钮2";
        // 设置自动扩展系数
        horizontalButton2.style.flexGrow = 2;
        // 设置偏移间隔
        horizontalButton2.style.marginLeft = 10;
        Button horizontalButton3 = new Button();
        horizontalButton3.text = "横向按钮3";
        // 设置自动扩展系数
        horizontalButton3.style.flexGrow = 3;
        // 设置偏移间隔
        horizontalButton3.style.marginLeft = 10;
        Button horizontalButton4 = new Button();
        horizontalButton4.text = "横向按钮4";
        // 设置自动扩展系数
        horizontalButton4.style.flexGrow = 4;
        // 设置偏移间隔
        horizontalButton4.style.marginLeft = 10;
        horizontalLayoutContainer.Add(horizontalButton1);
        horizontalLayoutContainer.Add(horizontalButton2);
        horizontalLayoutContainer.Add(horizontalButton3);
        horizontalLayoutContainer.Add(horizontalButton4);

        mRootVerticalRelativePosContainer.Add(horizontalLayoutContainer);
    }

    /// <summary>
    /// 创建竖向绝对位置容器
    /// </summary>
    private void CreateVerticalAbsolutePosContainer()
    {
        // 竖向绝对坐标容器
        var verticalAbsolutePosContainer = new VisualElement();
        // 设置竖向排版
        verticalAbsolutePosContainer.style.flexDirection = FlexDirection.Column;
        // 设置绝对坐标
        verticalAbsolutePosContainer.style.position = Position.Absolute;
        // 指定竖向绝对位置容器Style
        verticalAbsolutePosContainer.AddToClassList("unity-box");
        // 设置竖向容器大小位置
        verticalAbsolutePosContainer.style.left = 100;
        verticalAbsolutePosContainer.style.right = 100;
        verticalAbsolutePosContainer.style.top = 100;
        verticalAbsolutePosContainer.style.bottom = 100;
        // 设置竖向绝对位置容器自适应
        verticalAbsolutePosContainer.style.flexGrow = 1;

        // 添加竖向排版标签
        Label verticalAbsolutePosLabel = new Label();
        verticalAbsolutePosLabel.text = "竖向绝对位置标题";
        // 设置文本居中显示
        verticalAbsolutePosLabel.style.alignSelf = Align.Center;
        verticalAbsolutePosContainer.Add(verticalAbsolutePosLabel);

        // 设置竖向容器多个按钮
        Button verticalButton1 = new Button();
        verticalButton1.text = "竖向按钮1";
        // 设置按钮高度和位置
        verticalButton1.style.height = 20;
        verticalButton1.style.marginLeft = 20;
        verticalButton1.style.marginRight = 20;
        Button verticalButton2 = new Button();
        verticalButton2.text = "竖向按钮2";
        // 设置按钮高度
        verticalButton2.style.height = 20;
        verticalButton2.style.marginLeft = 20;
        verticalButton2.style.marginRight = 20;
        verticalAbsolutePosContainer.Add(verticalButton1);
        verticalAbsolutePosContainer.Add(verticalButton2);

        mRootVerticalRelativePosContainer.Add(verticalAbsolutePosContainer);
    }
}