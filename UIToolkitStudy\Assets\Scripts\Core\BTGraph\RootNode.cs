﻿/*
 * Description:     RootNode.cs
 * Author:          TonyTang
 * Create Date:     2023/12/05
 */

using NPBehave;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// RootNode.cs
    /// 根节点类型
    /// </summary>
    public class RootNode : BaseDecorationNode
    {
        /// <summary>
        /// 是否是根节点
        /// </summary>
        public override bool EntryPoint
        {
            get
            {
                return true;
            }
        }

        /// <summary>
        /// 是否执行一次(反之循环执行)
        /// </summary>
        [Header("是否执行一次(反之循环执行)")]
        public bool IsRunningOnce;

        /// <summary>
        /// 执行开始运行流程
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            DecorateNode.Start();
        }

        /// <summary>
        /// 执行打断流程
        /// </summary>
        protected override void DoAbort()
        {
            base.DoAbort();
            if(!DecorateNode.IsRunning)
            {
                DecorateNode.Abort();
            }
            else
            {
                mOwnerTreeData.Clock.RemoveTimer(Start);
            }
        }

        /// <summary>
        /// 响应子节点运行完毕
        /// </summary>
        /// <param name="child"></param>
        /// <param name="success"></param>
        protected override void DoChildStop(BaseNode child, bool success)
        {
            base.DoChildStop(child, success);
            // 根节点运行完一遍后
            if (!IsRunningOnce)
            {
                // 重置所有节点状态，避免反复运行时，可视化查看到之前的状态
                ResetAllNodeState();
                Clock.AddTimer(0, 0, Start);
            }
            else
            {
                Stop(success);
            }
        }

        /// <summary>
        /// 重置所有节点状态
        /// </summary>
        protected void ResetAllNodeState()
        {
            mOwnerTreeData?.OwnerGraphData.ResetAllNodeRunTimeData();
        }
    }
}
