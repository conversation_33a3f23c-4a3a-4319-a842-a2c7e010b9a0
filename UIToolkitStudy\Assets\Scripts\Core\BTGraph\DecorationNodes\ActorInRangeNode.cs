﻿/*
 * Description:     ActorInRangeNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/12/08
 */

using System;
using System.Collections.Generic;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// ActorInRangeNode.cs
    /// 对象在范围内条件装饰节点
    /// </summary>
    public class ActorInRangeNode : BaseConditionDecorationNode
    {
        /// <summary>
        /// 角色判定类型
        /// </summary>
        [Header("角色判定类型")]
        public ActorType ActType = ActorType.MONSTER;

        /// <summary>
        /// 范围
        /// </summary>
        [Header("范围")]
        public float Range = 10f;

        /// <summary>
        /// 条件检查
        /// </summary>
        protected override bool ConditionCheck()
        {
            var bindUID = mOwnerTreeData.OwnerGraphData.OwnerBT.BindUID;
            var bindActor = ActorManager.Singleton.GetActorByUID(bindUID);
            if(bindActor == null)
            {
                Debug.LogError($"找不到UID:{bindUID}对象，判定角色类型:{ActType}在范围内失败！");
                return false;
            }

            var bindActorPosition = bindActor.GetPosition();
            var actorList = ActorManager.Singleton.GetAllActorsByType(ActType);
            foreach(var targetActor in actorList)
            {
                var targetActorPosition = targetActor.GetPosition();
                var distance = Vector3.Distance(bindActorPosition, targetActorPosition);
                if(distance <= Range)
                {
                    return true;
                }
            }
            return false;
        }
    }
}
