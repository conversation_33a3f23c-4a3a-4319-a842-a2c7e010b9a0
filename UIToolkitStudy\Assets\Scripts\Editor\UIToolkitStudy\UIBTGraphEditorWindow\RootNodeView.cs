﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace TCommonGraph
{
    /// <summary>
    /// RootNodeView.cs
    /// 根节点NodeView类
    /// </summary>
    public class RootNodeView : BaseDecorationNodeView
    {
        /// <summary>
        /// 生成节点所有Input Port
        /// </summary>
        protected override void GenerateAllInputPort()
        {
            return;
        }
    }
}
