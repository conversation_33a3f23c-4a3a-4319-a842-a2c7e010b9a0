﻿/*
 * Description:     NodeView.cs
 * Author:          TonyTang
 * Create Date:     2023/07/03
 */

using System;
using System.Collections.Generic;
using UnityEditor.Experimental.GraphView;
using UnityEngine;
using UnityEngine.UIElements;

namespace TCommonGraph
{
    /// <summary>
    /// NodeView.cs
    /// 节点显示抽象
    /// </summary>
    public class NodeView : Node
    {
        /// <summary>
        /// 所属图数据
        /// </summary>
        public BehaviourTreeGraphData OwnerGraphData
        {
            get;
            private set;
        }

        /// <summary>
        /// 节点数据
        /// </summary>
        public BaseNode NodeData
        {
            get;
            private set;
        }

        /// <summary>
        /// 入口Port连线数量类型
        /// </summary>
        public virtual Port.Capacity InPortCapacity
        {
            get
            {
                return Port.Capacity.Single;
            }
        }

        /// <summary>
        /// 出口Port连线数量类型
        /// </summary>
        public virtual Port.Capacity OutPortCapacity
        {
            get
            {
                return Port.Capacity.Single;
            }
        }

        /// <summary>
        /// 节点朝向
        /// </summary>
        protected Orientation mOrientation;

        /// <summary>
        /// 节点选择委托
        /// </summary>
        protected Action<NodeView> mNodeSelectedDelegate;

        /// <summary>
        /// 所有输入端口
        /// </summary>
        protected List<Port> mAllInputPorts;

        /// <summary>
        /// 所有输出端口
        /// </summary>
        protected List<Port> mAllOutputPorts;

        /// <summary>
        /// 无参构造
        /// </summary>
        public NodeView()
        {
            mAllInputPorts = new List<Port>();
            mAllOutputPorts = new List<Port>();
        }

        /// <summary>
        /// 带参构造
        /// </summary>
        /// <param name="ownerGraphData">所属图数据</param>
        /// <param name="nodeData">节点数据</param>
        /// <param name="nodeSelectedCB">节点选择委托</param>
        /// <param name="nodeName">节点名(默认不传为BaseNode的类型名)</param>
        /// <param name="orientation">节点方向</param>
        public NodeView(BehaviourTreeGraphData ownerGraphData, BaseNode nodeData, Action<NodeView> nodeSelectedCB = null, string nodeName = null, Orientation orientation = Orientation.Horizontal)
        {
            Init(ownerGraphData, nodeData, nodeSelectedCB, nodeName, orientation);
        }

        /// <summary>
        /// 根据Node数据初始化
        /// </summary>
        /// <param name="ownerGraphData">所属图数据</param>
        /// <param name="nodeData">节点数据</param>
        /// <param name="nodeSelectedCB">节点选择委托</param>
        /// <param name="nodeName">节点名(默认不传为BaseNode的类型名)</param>
        /// <param name="orientation">节点方向</param>
        public void Init(BehaviourTreeGraphData ownerGraphData, BaseNode node, Action<NodeView> nodeSelectedCB = null, string nodeName = null, Orientation orientation = Orientation.Horizontal)
        {
            OwnerGraphData = ownerGraphData;
            name = node.GUID;
            // viewDataKey是GraphView.GetNodeByGUID的数据来源
            viewDataKey = node.GUID;
            NodeData = node;
            mNodeSelectedDelegate += nodeSelectedCB;
            mOrientation = orientation;
            title = nodeName == null ? node.GetType().Name : nodeName;
            titleContainer.style.backgroundColor = BTGraphUtilitiesEditor.GetBackgroundColorByNodeData(NodeData);
            var titleLabel = titleContainer.Q<Label>("title-label");
            titleLabel.style.color = Color.black;
            titleLabel.style.fontSize = BTGraphConstEditor.NormalLabelFontSize;
            CreateNodeUI();
            GenerateAllPort();
            RefreshExpandedState();
            RefreshPorts();
        }

        /// <summary>
        /// 创建自定义输入Port
        /// </summary>
        /// <param name="portName"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public Port InstantiateCustomInputPort(string portName, System.Type type)
        {
            var port = InstantiateCustomPort(portName, Direction.Input, InPortCapacity, type);
            inputContainer.Add(port);
            mAllInputPorts.Add(port);
            return port;
        }

        /// <summary>
        /// 创建自定义输出Port
        /// </summary>
        /// <param name="portName"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public Port InstantiateCustomOutputPort(string portName, System.Type type)
        {
            var port = InstantiateCustomPort(portName, Direction.Output, OutPortCapacity, type);
            outputContainer.Add(port);
            mAllOutputPorts.Add(port);
            return port;
        }

        /// <summary>
        /// 获取所有Input端口
        /// </summary>
        /// <returns></returns>
        public List<Port> GetAllInputPorts()
        {
            return mAllInputPorts;
        }

        /// <summary>
        /// 获取所有Output端口
        /// </summary>
        /// <returns></returns>
        public List<Port> GetAllOutputPorts()
        {
            return mAllOutputPorts;
        }

        /// <summary>
        /// 创建自定义Port
        /// Note:
        /// 不含添加到容器
        /// </summary>
        /// <param name="portName"></param>
        /// <param name="direction"></param>
        /// <param name="capacity"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        protected virtual Port InstantiateCustomPort(string portName, Direction direction, Port.Capacity capacity, System.Type type)
        {
            var port = InstantiatePort(mOrientation, direction, capacity, type);
            port.name = portName;
            port.portName = portName;
            return port;
        }

        /// <summary>
        /// 实例化Port(自定义Port创建接口用于支持创建自定义EdgeView)
        /// </summary>
        /// <param name="orientation"></param>
        /// <param name="direction"></param>
        /// <param name="capacity"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public override Port InstantiatePort(Orientation orientation, Direction direction, Port.Capacity capacity, Type type)
        {
            return Port.Create<EdgeView>(orientation, direction, capacity, type);
        }

        /// <summary>
        /// 创建节点UI
        /// </summary>
        protected void CreateNodeUI()
        {
            CreateNodeContainerUI();
            CreateNodeGUIDUI();
            CreateNodeStateUI();
            CreateCustomUI();
            CreateNodeDesUI();
        }

        /// <summary>
        /// 创建节点UI
        /// </summary>
        protected void CreateNodeContainerUI()
        {
            var nodeVerticalUIContainer = UIToolkitUtilities.CreateVerticalContainer(BTGraphElementNames.NodeUIVerticalContainerName,
                                                                                       1, "unity-box", 0, 0, 0, 0,
                                                                                            BTGraphConstEditor.NodeContainerBGColor);
            var nodeContentElement = this.Q<VisualElement>("contents");
            nodeContentElement.Insert(0, nodeVerticalUIContainer);
        }

        /// <summary>
        /// 创建节点GUID UI
        /// </summary>
        protected void CreateNodeGUIDUI()
        {
            var nodeVerticalUIContainer = this.Q<VisualElement>(BTGraphElementNames.NodeUIVerticalContainerName);
            var nodeGUIDHorizontalUIContainer = UIToolkitUtilities.CreateHorizontalContainer(BTGraphElementNames.NodeGUIDUIHorizontalContainerName);

            var nodeGUIDDivider = UIToolkitUtilities.CreateHorizontalDivider(BTGraphConstEditor.DividerColor);
            nodeVerticalUIContainer.Add(nodeGUIDDivider);
            nodeVerticalUIContainer.Add(nodeGUIDHorizontalUIContainer);

            var nodeGuidTitleLabel = new Label();
            nodeGuidTitleLabel.text = "GUID:";
            nodeGuidTitleLabel.style.height = BTGraphConstEditor.NodeOneLineHeight;
            nodeGuidTitleLabel.style.fontSize = BTGraphConstEditor.NormalLabelFontSize;
            nodeGuidTitleLabel.style.unityTextAlign = TextAnchor.MiddleCenter;
            nodeGuidTitleLabel.style.alignSelf = Align.Center;
            nodeGUIDHorizontalUIContainer.Add(nodeGuidTitleLabel);

            var nodeGuidLabel = new TextField();
            nodeGuidLabel.isReadOnly = true;
            nodeGuidLabel.value = NodeData.GUID;
            nodeGuidLabel.style.height = BTGraphConstEditor.NodeOneLineHeight;
            nodeGuidLabel.style.fontSize = BTGraphConstEditor.NormalLabelFontSize;
            nodeGuidLabel.style.unityTextAlign = TextAnchor.MiddleCenter;
            nodeGuidLabel.style.alignSelf = Align.Center;
            //nodeGuidLabel.style.color = Color.white;
            nodeGUIDHorizontalUIContainer.Add(nodeGuidLabel);
        }

        /// <summary>
        /// 创建节点状态UI
        /// </summary>
        protected void CreateNodeStateUI()
        {
            var nodeVerticalUIContainer = this.Q<VisualElement>(BTGraphElementNames.NodeUIVerticalContainerName);
            var nodeStateHorizontalUIContainer = UIToolkitUtilities.CreateHorizontalContainer(BTGraphElementNames.NodeStateUIHorizontalContainerName);

            var nodeStateDivider = UIToolkitUtilities.CreateHorizontalDivider(BTGraphConstEditor.DividerColor);
            nodeVerticalUIContainer.Add(nodeStateDivider);
            nodeVerticalUIContainer.Add(nodeStateHorizontalUIContainer);

            var nodeStateTitleLabel = new Label();
            nodeStateTitleLabel.style.width = 40f;
            nodeStateTitleLabel.style.height = BTGraphConstEditor.NodeOneLineHeight;
            nodeStateTitleLabel.text = "状态:";
            nodeStateTitleLabel.style.fontSize = BTGraphConstEditor.NormalLabelFontSize;
            nodeStateTitleLabel.style.color = Color.black;
            nodeStateTitleLabel.style.unityTextAlign = TextAnchor.MiddleCenter;
            nodeStateTitleLabel.style.alignSelf = Align.Center;
            nodeStateHorizontalUIContainer.Add(nodeStateTitleLabel);

            var nodeStateLabel = new Label();
            nodeStateLabel.name = BTGraphElementNames.NodeStateLabelName;
            nodeStateLabel.style.width = 40f;
            nodeStateLabel.style.unityTextAlign = TextAnchor.MiddleCenter;
            nodeStateLabel.style.alignSelf = Align.Center;
            nodeStateLabel.style.height = BTGraphConstEditor.NodeOneLineHeight;
            nodeStateLabel.style.fontSize = BTGraphConstEditor.NormalLabelFontSize;
            nodeStateLabel.style.color = Color.black;
            nodeStateLabel.style.flexGrow = 1;
            nodeStateHorizontalUIContainer.Add(nodeStateLabel);
            UpdateNodeStateBackgroundColor();
            UpdateNodeStateLabel();
        }

        /// <summary>
        /// 创建节点自定义UI
        /// </summary>
        protected virtual void CreateCustomUI()
        {

        }

        /// <summary>
        /// 更新节点状态背景颜色
        /// </summary>
        public void UpdateNodeStateBackgroundColor()
        {
            var nodeStateHorizontalUIContainer = this.Q<VisualElement>(BTGraphElementNames.NodeStateUIHorizontalContainerName);
            if(nodeStateHorizontalUIContainer != null)
            {
                nodeStateHorizontalUIContainer.style.backgroundColor = BTGraphUtilitiesEditor.GetColorByNodeState(NodeData.NodeState);
            }
        }

        /// <summary>
        /// 更新节点状态Label
        /// </summary>
        public void UpdateNodeStateLabel()
        {
            var nodeStateLabel = this.Q<Label>(BTGraphElementNames.NodeStateLabelName);
            if (nodeStateLabel != null)
            {
                var stateDes = NodeData.NodeState.ToString();
                if(NodeData.IsEnteredAbort)
                {
                    stateDes = $"{stateDes}(打断)";
                }
                nodeStateLabel.text = stateDes;
            }
        }

        /// <summary>
        /// 创建节点描述UI
        /// </summary>
        protected void CreateNodeDesUI()
        {
            var nodeVerticalUIContainer = this.Q<VisualElement>(BTGraphElementNames.NodeUIVerticalContainerName);
            var nodeDesHorizontalUIContainer = UIToolkitUtilities.CreateHorizontalContainer(BTGraphElementNames.NodeDesUIHorizontalContainerName);

            var nodeDesDivider = UIToolkitUtilities.CreateHorizontalDivider(BTGraphConstEditor.DividerColor);
            nodeVerticalUIContainer.Add(nodeDesDivider);
            nodeVerticalUIContainer.Add(nodeDesHorizontalUIContainer);

            UIToolkitUtilities.CreateBindSOTextField(nodeDesHorizontalUIContainer, NodeData,
                                                        BTGraphConstEditor.NodeDesPropertyName, 1,
                                                        BTGraphConstEditor.NodeOneLineHeight,
                                                        BTGraphConstEditor.NormalLabelFontSize);
        }

        /// <summary>
        /// 创建所有Port
        /// </summary>
        protected void GenerateAllPort()
        {
            GenerateAllInputPort();
            GenerateAllOutputPort();
        }

        /// <summary>
        /// 生成节点所有Input Port
        /// </summary>
        protected virtual void GenerateAllInputPort()
        {
            InstantiateCustomInputPort(BTGraphConstEditor.DefaultInputPortName, BTGraphConstEditor.FloatType);
        }

        /// <summary>
        /// 生成节点所有Output Port
        /// </summary>
        protected virtual void GenerateAllOutputPort()
        {
            InstantiateCustomOutputPort(BTGraphConstEditor.DefaultOutputPortName, BTGraphConstEditor.FloatType);
        }

        /// <summary>
        /// 响应节点选择
        /// </summary>
        public override void OnSelected()
        {
            base.OnSelected();
            Debug.Log($"节点GUID:{NodeData.GUID},节点类型:{NodeData.NodeType}，节点名:{NodeData.GetType().Name}被选中！");
            if (mNodeSelectedDelegate != null)
            {
                mNodeSelectedDelegate(this);
            }
        }

        /// <summary>
        /// 创建节点自定义GUID Inspector显示
        /// Note:
        /// 子类重写实现自定义节点Inspector创建显示
        /// </summary>
        /// <param name="bindPropertyDataMap">属性绑定数据Map</param>
        /// <returns></returns>
        public virtual VisualElement CreateInspectorGUIElement(Dictionary<string, PropertyBindData> bindPropertyDataMap)
        {
            return UIToolkitUtilities.CreateBindSOInspector(this.NodeData, bindPropertyDataMap);
        }
    }
}