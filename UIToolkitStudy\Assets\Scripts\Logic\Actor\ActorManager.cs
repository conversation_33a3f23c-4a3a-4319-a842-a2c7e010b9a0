﻿/*
 * Description:     ActorManager.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/12/06
 */

using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// ActorManager.cs
/// 角色单例管理类
/// </summary>
public class ActorManager : SingletonTemplate<ActorManager>
{
    /// <summary>
    /// 下一个角色UID
    /// </summary>
    private static int NextActorUID = 0;

    /// <summary>
    /// 角色Map<角色UID, 角色对象>
    /// </summary>
    private Dictionary<int, BaseActor> mActorsMap;

    /// <summary>
    /// 角色类型Map<角色类型, 角色对象列表>
    /// </summary>
    private Dictionary<ActorType, List<BaseActor>> mActorTypeMap;

    /// <summary>
    /// 构造函数
    /// </summary>
    public ActorManager()
    {
        mActorsMap = new Dictionary<int, BaseActor>();
        mActorTypeMap = new Dictionary<ActorType, List<BaseActor>>();
    }

    /// <summary>
    /// 创建指定类型指定资源路径指定位置的角色对象
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="resPath"></param>
    /// <param name="position"></param>
    /// <returns></returns>
    public T CreateActor<T>(string resPath, Vector3 position) where T : BaseActor, new()
    {
        T actor = new T();
        var nextActorUID = GetNextActorUID();
        actor.Init(nextActorUID, resPath, position);
        AddActor<T>(actor);
        return actor;
    }

    /// <summary>
    /// 获取指定uid的对象
    /// </summary>
    /// <param name="uid"></param>
    /// <returns></returns>
    public BaseActor GetActorByUID(int uid)
    {
        BaseActor actor;
        if(!mActorsMap.TryGetValue(uid, out actor))
        {
            Debug.LogError($"找不到UID:{uid}的对象，获取对象失败！");
            return null;
        }
        return actor;
    }

    /// <summary>
    /// 获取所有指定类型的对象列表
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public List<T> GetAllActors<T>() where T : BaseActor, new()
    {
        List<T> actorList = new List<T>();
        foreach(var actor in actorList)
        {
            if(actor is T)
            {
                actorList.Add(actor);
            }
        }
        return actorList;
    }

    /// <summary>
    /// 获取所有指定类型和角色类型的对象列表
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="actorType"></param>
    /// <returns></returns>
    public List<BaseActor> GetAllActorsByType(ActorType actorType)
    {
        List<BaseActor> actorList;
        if(!mActorTypeMap.TryGetValue(actorType, out actorList))
        {
            return null;
        }
        return actorList;
    }

    /// <summary>
    /// 销毁指定uid的对象
    /// </summary>
    /// <param name="uid"></param>
    /// <returns></returns>
    public bool DestroyActorByUID(int uid)
    {
        var actor = GetActorByUID(uid);
        if(actor == null)
        {
            Debug.LogError($"找不到UID:{uid}的对象，销毁对象失败！");
            return false;
        }
        actor.Destroy();
        RemoveActorByUID(uid);
        return true;
    }

    /// <summary>
    /// 添加指定类型对象
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="actor"></param>
    /// <returns></returns>
    private bool AddActor<T>(T actor) where T : BaseActor, new()
    {
        if(actor == null)
        {
            Debug.LogError($"不允许添加空对象，添加对象失败！");
            return false;
        }
        BaseActor existActor;
        if(mActorsMap.TryGetValue(actor.UID, out existActor))
        {
            Debug.LogError($"已存在UID:{actor.UID}的角色对象，请勿重复添加，请检查代码！");
            return false;
        }
        mActorsMap.Add(actor.UID, actor);
        List<BaseActor> actorTypeList;
        if(!mActorTypeMap.TryGetValue(actor.ActorType, out actorTypeList))
        {
            actorTypeList = new List<BaseActor>();
            mActorTypeMap.Add(actor.ActorType, actorTypeList);
        }
        actorTypeList.Add(actor);
        return true;
    }

    /// <summary>
    /// 移除指定uid的对象
    /// </summary>
    /// <param name="uid"></param>
    /// <returns></returns>
    private bool RemoveActorByUID(int uid)
    {
        BaseActor actor;
        if(!mActorsMap.TryGetValue(uid, out actor))
        {
            Debug.LogError($"找不到UID:{uid}的对象，移除对象失败！");
            return false;
        }
        mActorsMap.Remove(uid);
        List<BaseActor> actorTypeList;
        if (!mActorTypeMap.TryGetValue(actor.ActorType, out actorTypeList))
        {
            Debug.LogError($"角色类型:{actor.ActorType}对象数据里找不到UID:{uid}的对象，移除角色类型对象数据失败，请检查代码！");
        }
        actorTypeList?.Remove(actor);
        return true;
    }

    /// <summary>
    /// 获取下一个角色UID
    /// </summary>
    /// <returns></returns>
    private int GetNextActorUID()
    {
        return ++NextActorUID;
    }
}
