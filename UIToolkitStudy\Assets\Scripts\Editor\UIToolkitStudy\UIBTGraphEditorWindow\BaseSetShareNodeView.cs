﻿/*
 * Description:             BaseSetShareNodeView.cs
 * Author:                  TONYTANG
 * Create Date:             2023/10/05
 */

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;

namespace TCommonGraph
{
    /// <summary>
    /// BaseSetShareNodeView.cs
    /// 黑板数据设置节点显示基类
    /// </summary>
    public class BaseSetShareNodeView : BaseActionNodeView
    {
        /// <summary>
        /// 创建节点自定义UI
        /// </summary>
        protected override void CreateCustomUI()
        {
            base.CreateCustomUI();
            var nodeVerticalUIContainer = this.Q<VisualElement>(BTGraphElementNames.NodeUIVerticalContainerName);
            var nodeDesDivider = UIToolkitUtilities.CreateHorizontalDivider(BTGraphConstEditor.DividerColor);
            nodeVerticalUIContainer.Add(nodeDesDivider);

            UIToolkitUtilities.CreateBindSOPropertyField(nodeVerticalUIContainer, NodeData, "VariableName");
        }
    }
}