﻿using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;
using UnityEditor.UIElements;

/// <summary>
/// 代码修改USS的EditorWindow
/// </summary>
public class UIChangeUSSByCodeEditorWindow : EditorWindow
{
    [MenuItem("Window/UI Toolkit/UIToolkitStudy/UIChangeUSSByCodeEditorWindow")]
    public static void ShowUICodeStyleEditorWindow()
    {
        UIChangeUSSByCodeEditorWindow wnd = GetWindow<UIChangeUSSByCodeEditorWindow>();
        wnd.titleContent = new GUIContent("UIChangeUSSByCodeEditorWindow");
    }

    /// <summary>
    /// UIToolkit的rootVisualElement绘制方法
    /// </summary>
    public void CreateGUI()
    {
        CreateStyleUIByCode();
    }

    /// <summary>
    /// 通过代码修改USS创建UI
    /// </summary>
    private void CreateStyleUIByCode()
    {
        // 添加一个标题作为ui容器标题组件显示
        Label uiTitleLable = new Label("代码修改USS的Label");
        // 给Label指定不同的Color Style
        uiTitleLable.style.color = Color.red;
        // 设置Label居中显示Style
        uiTitleLable.style.alignSelf = Align.Center;

        // 必须将需要显示的组件添加到根节点才能显示
        // 将ui容器添加到根节点作为需要显示的UIElement
        rootVisualElement.Add(uiTitleLable);
    }
}