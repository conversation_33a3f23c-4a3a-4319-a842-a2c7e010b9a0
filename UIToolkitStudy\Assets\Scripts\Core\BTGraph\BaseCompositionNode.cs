﻿/*
 * Description:     BaseCompositionNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/06/19
 */

using System;

namespace TCommonGraph
{
    /// <summary>
    /// BaseCompositionNode.cs
    /// 行为树组合节点基类
    /// </summary>
    [Serializable]
    public abstract class BaseCompositionNode : BaseParentNode
    {
        /// <summary>
        /// 节点类型
        /// </summary>
        public override NodeType NodeType
        {
            get
            {
                return NodeType.Composition;
            }
        }

        /// <summary>
        /// 节点运行完毕
        /// </summary>
        /// <param name="success"></param>
        protected override void Stop(bool success)
        {
            if(mChildNodeList != null)
            {
                foreach(BaseNode childNode in mChildNodeList)
                {
                    childNode.ParentCompositeStop(this);
                }
            }
            base.Stop(success);
        }

        /// <summary>
        /// 停止比指定子节点优先级低的节点，并决定是否重新开启组合节点
        /// </summary>
        /// <param name="child"></param>
        /// <param name="immediateRestart"></param>
        public abstract void StopLowerPriorityChildrenForChild(BaseNode child, bool immediateRestart);
    }
}