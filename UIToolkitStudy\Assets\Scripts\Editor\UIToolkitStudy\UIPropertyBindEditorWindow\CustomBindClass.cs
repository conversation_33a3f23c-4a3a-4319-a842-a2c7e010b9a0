﻿/*
 * Description:     CustomBindClass.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/05/29
 */

using UnityEngine;

/// <summary>
/// CustomBindClass.cs
/// 自定义绑定类
/// </summary>
public class CustomBindClass
{
    /// <summary>
    /// 开关
    /// </summary>
    [Header("开关")]
    [SerializeField]
    public bool Switch;

    /// <summary>
    /// 数量
    /// </summary>
    [Header("数量")]
    [SerializeField]
    public int Num;
}