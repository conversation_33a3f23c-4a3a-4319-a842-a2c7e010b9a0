﻿/*
 * Description:     WaitTimeNode.cs
 * Author:          TonyTang
 * Create Date:     2023/06/19
 */

using System;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// WaitTimeNode.cs
    /// 等待时间节点
    /// </summary>
    [Serializable]
    public class WaitTimeNode : BaseActionNode
    {
        /// <summary>
        /// 等待时长
        /// </summary>
        [Header("等待时长")]
        [SerializeReference]
        public float WaitTime;

        /// <summary>
        /// 执行开始
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            mOwnerTreeData.Clock.AddTimer(WaitTime, 1, OnWaitTimeComplete);
        }

        /// <summary>
        /// 响应打断
        /// </summary>
        protected override void DoAbort()
        {
            base.DoAbort();
            Stop(false);
        }

        /// <summary>
        /// 执行运行完毕流程
        /// </summary>
        protected override void DoStop()
        {
            base.DoStop();
            mOwnerTreeData.Clock.RemoveTimer(OnWaitTimeComplete);
        }

        /// <summary>
        /// 响应等待时长完成
        /// </summary>
        protected void OnWaitTimeComplete()
        {
            Debug.Log($"UID:{GUID},WaitTimeNode:OnWaitTimeComplete(),WaitTime:{WaitTime}");
            Stop(true);
        }
    }
}