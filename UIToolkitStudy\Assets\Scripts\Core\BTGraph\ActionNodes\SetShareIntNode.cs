﻿/*
 * Description:             SetShareIntNode.cs
 * Author:                  TONYTANG
 * Create Date:             2023/10/04
 */

using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// SetShareIntNode.cs
    /// 设置黑板Int数据行为节点
    /// </summary>
    public class SetShareIntNode : BaseSetShareNode
    {
        /// <summary>
        /// 设置数据
        /// </summary>
        [Header("设置数据")]
        public int TargetValue;

        /// <summary>
        /// 执行开始
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            mOwnerTreeData.Blackboard.UpdateData<int>(VariableName, TargetValue);
            Stop(true);
        }
    }
}