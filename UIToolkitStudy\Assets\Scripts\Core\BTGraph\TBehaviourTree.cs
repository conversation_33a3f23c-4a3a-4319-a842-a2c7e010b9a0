﻿/*
 * Description:     TBehaviourTree.cs
 * Author:          Tony<PERSON><PERSON>
 * Create Date:     2023/11/10
 */

using NPBehave;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// TBehaviourTree.cs
    /// 行为树类
    /// </summary>
    public class TBehaviourTree : IRecycle
    {
        /// <summary>
        /// 绑定对象UID
        /// </summary>
        public int BindUID
        {
            get;
            private set;
        }

        /// <summary>
        /// 行为树原始数据对象(反序列化)
        /// </summary>
        public BehaviourTreeGraphData BTOriginalGraph
        {
            get;
            private set;
        }

        /// <summary>
        /// 运行时的行为树数据(Clone反序列化原始数据BTOriginalGraph而来)
        /// </summary>
        public BehaviourTreeGraphData BTRunningGraph
        {
            get;
            private set;
        }

        /// <summary>
        /// 行为树定时器
        /// </summary>
        public Clock BehaviourTreeClock
        {
            get;
            private set;
        }

        public TBehaviourTree()
        {
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="bindUID"></param>
        public void Init(int bindUID)
        {
            BehaviourTreeClock = new Clock();
            UpdateBindUID(bindUID);
            RegisterBehaviourTree();
        }

        /// <summary>
        /// 释放
        /// </summary>
        public void Release()
        {
            Reset();
            UnregisterBehaviourTree();
            ClearClock();
            ClearGraphData();
        }

        /// <summary>
        /// 更新绑定对象UID
        /// </summary>
        /// <param name="name"></param>
        public void UpdateBindUID(int bindUID)
        {
            BindUID = bindUID;
        }

        /// <summary>
        /// 注册行为树
        /// </summary>
        private void RegisterBehaviourTree()
        {
            TBehaviourTreeManager.Singleton.RegisterTBehaviourTree(this);
        }

        /// <summary>
        /// 取消注册行为树
        /// </summary>
        private void UnregisterBehaviourTree()
        {
            TBehaviourTreeManager.Singleton.UnregisterTBehaviourTree(this);
        }

        /// <summary>
        /// 重置数据
        /// </summary>
        private void Reset()
        {
            BindUID = 0;
        }

        /// <summary>
        /// 出池
        /// </summary>
        public void OnCreate()
        {
            Reset();
        }

        /// <summary>
        /// 入池
        /// </summary>
        public void OnDispose()
        {
            Release();
        }

        /// <summary>
        /// 暂停
        /// </summary>
        public void Pause()
        {
            BTRunningGraph?.Clock.Disable();
        }

        /// <summary>
        /// 继续
        /// </summary>
        public void Resume()
        {
            BTRunningGraph?.Clock.Enable();
        }

        /// <summary>
        /// 打断
        /// </summary>
        public void Stop()
        {
            BTRunningGraph?.Stop();
        }

        /// <summary>
        /// 行为树定时器更新驱动
        /// </summary>
        /// <param name="deltaTime"></param>
        public void Update(float deltaTime)
        {
            BTRunningGraph?.Clock?.Update(deltaTime);
        }

        /// <summary>
        /// 加载行为树图数据
        /// </summary>
        /// <param name="assetPath"></param>
        public void LoadBTGraphData(string assetPath)
        {
            ReleaseBTGraphAsset();
            BTOriginalGraph = TBehaviourTreeManager.Singleton.GetCacheBTGraph(assetPath);
            if(BTOriginalGraph == null)
            {
                var btGraphAsset = Resources.Load<BehaviourTreeGraphData>(assetPath);
                BTOriginalGraph = btGraphAsset;
                TBehaviourTreeManager.Singleton.CacheBTGraph(assetPath, BTOriginalGraph);
            }

            BTRunningGraph = BTOriginalGraph?.CloneSelf();
            BTRunningGraph?.SetBTOwner(this);
            BTRunningGraph?.InitRuntimeDatas(BehaviourTreeClock);
            BTRunningGraph?.Start();
        }

        /// <summary>
        /// 为TBehaviourTree创建行为树调试器
        /// </summary>
        public void CreateBTDebugger()
        {
            var bindActor = GetBindActor();
            if(bindActor == null)
            {
                Debug.LogError($"找不到UID:{BindUID}的绑定对象，创建行为树调试器失败！");
                return;
            }
            var btDebugger = bindActor.GO.GetComponent<BTDebugger>();
            if(btDebugger == null)
            {
                btDebugger = bindActor.GO.AddComponent<BTDebugger>();
            }
            btDebugger.BindTBehaviourTree = this;
        }

        /// <summary>
        /// 清理定时器
        /// </summary>
        private void ClearClock()
        {
            BehaviourTreeClock = null;
        }

        /// <summary>
        /// 清理图数据
        /// </summary>
        private void ClearGraphData()
        {
            ReleaseBTGraphAsset();
        }

        /// <summary>
        /// 获取绑定对象
        /// </summary>
        /// <returns></returns>
        public BaseActor GetBindActor()
        {
            var bindActor = ActorManager.Singleton.GetActorByUID(BindUID);
            if(bindActor == null)
            {
                Debug.LogError($"找不到绑定UID:{BindUID}的对象！");
                return null;
            }
            return bindActor;
        }

        /// <summary>
        /// 释放行为树数据
        /// </summary>
        /// <returns></returns>
        private bool ReleaseBTGraphAsset()
        {
            BTRunningGraph?.Stop();
            BTOriginalGraph = null;
            BTRunningGraph = null;
            return BTOriginalGraph != null;
        }
    }
}
