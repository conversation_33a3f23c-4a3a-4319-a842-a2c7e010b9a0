﻿/*
 * Description:     BTGraphDataKeys.cs
 * Author:          TonyTang
 * Create Date:     2023/06/19
 */

namespace TCommonGraph
{
    /// <summary>
    /// BTGraphDataKeys.cs
    /// 节点编辑器显示数据Key
    /// </summary>
    public static class BTGraphDataKeys
    {
        /// <summary>
        /// 组合节点折叠ViewDataKey
        /// </summary>
        public const string CompositionNodeFoldOutViewDataKeyName = "CompositionNodeFoldOutViewDataKey";

        /// <summary>
        /// 装饰节点折叠ViewDataKey
        /// </summary>
        public const string DecorationNodeFoldOutViewDataKeyName = "DecorationNodeFoldOutViewDataKey";

        /// <summary>
        /// 条件节点折叠ViewDataKey
        /// </summary>
        public const string ConditionNodeFoldOutViewDataKeyName = "ConditionNodeFoldOutViewDataKey";

        /// <summary>
        /// 行为节点折叠ViewDataKey
        /// </summary>
        public const string ActionNodeFoldOutViewDataKeyName = "ActionNodeFoldOutViewDataKey";

        /// <summary>
        /// bool变量类型折叠ViewDataKey
        /// </summary>
        public const string BoolVariableTypeFoldOutViewDataKeyName = "BoolVariableTypeFoldOutViewDataKey";

        /// <summary>
        /// int变量类型折叠ViewDataKey
        /// </summary>
        public const string IntVariableTypeFoldOutViewDataKeyName = "IntVariableTypeFoldOutViewDataKey";

        /// <summary>
        /// float变量类型折叠ViewDataKey
        /// </summary>
        public const string FloatVariableTypeFoldOutViewDataKeyName = "FloatVariableTypeFoldOutViewDataKey";

        /// <summary>
        /// string变量类型折叠ViewDataKey
        /// </summary>
        public const string StringVariableTypeFoldOutViewDataKeyName = "StringVariableTypeFoldOutViewDataKey";
    }
}