﻿/*
 * Description:     UIChildGenericEditorWindow1.cs
 * Author:          TonyTang
 * Create Date:     2023/10/04
 */

using UnityEditor;
using UnityEngine;

/// <summary>
/// UIChildGenericEditorWindow1.cs
/// </summary>
public class UIChildGenericEditorWindow1 : UIBaseGenericEditorWindow<ChildGraphView1>
{
    /// <summary>
    /// 打开节点编辑器窗口
    /// </summary>
    [MenuItem("Window/UI Toolkit/UIToolkitStudy/OpenUIChildGenericEditorWindow1")]
    public static void ShowUIChildGenericEditorWindow1()
    {
        if (!EditorWindow.HasOpenInstances<UIChildGenericEditorWindow1>())
        {
            UIChildGenericEditorWindow1 wnd = GetWindow<UIChildGenericEditorWindow1>();
            wnd.titleContent = new GUIContent("UIChildGenericEditorWindow1");
            wnd.Show();
        }
    }

    /// <summary>
    /// 关闭节点编辑器窗口
    /// </summary>
    [MenuItem("Window/UI Toolkit/UIToolkitStudy/CloseUIChildGenericEditorWindow1")]
    public static void CloseUIChildGenericEditorWindow1()
    {
        UIChildGenericEditorWindow1 wnd = GetWindow<UIChildGenericEditorWindow1>();
        if (wnd != null)
        {
            wnd.Close();
        }
    }

    /// <summary>
    /// 响应Unity加载完成
    /// </summary>
    [InitializeOnLoadMethod]
    private static void OnProjectLoadedInEditor()
    {
        if(EditorWindow.HasOpenInstances<UIChildGenericEditorWindow1>())
        {
            Debug.Log($"有已打开的UIChildGenericEditorWindow1窗口！");
        }
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    public UIChildGenericEditorWindow1() : base()
    {
        Debug.Log($"UIChildGenericEditorWindow1()");
        var editorWindowType = this.GetType();
        var graphViewFiledInfo = editorWindowType.GetField("GraphView");
        var graphViewMemberType = graphViewFiledInfo.FieldType;
        Debug.Log($"GraphView Type = {graphViewMemberType.Name} GraphData Type = {GetGraphViewDataType().Name}");
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        Debug.Log($"UIChildGenericEditorWindow1:OnDestroy()");
    }
}