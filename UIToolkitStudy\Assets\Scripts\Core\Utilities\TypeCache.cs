﻿/*
 * Description:     TypeCache.cs
 * Author:          Tony<PERSON>ang
 * Create Date:     2023/07/05
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;

/// <summary>
/// TypeCache.cs
/// 类型缓存静态工具类
/// Note:
/// 只支持当前工具类所在Assembly
/// </summary>
public static class TypeCache
{
    /// <summary>
    /// 类型信息缓存Map<类型全名, 类型信息>
    /// </summary>
    private static Dictionary<string, Type> TypeCacheMap = new Dictionary<string, Type>();

    /// <summary>
    /// 当前类所在Assembly
    /// </summary>
    private static Assembly CurrentAssembly = typeof(TypeCache).Assembly;

    /// <summary>
    /// 当前Assemble所有类型信息数组
    /// </summary>
    private static Type[] CurrentAssemblyAllTypes = CurrentAssembly.GetTypes();

    /// <summary>
    /// 获取指定类型名字的类型信息
    /// </summary>
    /// <param name="typeFullName"></param>
    /// <returns></returns>
    public static Type GetType(string typeFullName)
    {
        Type targetType;
        if(TypeCacheMap.TryGetValue(typeFullName, out targetType))
        {
            return targetType;
        }
        targetType = CurrentAssembly.GetType(typeFullName);
        if(targetType == null)
        {
            Debug.LogError($"找不到类型全名:{typeFullName}的类型信息！");
        }
        else
        {
            CacheType(targetType);
        }
        return targetType;
    }

    /// <summary>
    /// 缓存指定类型信息
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public static bool CacheType(Type type)
    {
        if (type == null)
        {
            Debug.LogError($"不允许缓存空类型信息！");
            return false;
        }
        if (!TypeCacheMap.ContainsKey(type.FullName))
        {
            TypeCacheMap.Add(type.FullName, type);
        }
        return true;
    }
}