﻿/*
 * Description:             SetShareBoolNode.cs
 * Author:                  TONYTANG
 * Create Date:             2023/10/04
 */

using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// SetShareBoolNode.cs
    /// 设置黑板Bool数据行为节点
    /// </summary>
    public class SetShareBoolNode : BaseSetShareNode
    {
        /// <summary>
        /// 设置数据
        /// </summary>
        [Header("设置数据")]
        public bool TargetValue;

        /// <summary>
        /// 执行开始
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            mOwnerTreeData.Blackboard.UpdateData<bool>(VariableName, TargetValue);
            Stop(true);
        }
    }
}