﻿/*
 * Description:     BehaviourTreeGraphData.cs
 * Author:          TonyTang
 * Create Date:     2023/07/03
 */

using System;
using System.Collections.Generic;
using UnityEditor.Experimental.GraphView;
using UnityEngine;
using NPBehave;

namespace TCommonGraph
{
    /// <summary>
    /// BehaviourTreeGraphData.cs
    /// 行为树图数据
    /// </summary>
    public class BehaviourTreeGraphData : ScriptableObject
    {
        /// <summary>
        /// 图朝向
        /// </summary>
        [Header("图朝向")]
        public Orientation GraphOrientation;

        /// <summary>
        /// 所有节点数据列表
        /// </summary>
        [Header("所有节点数据列表")]
        public List<BaseNode> AllNodeList;

        /// <summary>
        /// 所有边数据列表
        /// </summary>
        [Header("所有边数据列表")]
        public List<EdgeData> AllEdgeDataList;

        /// <summary>
        /// 黑板数据
        /// </summary>
        [Header("黑板数据")]
        public BlackboardData BlackboardData;

        /// <summary>
        /// 行为树节点所属行为树
        /// </summary>
        public TBehaviourTree OwnerBT
        {
            get;
            private set;
        }

        /// <summary>
        /// 树数据列表
        /// </summary>
        public List<TreeData> TreeDataList
        {
            get;
            private set;
        }

        /// <summary>
        /// 根节点树
        /// </summary>
        public TreeData RootTree
        {
            get;
            private set;
        }

        /// <summary>
        /// 时钟
        /// </summary>
        public Clock Clock
        {
            get;
            private set;
        }

        /// <summary>
        /// 数据共享黑板
        /// </summary>
        public Blackboard Blackboard
        {
            get;
            private set;
        }

        /// <summary>
        /// 初始化运行时数据完成
        /// </summary>
        protected bool mInitRunTimeDataComplete;

        /// <summary>
        /// 临时边数据列表
        /// </summary>
        private List<EdgeData> mTempEdgeDataList;

        public BehaviourTreeGraphData()
        {
            AllNodeList = new List<BaseNode>();
            AllEdgeDataList = new List<EdgeData>();
            OwnerBT = null;
            TreeDataList = new List<TreeData>();
            BlackboardData = new BlackboardData();
            Clock = null;
            Blackboard = null;
            RootTree = null;
            mInitRunTimeDataComplete = false;
            mTempEdgeDataList = new List<EdgeData>();
        }

        /// <summary>
        /// 设置所属行为树
        /// </summary>
        /// <param name="owner"></param>
        public void SetBTOwner(TBehaviourTree owner)
        {
            OwnerBT = owner;
        }

        /// <summary>
        /// 初始化运行时数据
        /// </summary>
        public void InitRuntimeDatas(Clock clock)
        {
            InitClock(clock);
            InitBlackboard();
            InitTreeDatas();
            mInitRunTimeDataComplete = true;
        }

        /// <summary>
        /// 开始行为树图数据运行
        /// </summary>
        public void Start()
        {
            if(!mInitRunTimeDataComplete)
            {
                Debug.LogError($"未初始化运行时数据，开始行为树图数据失败！");
                return;
            }
            Clock?.Enable();
            RootTree?.Start();
        }

        /// <summary>
        /// 停止行为树图数据运行
        /// </summary>
        public void Stop()
        {
            if(!mInitRunTimeDataComplete)
            {
                Debug.LogError($"未初始化运行时数据，停止听位数图数据失败！");
                return;
            }
            RootTree?.Stop();
        }

        /// <summary>
        /// 初始化定时器
        /// </summary>
        protected void InitClock(Clock clock)
        {
            Clock = clock;
        }

        /// <summary>
        /// 初始化共享黑板
        /// </summary>
        protected void InitBlackboard()
        {
            Blackboard = new Blackboard(Clock);
            foreach (var boolVariableData in BlackboardData.AllBoolDataList)
            {
                UpdateBlackboardData<bool>(boolVariableData.Name, boolVariableData.Data, true);
            }
            foreach (var intVariableData in BlackboardData.AllIntDataList)
            {
                UpdateBlackboardData<int>(intVariableData.Name, intVariableData.Data, true);
            }
            foreach (var floatVariableData in BlackboardData.AllFloatDataList)
            {
                UpdateBlackboardData<float>(floatVariableData.Name, floatVariableData.Data, true);
            }
            foreach (var stringVariableData in BlackboardData.AllStringDataList)
            {
                UpdateBlackboardData<string>(stringVariableData.Name, stringVariableData.Data, true);
            }
            Blackboard.PrintAllBlackBoardDatas();
        }

        /// <summary>
        /// 初始化树数据
        /// </summary>
        protected void InitTreeDatas()
        {
            RecyleAllTreeDatas();
            TreeDataList.Clear();
            var rootNodeList = GetAllRootNodes();
            foreach (var rootNode in rootNodeList)
            {
                var treeData = ObjectPool.Singleton.Pop<TreeData>();
                treeData.SetData(this, rootNode, Blackboard, Clock);
                TreeDataList.Add(treeData);
                if(RootTree == null)
                {
                    RootTree = treeData;
                }
            }
            if(TreeDataList.Count > 1)
            {
                Debug.LogError($"行为树不应该有多个根节点树，请检查配置！");
            }
        }

        /// <summary>
        /// 回收所有树列表数据
        /// </summary>
        protected void RecyleAllTreeDatas()
        {
            foreach (var treeData in TreeDataList)
            {
                ObjectPool.Singleton.Push<TreeData>(treeData);
            }
            TreeDataList.Clear();
        }

        /// <summary>
        /// 克隆自身
        /// Note:
        /// 此方法会实现嵌套存储的SO Asset也会全部Clone
        /// </summary>
        /// <returns></returns>
        public BehaviourTreeGraphData CloneSelf()
        {
            var cloneGraphData = this.Clone();
            for (int index = 0, length = AllNodeList.Count; index < length; index++)
            {
                cloneGraphData.AllNodeList[index] = AllNodeList[index].CloneSelf();
            }
            return cloneGraphData;
        }

        /// <summary>
        /// 设置图朝向
        /// </summary>
        /// <param name="orientation"></param>
        public void SetGraphOrientation(Orientation orientation)
        {
            GraphOrientation = orientation;
        }

        /// <summary>
        /// 添加指定节点
        /// </summary>
        /// <param name="node"></param>
        /// <returns></returns>
        public bool AddNode(BaseNode node)
        {
            if (GetNodeIndexByGuid(node.GUID) != -1)
            {
                Debug.LogError($"重复添加GUID:{node.GUID}的节点，添加节点数据失败！");
                return false;
            }
            AllNodeList.Add(node);
            Debug.Log($"添加GUID:{node.GUID}的节点数据！");
            return true;
        }

        /// <summary>
        /// 移除指定节点数据
        /// </summary>
        /// <param name="node"></param>
        /// <returns></returns>
        public bool RemoveNode(BaseNode node)
        {
            var nodeIndex = GetNodeIndexByNode(node);
            if(nodeIndex == -1)
            {
                Debug.LogError($"找不到节点GUID:{node.GUID}的节点，移除节点失败！");
                return false;
            }
            AllNodeList.Remove(node);
            Debug.Log($"移除GUID:{node}的节点数据！");
            return true;
        }

        /// <summary>
        /// 移除指定GUID的节点数据
        /// </summary>
        /// <param name="nodeGuid"></param>
        /// <returns></returns>
        public bool RemoveNodeByGUID(string nodeGuid)
        {
            var nodeIndex = GetNodeIndexByGuid(nodeGuid);
            if (nodeIndex == -1)
            {
                Debug.LogError($"不包含GUID:{nodeGuid}的节点数据，移除节点数据失败！");
                return false;
            }
            AllNodeList.RemoveAt(nodeIndex);
            Debug.Log($"移除GUID:{nodeGuid}的节点数据！");
            return true;
        }

        /// <summary>
        /// 获取制定节点GUID的索引
        /// </summary>
        /// <param name="nodeGuid"></param>
        /// <returns></returns>
        public int GetNodeIndexByGuid(string nodeGuid)
        {
            for(int i = 0; i < AllNodeList.Count; i++)
            {
                if(String.Equals(AllNodeList[i].GUID, nodeGuid))
                {
                    return i;
                }
            }
            return -1;
        }

        /// <summary>
        /// 获取指定节点的索引
        /// </summary>
        /// <param name="node"></param>
        /// <returns></returns>
        public int GetNodeIndexByNode(BaseNode node)
        {
            for(int i = 0;i < AllNodeList.Count; i++)
            {
                if(AllNodeList[i] == node)
                {
                    return i;
                }
            }
            return -1;
        }

        /// <summary>
        /// 获取指定GUID的节点
        /// </summary>
        /// <param name="nodeGuid"></param>
        /// <returns></returns>
        public BaseNode GetNodeByGUID(string nodeGuid)
        {
            var nodeIndex = GetNodeIndexByGuid(nodeGuid);
            if(nodeIndex != -1)
            {
                return AllNodeList[nodeIndex];
            }
            return null;
        }

        /// <summary>
        /// 添加指定边连线数据
        /// </summary>
        /// <param name="edgeData"></param>
        /// <returns></returns>
        public bool AddEdge(EdgeData edgeData)
        {
            var edgeIndex = GetEdgeIndexByEdgeData(edgeData);
            if(edgeIndex != -1)
            {
                Debug.LogError(@$"添加输出GUID:{edgeData.OutputNodeGUID}节点的输出端口名:{edgeData.OutputPortName},
                    输入GUID:{edgeData.InputNodeGUID}节点的输入端口名:{edgeData.InputPortName}的边数据已存在，添加边数据失败！");
                return false;
            }
            AllEdgeDataList.Add(edgeData);
            Debug.Log(@$"添加输出GUID:{edgeData.OutputNodeGUID}节点的输出端口名:{edgeData.OutputPortName},
                    输入GUID:{edgeData.InputNodeGUID}节点的输入端口名:{edgeData.InputPortName}的边数据！");
            OnEdgeDataUpdate(edgeData);
            return true;
        }

        /// <summary>
        /// 移除指定输出GUID节点，指定输出端口名，指定输入GUID节点和指定输入端口名的边数据
        /// </summary>
        /// <param name="outputNodeGuid"></param>
        /// <param name="outputPortName"></param>
        /// <param name="inputNodeGuid"></param>
        /// <param name="inputPortName"></param>
        /// <param name="removedEdgeData"></param>
        /// <returns></returns>
        public bool RemoveEdgeData(string outputNodeGuid, string outputPortName, string inputNodeGuid, string inputPortName, out EdgeData removedEdgeData)
        {
            removedEdgeData = null;
            var edgeData = GetEdgeData(outputNodeGuid, outputPortName, inputNodeGuid, inputPortName);
            if(edgeData == null)
            {
                Debug.LogError($"找不到输出GUID:{outputNodeGuid}，输出端口名:{outputPortName}，输入GUID:{inputNodeGuid}，输入端口名:{inputPortName}的边数据，移除边数据失败！");
                return false;
            }
            AllEdgeDataList.Remove(edgeData);
            removedEdgeData = edgeData;
            Debug.Log($"移除输出GUID:{outputNodeGuid}，输出端口名:{outputPortName}，输入GUID:{inputNodeGuid}，输入端口名:{inputPortName}的边数据！");
            OnEdgeDataUpdate(removedEdgeData);
            return true;
        }

        /// <summary>
        /// 获取指定输出GUID节点，指定输出端口名的边数据列表
        /// </summary>
        /// <param name="outputNodeGuid"></param>
        /// <param name="outputPortName"></param>
        /// <param name="edgeDataList"></param>
        /// <returns></returns>
        public void GetOutputNodePortEdgeDatas(string outputNodeGuid, string outputPortName, ref List<EdgeData> edgeDataList)
        {
            foreach(var edgeData in AllEdgeDataList)
            {
                if(string.Equals(edgeData.OutputNodeGUID, outputNodeGuid) &&
                    string.Equals(edgeData.OutputPortName, outputPortName))
                {
                    edgeDataList.Add(edgeData);
                }
            }
        }

        /// <summary>
        /// 获取指定输出GUID节点，指定输出端口名，指定输入GUID节点和指定输入端口名的边数据
        /// </summary>
        /// <param name="outputNodeGuid"></param>
        /// <param name="outputPortName"></param>
        /// <param name="inputNodeGuid"></param>
        /// <param name="inputPortName"></param>
        /// <returns></returns>
        public EdgeData GetEdgeData(string outputNodeGuid, string outputPortName, string inputNodeGuid, string inputPortName)
        {
            foreach(var edgeData in AllEdgeDataList)
            {
                if(string.Equals(edgeData.OutputNodeGUID, outputNodeGuid) &&
                    string.Equals(edgeData.OutputPortName, outputPortName) &&
                     string.Equals(edgeData.InputNodeGUID, inputNodeGuid) &&
                      string.Equals(edgeData.InputPortName, inputPortName))
                {
                    return edgeData;
                }
            }
            //Debug.LogError($"找不到输出GUID:{outputNodeGuid}，输出端口:{outputPortName}，输入GUID:{inputNodeGuid}，输入端口:{inputPortName}的边数据，获取边数据失败！");
            return null;
        }

        /// <summary>
        /// 获取指定边数据的索引
        /// </summary>
        /// <param name="edgeData"></param>
        /// <returns></returns>
        public int GetEdgeIndexByEdgeData(EdgeData edgeData)
        {
            for(int i = 0; i < AllEdgeDataList.Count; i++)
            {
                if(AllEdgeDataList[i] == edgeData)
                {
                    return i;
                }
            }
            return -1;
        }

        /// <summary>
        /// 响应边数据更新
        /// </summary>
        /// <param name="edgeData"></param>
        private void OnEdgeDataUpdate(EdgeData edgeData)
        {
            if (edgeData == null)
            {
                Debug.LogError($"不应该更新空边数据，请检查代码！");
                return;
            }
            UpdateNodeOutputPortEdgeIndex(edgeData.OutputNodeGUID, edgeData.OutputPortName);
        }

        /// <summary>
        /// 更新指定节点和指定输出端口名的边索引数据
        /// </summary>
        /// <param name="nodeGUID"></param>
        /// <param name="outputPortName"></param>
        public bool UpdateNodeOutputPortEdgeIndex(string nodeGUID, string outputPortName)
        {
            mTempEdgeDataList.Clear();
            GetOutputNodePortEdgeDatas(nodeGUID, outputPortName, ref mTempEdgeDataList);
            if (mTempEdgeDataList.Count == 0)
            {
                //Debug.Log($"找不到节点GUID:{nodeGUID}和输出端口名:{outputPortName}的边数据列表，更新边索引数据失败！");
                return false;
            }
            mTempEdgeDataList.Sort(SortPortEdgeDataIndex);
            for (int index = 0, length = mTempEdgeDataList.Count; index < length; index++)
            {
                var edgeData = mTempEdgeDataList[index];
                edgeData.UpdateEdgeOutputPortIndex(index);
            }
            Debug.Log($"更新节点GUID:{nodeGUID}的输出端口明:{outputPortName}的边索引数据！");
            AllEdgeDataList.Sort(SortAllEdgeDataIndex);
            return true;
        }

        /// <summary>
        /// 相同节点和相同端口边数据索引排序
        /// </summary>
        /// <param name="edgeData1"></param>
        /// <param name="edgeData2"></param>
        /// <returns></returns>
        private int SortPortEdgeDataIndex(EdgeData edgeData1, EdgeData edgeData2)
        {
            // 排序规则：
            // 1. 根据图的方向比较边输入节点的X或Y(横向图比Y，竖向图比X)
            var edgeData1InputNode = GetNodeByGUID(edgeData1.InputNodeGUID);
            var edgeData2InputNode = GetNodeByGUID(edgeData2.InputNodeGUID);
            if (GraphOrientation == Orientation.Horizontal)
            {
                return edgeData1InputNode.Position.y.CompareTo(edgeData2InputNode.Position.y);
            }
            else if (GraphOrientation == Orientation.Vertical)
            {
                return edgeData1InputNode.Position.x.CompareTo(edgeData2InputNode.Position.x);
            }
            else
            {
                Debug.LogError($"不支持的图朝向:{GraphOrientation}，边数据排序失败！");
                return 0;
            }
        }

        /// <summary>
        /// 所有边数据排序
        /// </summary>
        /// <param name="edgeData1"></param>
        /// <param name="edgeData2"></param>
        /// <returns></returns>
        private int SortAllEdgeDataIndex(EdgeData edgeData1, EdgeData edgeData2)
        {
            // 排序规则：
            // 1. 比较输出节点GUID
            // 2. 比较输出端口名
            // 3. 根据图的方向比较边输入节点的X或Y(横向图比Y，竖向图比X)
            if (!string.Equals(edgeData1.OutputNodeGUID, edgeData2.OutputNodeGUID))
            {
                return edgeData1.OutputNodeGUID.CompareTo(edgeData2.OutputNodeGUID);
            }
            else if (!string.Equals(edgeData1.OutputPortName, edgeData2.OutputPortName))
            {
                return edgeData1.OutputPortName.CompareTo(edgeData2.OutputPortName);
            }
            else
            {
                return SortPortEdgeDataIndex(edgeData1, edgeData2);
            }
        }

        /// <summary>
        /// 获取所有根节点列表
        /// </summary>
        /// <returns></returns>
        public List<BaseNode> GetAllRootNodes()
        {
            List<BaseNode> rootNodeList = new List<BaseNode>();
            foreach (var node in AllNodeList)
            {
                if (node.EntryPoint)
                {
                    rootNodeList.Add(node);
                }
            }
            if (rootNodeList.Count == 0)
            {
                Debug.LogWarning($"图:{name}没有根节点，获取所有根节点数量为0！");
            }
            return rootNodeList;
        }

        /// <summary>
        /// 获取首个根节点
        /// </summary>
        /// <returns></returns>
        public BaseNode GetFirstRootNode()
        {
            foreach (var nodeData in AllNodeList)
            {
                if (nodeData.EntryPoint)
                {
                    return nodeData;
                }
            }
            Debug.LogError($"图:{name}没有根节点，获取首个根节点失败！");
            return null;
        }

        /// <summary>
        /// 获取指定节点相连的所有子节点列表(含自身)
        /// </summary>
        /// <param name="allNodeList"></param>
        /// <param name="node"></param>
        /// <param name="recusive"></param>
        public void GetAllChildNodeList(ref List<BaseNode> allNodeList, BaseNode node, bool recusive = false)
        {
            if (node == null)
            {
                return;
            }
            if (!allNodeList.Contains(node))
            {
                allNodeList.Add(node);
            }
            if (AllEdgeDataList == null || AllEdgeDataList.Count == 0)
            {
                return;
            }
            List<BaseNode> searchChildNodeList = new List<BaseNode>();
            foreach (var edgeData in AllEdgeDataList)
            {
                if (edgeData.IsNodeOutputEdge(node.GUID))
                {
                    var connectedChildNode = GetNodeByGUID(edgeData.InputNodeGUID);
                    // 需要搜索的子节点必须是没搜索过且不重复的，避免循环连接后导致死循环
                    if (!allNodeList.Contains(connectedChildNode))
                    {
                        allNodeList.Add(connectedChildNode);
                        if (!searchChildNodeList.Contains(connectedChildNode))
                        {
                            searchChildNodeList.Add(connectedChildNode);
                        }
                    }
                }
            }
            if (recusive)
            {
                foreach (var searchChildNode in searchChildNodeList)
                {
                    GetAllChildNodeList(ref allNodeList, searchChildNode, recusive);
                }
            }
        }

        /// <summary>
        /// 获取指定节点相连的所有节点相关边数据列表
        /// </summary>
        /// <param name="allEdgeDataList"></param>
        /// <param name="node"></param>
        /// <param name="recusive"></param>
        public void GetAllChildEdgeDataList(ref List<EdgeData> allEdgeDataList, BaseNode node, bool recusive = false)
        {
            if (node == null)
            {
                return;
            }
            if (AllEdgeDataList == null || AllEdgeDataList.Count == 0)
            {
                return;
            }
            List<BaseNode> searchChildNodeList = new List<BaseNode>();
            foreach (var edgeData in AllEdgeDataList)
            {
                if (edgeData.IsNodeOutputEdge(node.GUID))
                {
                    var connectedChildNode = GetNodeByGUID(edgeData.InputNodeGUID);
                    // 需要搜索的子节点必须是没搜索过且不重复的，避免循环连接后导致死循环
                    if (!allEdgeDataList.Contains(edgeData))
                    {
                        allEdgeDataList.Add(edgeData);
                        if (!searchChildNodeList.Contains(connectedChildNode))
                        {
                            searchChildNodeList.Add(connectedChildNode);
                        }
                    }
                }
            }
            if (recusive)
            {
                foreach (var searchCHildNode in searchChildNodeList)
                {
                    GetAllChildEdgeDataList(ref allEdgeDataList, searchCHildNode, recusive);
                }
            }
        }

        /// <summary>
        /// 重新生成所有节点的GUID
        /// </summary>
        public void RegenerateAllNodeGUID()
        {
            if (AllNodeList == null || AllNodeList.Count == 0)
            {
                return;
            }
            // GUID变化Map<老GUID, 新GUID>
            Dictionary<string, string> guidChangeMap = new Dictionary<string, string>();
            foreach (var nodeData in AllNodeList)
            {
                var newGuid = Guid.NewGuid().ToString();
                Debug.Log($"原节点GUID:{nodeData.GUID}，新节点GUID:{newGuid}");
                guidChangeMap.Add(nodeData.GUID, newGuid);
                nodeData.UpdateGUID(newGuid);
            }
            foreach (var edgeData in AllEdgeDataList)
            {
                var newGuid = Guid.NewGuid().ToString();
                Debug.Log($"原边GUID:{edgeData.GUID}，新边GUID:{newGuid}");
                var inputNodeGUID = edgeData.InputNodeGUID;
                var outputNodeGUID = edgeData.OutputNodeGUID;
                Debug.Log($"边输入节点GUID:{inputNodeGUID}，边输出节点GUID:{outputNodeGUID}");
                var newInputNodeGUID = guidChangeMap[inputNodeGUID];
                var newOutputNodeGUID = guidChangeMap[outputNodeGUID];
                edgeData.UpdateEdgeGUID(newGuid);
                edgeData.UpdateInputNodeGUID(newInputNodeGUID);
                edgeData.UpdateOutputNodeGUID(newOutputNodeGUID);
            }
        }

        /// <summary>
        /// 移动到指定位置
        /// </summary>
        /// <param name="targetPos"></param>
        /// <returns></returns>
        public bool MoveToTargetPosition(Vector2 targetPos)
        {
            var firstRootNode = GetFirstRootNode();
            if (firstRootNode == null)
            {
                Debug.Log($"图:{name}没有根节点，无法移动节点到目标位置！");
                return false;
            }
            var positionOffset = targetPos - firstRootNode.Position.position;
            foreach (var nodeData in AllNodeList)
            {
                nodeData.Position.position = nodeData.Position.position + positionOffset;
            }
            return true;
        }

        /// <summary>
        /// 更新T类型共享黑板数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="noNotification">是否不通知(默认通知)</param>
        /// <returns></returns>
        public bool UpdateBlackboardData<T>(string key, T value, bool noNotification = false)
        {
            return Blackboard.UpdateData<T>(key, value, noNotification);
        }

        /// <summary>
        /// 获取T类型共享黑板数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <returns></returns>
        public T GetValue<T>(string key)
        {
            return Blackboard.GetValue<T>(key);
        }

        /// <summary>
        /// 移除黑板数据
        /// </summary>
        /// <param name="key"></param>
        /// <param name="noNotification">是否不通知(默认通知)</param>
        /// <returns></returns>
        public bool RemoveBlackboardData(string key, bool noNotification = false)
        {
            return Blackboard.RemoveData(key, noNotification);
        }

        /// <summary>
        /// 重置所有节点运行时数据
        /// </summary>
        public void ResetAllNodeRunTimeData()
        {
            if(AllNodeList == null)
            {
                return;
            }
            foreach(var node in AllNodeList)
            {
                node.ResetNodeRuntimeData();
            }
        }
    }
}