﻿/*
 * Description:             CompareShareIntNode.cs
 * Author:                  TONYTANG
 * Create Date:             2023/10/04
 */

using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// CompareShareIntNode.cs
    /// 比较黑板Int数据条件节点
    /// </summary>
    public class CompareShareIntNode : BaseCompareShareNode
    {
        /// <summary>
        /// 比较数据
        /// </summary>
        [Header("比较数据")]
        public int TargetValue;

        /// <summary>
        /// 值是否相等
        /// </summary>
        /// <returns></returns>
        protected override bool IsValueEqual()
        {
            var value = mOwnerTreeData.Blackboard.GetValue<int>(VariableName);
            return TargetValue == value;
        }

        /// <summary>
        /// 值是否大于
        /// </summary>
        /// <returns></returns>
        protected override bool IsValueGreater()
        {
            var value = mOwnerTreeData.Blackboard.GetValue<int>(VariableName);
            return value > TargetValue;
        }
    }
}