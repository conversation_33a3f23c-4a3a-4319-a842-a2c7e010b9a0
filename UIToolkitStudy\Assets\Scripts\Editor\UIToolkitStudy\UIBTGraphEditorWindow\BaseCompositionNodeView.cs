﻿/*
 * Description:     BaseCompositionNodeView.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/07/03
 */

using UnityEditor.Experimental.GraphView;

namespace TCommonGraph
{
    /// <summary>
    /// BaseCompositionNodeView.cs
    /// 组合节点NodeView基类
    /// </summary>
    public class BaseCompositionNodeView : NodeView
    {
        /// <summary>
        /// 出口Port连线数量类型
        /// </summary>
        public override Port.Capacity OutPortCapacity
        {
            get
            {
                return Port.Capacity.Multi;
            }
        }
    }
}