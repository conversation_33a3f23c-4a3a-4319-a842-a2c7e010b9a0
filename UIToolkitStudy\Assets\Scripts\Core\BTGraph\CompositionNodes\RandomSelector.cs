﻿/*
 * Description:     RandomSelector.cs
 * Author:          Tony<PERSON>ang
 * Create Date:     2023/06/28
 */

using System;
using System.Collections.Generic;

namespace TCommonGraph
{
    /// <summary>
    /// RandomSelector.cs
    /// 行为树随机选择节点
    /// </summary>
    [Serializable]
    public class RandomSelector : BaseCompositionNode
    {
        /// <summary>
        /// 随机数
        /// </summary>
        private static Random Rnd = new Random();

        /// <summary>
        /// 当前运行子节点索引
        /// </summary>
        private int mCurrentIndex = -1;

        /// <summary>
        /// 随机顺序
        /// </summary>
        private int[] mRandomizedOrder;

        /// <summary>
        /// 设置子节点列表
        /// </summary>
        /// <param name="childNodeList"></param>
        public override void SetChildNodeList(List<BaseNode> childNodeList)
        {
            base.SetChildNodeList(childNodeList);
            mRandomizedOrder = new int[ChildNodeCount];
            for(int i = 0; i < ChildNodeCount; i++)
            {
                mRandomizedOrder[i] = i;
            }
        }

        /// <summary>
        /// 执行开始
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            mCurrentIndex = -1;
            DoRandomExecuteOrder();
        }

        /// <summary>
        /// 执行随机执行顺序
        /// </summary>
        protected void DoRandomExecuteOrder()
        {
            int randomRange = ChildNodeCount;
            while(randomRange > 1)
            {
                int nextRandomIndex = Rnd.Next(randomRange);
                randomRange--;
                var lastRandomIndex = mRandomizedOrder[randomRange];
                mRandomizedOrder[randomRange] = mRandomizedOrder[nextRandomIndex];
                mRandomizedOrder[nextRandomIndex] = lastRandomIndex;
            }
            ProcessChildren();
        }

        /// <summary>
        /// 响应打断
        /// </summary>
        /// <returns></returns>
        protected override void DoAbort()
        {
            var nextRandomChildIndex = mRandomizedOrder[mCurrentIndex];
            mChildNodeList[nextRandomChildIndex].Abort();
        }

        /// <summary>
        /// 执行子节点打断
        /// </summary>
        /// <param name="child"></param>
        /// <param name="success"></param>
        protected override void DoChildStop(BaseNode child, bool success)
        {
            base.DoChildStop(child, success);
            if (success)
            {
                Stop(true);
            }
            else
            {
                ProcessChildren();
            }
        }

        /// <summary>
        /// 执行子节点
        /// </summary>
        protected void ProcessChildren()
        {
            mCurrentIndex++;
            if (mCurrentIndex < ChildNodeCount)
            {
                if (IsAbort)
                {
                    Stop(false);
                }
                else
                {
                    var nextRandomChildIndex = mRandomizedOrder[mCurrentIndex];
                    mChildNodeList[nextRandomChildIndex].Start();
                }
            }
            else
            {
                Stop(false);
            }
        }

        /// <summary>
        /// 停止比指定子节点优先级低的节点，并决定是否重新开启组合节点
        /// </summary>
        /// <param name="child"></param>
        /// <param name="immediateRestart"></param>
        public override void StopLowerPriorityChildrenForChild(BaseNode child, bool immediateRestart)
        {
            int indexForChild = 0;
            bool found = false;
            foreach (BaseNode currentChild in mChildNodeList)
            {
                if (currentChild == child)
                {
                    found = true;
                }
                else if (!found)
                {
                    indexForChild++;
                }
                else if (found && currentChild.IsRunning)
                {
                    if (immediateRestart)
                    {
                        mCurrentIndex = indexForChild - 1;
                    }
                    else
                    {
                        mCurrentIndex = ChildNodeCount;
                    }
                    currentChild.Abort();
                    break;
                }
            }
        }

        /// <summary>
        /// 字符串表达
        /// </summary>
        public override string ToString()
        {
            return $"{base.ToString()},mCurrentIndex:{mCurrentIndex}";
        }

    }
}