/*
 * Description:             GameLuancher.cs
 * Author:                  TONYTANG
 * Create Date:             2023/12/08
 */

using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace TCommonGraph
{
    /// <summary>
    /// GameLuancher.cs
    /// 游戏启动器
    /// </summary>
    public class GameLuancher : MonoBehaviour
    {
        /// <summary>
        /// 加载行为树执行介绍按钮
        /// </summary>
        [Header("加载行为树执行介绍按钮")]
        public Button BTExecuteFlowIntroBtn;

        /// <summary>
        /// 增加玩家血量按钮
        /// </summary>
        [Header("增加玩家血量按钮")]
        public Button IncreasePlayerHealthBtn;

        /// <summary>
        /// 减少玩家血量按钮
        /// </summary>
        [Header("减少玩家血量按钮")]
        public Button DecreasePlayerHealthBtn;

        /// <summary>
        /// 加载None黑板打断AI按钮
        /// </summary>
        [Header("加载None黑板打断AI按钮")]
        public Button LoadStopNoneBBAIBtn;

        /// <summary>
        /// 加载Self黑板打断AI按钮
        /// </summary>
        [Header("加载Self黑板打断AI按钮")]
        public Button LoadStopSelfBBAIBtn;

        /// <summary>
        /// 加载LowerPriority黑板打断AI按钮
        /// </summary>
        [Header("加载LP黑板打断AI按钮")]
        public Button LoadStopLPBBAIBtn;

        /// <summary>
        /// 加载Both黑板打断AI按钮
        /// </summary>
        [Header("加载Both黑板打断AI按钮")]
        public Button LoadStopBothBBAIBtn;

        /// <summary>
        /// 加载LPIR黑板打断AI按钮
        /// </summary>
        [Header("加载LPIR黑板打断AI按钮")]
        public Button LoadStopLPIRBBAIBtn;

        /// <summary>
        /// 加载IR黑板打断AI按钮
        /// </summary>
        [Header("加载IR黑板打断AI按钮")]
        public Button LoadStopImmediateRestartBBAIBtn;

        /// <summary>
        /// 自定义观察条件AI按钮
        /// </summary>
        [Header("自定义观察条件AI按钮")]
        public Button LoadTestConditionAIBtn;

        /// <summary>
        /// 暂停所有AI按钮
        /// </summary>
        [Header("暂停所有AI按钮")]
        public Button PauseAllAIBtn;

        /// <summary>
        /// 继续所有AI按钮
        /// </summary>
        [Header("继续所有AI按钮")]
        public Button ResumeAllAIBtn;

        /// <summary>
        /// 玩家血量描述文本
        /// </summary>
        [Header("玩家血量描述文本")]
        public Text PlayerHealthValueTxt;

        /// <summary>
        /// 怪物最近距离文本
        /// </summary>
        [Header("怪物最近距离文本")]
        public Text MonsterDistanceTxt;

        /// <summary>
        /// 玩家角色对象
        /// </summary>
        private PlayerActor mPlayerActor;

        /// <summary>
        /// 怪物角色对象列表
        /// </summary>
        private List<MonsterActor> mMonsterActorList;

        private void Awake()
        {
            DontDestroyOnLoad(this);
            Init();
        }

        private void Start()
        {
            AddListeners();
        }

        private void Update()
        {
            RefreshGameDataUIView();
        }

        /// <summary>
        /// 添加所有监听
        /// </summary>
        private void AddListeners()
        {
            BTExecuteFlowIntroBtn.onClick.AddListener(OnBTExecuteFlowIntroBtnClick);
            IncreasePlayerHealthBtn.onClick.AddListener(OnIncreasePlayerHealthBtnClick);
            DecreasePlayerHealthBtn.onClick.AddListener(OnDecreasePlayerHealthBtnClick);
            LoadStopNoneBBAIBtn.onClick.AddListener(OnLoadStopNoneBBAIBtnClick);
            LoadStopSelfBBAIBtn.onClick.AddListener(OnLoadStopSelfBBAIBtnClick);
            LoadStopLPBBAIBtn.onClick.AddListener(OnLoadStopLPBBAIBtnClick);
            LoadStopBothBBAIBtn.onClick.AddListener(OnLoadStopBothBBAIBtnClick);
            LoadStopLPIRBBAIBtn.onClick.AddListener(OnLoadStopLPIRBBAIBtnClick);
            LoadStopImmediateRestartBBAIBtn.onClick.AddListener(OnLoadStopIRBBAIBtnClick);
            LoadTestConditionAIBtn.onClick.AddListener(OnLoadGameAIBtnClick);
            PauseAllAIBtn.onClick.AddListener(OnPauseAllAIBtnClick);
            ResumeAllAIBtn.onClick.AddListener(OnResumeAllAIBtnClick);
        }

        /// <summary>
        /// 初始化
        /// </summary>
        private void Init()
        {
            CreatePlayers();
            CreateMonsters();
        }

        /// <summary>
        /// 创建玩家
        /// </summary>
        private void CreatePlayers()
        {
            mPlayerActor = ActorManager.Singleton.CreateActor<PlayerActor>("BTGraph/Player", Vector3.zero);
        }

        /// <summary>
        /// 创建怪物
        /// </summary>
        private void CreateMonsters()
        {
            mMonsterActorList = new List<MonsterActor>();
            var monsterBasicPos = new Vector3(5, 0, 5);
            for(int i = 0; i < 1; i++)
            {
                var monsterPos = monsterBasicPos + new Vector3(i * 5, 0, 0);
                var monsterActor = ActorManager.Singleton.CreateActor<MonsterActor>("BTGraph/Monster", monsterPos);
                mMonsterActorList.Add(monsterActor);
            }
        }

        /// <summary>
        /// 刷新游戏数据UI显示
        /// </summary>
        private void RefreshGameDataUIView()
        {
            RefreshPlayerHealthValueView();
            RefreshMonsterNearestDistanceView();
        }

        /// <summary>
        /// 刷新玩家生命值显示
        /// </summary>
        private void RefreshPlayerHealthValueView()
        {
            var playerHealthValue = mPlayerActor != null ? mPlayerActor.Health : 0f;
            PlayerHealthValueTxt.text = $"玩家生命值:{playerHealthValue}";
        }

        /// <summary>
        /// 刷新怪物最近距离显示
        /// </summary>
        private void RefreshMonsterNearestDistanceView()
        {
            var nearestMonsterDistance = GetNearestMonsterDistance();
            MonsterDistanceTxt.text = $"怪物最近距离:{nearestMonsterDistance}";
        }

        /// <summary>
        /// 获取怪物最近距离
        /// </summary>
        /// <returns></returns>
        private float GetNearestMonsterDistance()
        {
            if(mMonsterActorList == null)
            {
                return 0;
            }
            var playerPos = mPlayerActor.GetPosition();
            var neareastDistance = 0f;
            bool firstMonster = true;
            foreach(var monster in mMonsterActorList)
            {
                var monsterPos = monster.GetPosition();
                var distance = Vector3.Distance(playerPos, monsterPos);
                if(firstMonster)
                {
                    neareastDistance = distance;
                }
                else
                {
                    if(distance < neareastDistance)
                    {
                        neareastDistance = distance;
                    }
                }
                firstMonster = false;
            }
            return neareastDistance;
        }

        /// <summary>
        /// 响应行为树执行流程介绍按钮点击
        /// </summary>
        private void OnBTExecuteFlowIntroBtnClick()
        {
            
        }

        /// <summary>
        /// 响应增加玩家生命值按钮点击
        /// </summary>
        private void OnIncreasePlayerHealthBtnClick()
        {
            mPlayerActor?.AddHealth(5);
        }

        /// <summary>
        /// 响应减少玩家生命值按钮点击
        /// </summary>
        private void OnDecreasePlayerHealthBtnClick()
        {
            mPlayerActor?.DecreaseHealth(5);
        }

        /// <summary>
        /// 响应加载测试Stop.None黑板打断监听AI按钮点击
        /// </summary>
        private void OnLoadStopNoneBBAIBtnClick()
        {
            mPlayerActor?.LoadAI("BTGraph/BehaviourTreeGraphView/TestBBStopNone");
        }

        /// <summary>
        /// 响应加载测试Stop.Self黑板打断监听AI按钮点击
        /// </summary>
        private void OnLoadStopSelfBBAIBtnClick()
        {
            mPlayerActor?.LoadAI("BTGraph/BehaviourTreeGraphView/TestBBStopSelf");
        }

        /// <summary>
        /// 响应加载测试Stop.LowerPriority黑板打断监听AI按钮点击
        /// </summary>
        private void OnLoadStopLPBBAIBtnClick()
        {
            mPlayerActor?.LoadAI("BTGraph/BehaviourTreeGraphView/TestBBStopLowerPriority");
        }

        /// <summary>
        /// 响应加载测试Stop.Both黑板打断监听AI按钮点击
        /// </summary>
        private void OnLoadStopBothBBAIBtnClick()
        {
            mPlayerActor?.LoadAI("BTGraph/BehaviourTreeGraphView/TestBBStopBoth");
        }

        /// <summary>
        /// 响应加载测试Stop.LowerPriorityImmediateRestart黑板打断监听AI按钮点击
        /// </summary>
        private void OnLoadStopLPIRBBAIBtnClick()
        {
            mPlayerActor?.LoadAI("BTGraph/BehaviourTreeGraphView/TestBBStopLPIR");
        }

        /// <summary>
        /// 响应加载测试Stop.ImmediateRestart黑板打断监听AI按钮点击
        /// </summary>
        private void OnLoadStopIRBBAIBtnClick()
        {
            mPlayerActor?.LoadAI("BTGraph/BehaviourTreeGraphView/TestBBStopIR");
        }

        /// <summary>
        /// 响应加载测试自定义观察条件AI按钮点击
        /// </summary>
        private void OnLoadGameAIBtnClick()
        {
            mPlayerActor?.LoadAI("BTGraph/BehaviourTreeGraphView/PlayerGraph");
            foreach(var monster in mMonsterActorList)
            {
                monster.LoadAI("BTGraph/BehaviourTreeGraphView/MonsterGraph");
            }
        }

        /// <summary>
        /// 响应暂停所有AI按钮点击
        /// </summary>
        private void OnPauseAllAIBtnClick()
        {
            TBehaviourTreeManager.Singleton.PauseAll();
        }

        /// <summary>
        /// 响应继续所有AI按钮点击
        /// </summary>
        private void OnResumeAllAIBtnClick()
        {
            TBehaviourTreeManager.Singleton.ResumeAll();
        }
    }
}
