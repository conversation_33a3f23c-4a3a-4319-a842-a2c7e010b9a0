﻿/*
 * Description:             UIBTGraphEditorWindow.cs
 * Author:                  TONYTANG
 * Create Date:             2023/10/04
 */

using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEditor.UIElements;
using UnityEngine;
using UnityEngine.UIElements;

namespace TCommonGraph
{
    /// <summary>
    /// UIBTGraphEditorWindow.cs
    /// 行为树图窗口类
    /// </summary>
    public class UIBTGraphEditorWindow : EditorWindow
    {
        /// <summary>
        /// 面板类型
        /// </summary>
        public enum PanelType
        {
            NODE_CONFIG_PANEL = 0,      // 节点参数配置面板
            BLACKBOARD_PANEL,           // 黑板面板
        }

        /// <summary>
        /// 节点自定义数据
        /// </summary>
        protected class NodeItemUserData
        {
            /// <summary>
            /// 节点类型
            /// </summary>
            public NodeType NodeType
            {
                get;
                private set;
            }

            /// <summary>
            /// 节点索引
            /// </summary>
            public int NodeIndex
            {
                get;
                private set;
            }

            /// <summary>
            /// 节点类型信息
            /// </summary>
            public Type TypeInfo
            {
                get;
                private set;
            }

            public NodeItemUserData(NodeType nodeType, int nodeIndex, Type typeInfo)
            {
                NodeType = nodeType;
                NodeIndex = nodeIndex;
                TypeInfo = typeInfo;
            }
        }

        /// <summary>
        /// 可用的节点类型列表
        /// </summary>
        protected List<NodeType> mAvailableNodeTypeList;

        /// <summary>
        /// 可用节点类型Map
        /// </summary>
        protected Dictionary<NodeType, NodeType> mAvailableNodeTypeMap;

        /// <summary>
        /// 节点类型和对应节点类型信息Map
        /// </summary>
        protected Dictionary<NodeType, List<Type>> mNodeTypeTypeMap;

        /// <summary>
        /// 保存文件名
        /// </summary>
        protected string mGraphSaveFileName = BTGraphConstEditor.DefaultGraphSaveFileName;

        /// <summary>
        /// 选中Graph Asset路径
        /// </summary>
        protected string mSelectedGraphAssetPath;

        /// <summary>
        /// 选中Graph数据
        /// </summary>
        protected BehaviourTreeGraphData mSelectedGraphData;

        /// <summary>
        /// 临时绑定属性Map<属性名, 属性绑定数据>
        /// </summary>
        protected Dictionary<string, PropertyBindData> mTempBindPropertyDataMap = new Dictionary<string, PropertyBindData>();

        /// <summary>
        /// 当前选择面板类型
        /// </summary>
        protected PanelType mCurrentSelectedPanelType = PanelType.NODE_CONFIG_PANEL;

        /// <summary>
        /// 可用黑板变量类型列表
        /// </summary>
        protected List<BlackboardDataType> mAvalibleVariableTypeList;

        /// <summary>
        /// 节点编辑GraphView
        /// </summary>
        protected BehaviourTreeGraphView mGraphView;

        /// <summary>
        /// 保存路径
        /// </summary>
        protected string mGraphSaveFolderPath;

        /// <summary>
        /// 构造函数
        /// </summary>
        public UIBTGraphEditorWindow()
        {
            Debug.Log($"UIBTGraphEditorWindow()");
            mGraphSaveFolderPath = GetDeafaultSaveFolderPath();
        }

        /// <summary>
        /// 打开行为树节点编辑器窗口
        /// </summary>
        [MenuItem("Window/UI Toolkit/UIToolkitStudy/UIBTGraphEditorWindow")]
        public static void ShowUIBTGraphEditorWindow()
        {
            if (!EditorWindow.HasOpenInstances<UIBTGraphEditorWindow>())
            {
                UIBTGraphEditorWindow wnd = GetWindow<UIBTGraphEditorWindow>();
                wnd.titleContent = new GUIContent("UIBTGraphEditorWindow");
                wnd.Show();
            }
        }

        /// <summary>
        /// 关闭行为树节点编辑器窗口Assets/Resources/BTGraph/BehaviourTreeGraphView
        /// </summary>
        [MenuItem("Window/UI Toolkit/UIToolkitStudy/CloseUIBTGraphEditorWindow")]
        public static void CloseUICoomonGraphEditorWindow()
        {
            UIBTGraphEditorWindow wnd = GetWindow<UIBTGraphEditorWindow>();
            if (wnd != null)
            {
                wnd.Close();
            }
        }

        /// <summary>
        /// 响应EditorWindow的OnInspectorUpdate
        /// </summary>
        private void OnInspectorUpdate()
        {
            mGraphView?.OnInspectorUpdate();
        }

        /// <summary>
        /// 响应EditorWindow的Update
        /// </summary>
        private void Update()
        {
            mGraphView?.Update();
            // 运行时黑板数据不是通过属性绑定显示的
            // 所以采用通过更新刷新的方式确保黑板UI实时显示
            if(Application.isPlaying)
            {
                UpdateBlackboardDataUI();
            }
        }

        private void OnEnable()
        {
            Debug.Log($"UICommonGraphEditorWindow:OnEnable()");
            InitNodeDatas();
            InitBlackboardDatas();
        }

        private void OnDisable()
        {
            Debug.Log($"UICommonGraphEditorWindow:OnDisable()");
        }

        private void OnDestroy()
        {
            Debug.Log($"UICommonGraphEditorWindow:OnDestroy()");
            ClearSelectedGraphData();
            ClearGraphView();
        }

        /// <summary>
        /// 获取当前GraphView的GraphData类型
        /// </summary>
        /// <returns></returns>
        protected Type GetGraphViewDataType()
        {
            return BTGraphConstEditor.BehaviourTreeGraphDataType;
        }

        /// <summary>
        /// 获取默认保存路径
        /// </summary>
        /// <returns></returns>
        protected string GetDeafaultSaveFolderPath()
        {
            var graphDataViewTypeName = typeof(BehaviourTreeGraphView).Name;
            return $"{BTGraphConstEditor.DefaultGraphSaveFolderPath}/{graphDataViewTypeName}";
        }

        /// <summary>
        /// 清除选中图数据
        /// </summary>
        private void ClearSelectedGraphData()
        {
            if (mSelectedGraphData != null)
            {
                Debug.Log($"清除选中图数据！");
                mSelectedGraphData = null;
                mSelectedGraphAssetPath = string.Empty;
                mGraphSaveFolderPath = BTGraphConstEditor.DefaultGraphSaveFolderPath;
                mGraphSaveFileName = BTGraphConstEditor.DefaultGraphSaveFileName;
                UpdateAssetSelectionValue();
            }
        }

        /// <summary>
        /// 清理GraphView
        /// </summary>
        protected void ClearGraphView()
        {
            if (mGraphView != null)
            {
                mGraphView?.OnDestroy();
                var middleGraphViewContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.MiddleGraphViewContainerName);
                middleGraphViewContainer.Remove(mGraphView);
                mGraphView = null;
            }
        }

        /// <summary>
        /// 响应选择变化
        /// </summary>
        private void OnSelectionChange()
        {
            if (Selection.objects.Length > 0)
            {
                var selectionAsset = Selection.objects[0];
                if (selectionAsset is GameObject selectionGo)
                {
                    var btDebugger = selectionGo.GetComponent<BTDebugger>();
                    if(btDebugger != null && btDebugger.BindTBehaviourTree.BTRunningGraph != null)
                    {
                        UpdateDebugSelectedGraphData(btDebugger.BindTBehaviourTree.BTRunningGraph);
                    }
                }
            }
        }

        /// <summary>
        /// 初始化节点数据
        /// </summary>
        private void InitNodeDatas()
        {
            mAvailableNodeTypeList = GetInitAvalibleNodeTypeList();
            InitNodeTypeDatas();
        }

        /// <summary>
        /// 获取初始化可用节点类型列表
        /// Note:
        /// 子类重写自定义可用节点类型
        /// </summary>
        /// <returns></returns>
        protected List<NodeType> GetInitAvalibleNodeTypeList()
        {
            return new List<NodeType>()
            {
                NodeType.Composition,
                NodeType.Decoration,
                NodeType.Condition,
                NodeType.Action,
            };
        }

        /// <summary>
        /// 初始化黑板相关数据
        /// </summary>
        private void InitBlackboardDatas()
        {
            mAvalibleVariableTypeList = GetInitAvalibleVariableTypeList();
        }

        /// <summary>
        /// 获取初始化可用黑板变量类型列表
        /// </summary>
        /// <returns></returns>
        protected List<BlackboardDataType> GetInitAvalibleVariableTypeList()
        {
            return new List<BlackboardDataType>()
            {
                BlackboardDataType.Bool,
                BlackboardDataType.String,
                BlackboardDataType.Float,
                BlackboardDataType.Int,
            };
        }

        /// <summary>
        /// 初始化节点类型数据
        /// </summary>
        private void InitNodeTypeDatas()
        {
            InitAvalibleNodeTypeMapData();
            InitNodeTypeTypeListData();
        }

        /// <summary>
        /// 初始化节点类型Map数据
        /// </summary>
        private void InitAvalibleNodeTypeMapData()
        {
            mAvailableNodeTypeMap = new Dictionary<NodeType, NodeType>();
            foreach (var availableNodeType in mAvailableNodeTypeList)
            {
                if (!mAvailableNodeTypeMap.ContainsKey(availableNodeType))
                {
                    mAvailableNodeTypeMap.Add(availableNodeType, availableNodeType);
                }
            }
        }

        /// <summary>
        /// 初始化节点类型类型信息列表数据
        /// </summary>
        private void InitNodeTypeTypeListData()
        {
            mNodeTypeTypeMap = new Dictionary<NodeType, List<Type>>();
            foreach (var availableNodeType in mAvailableNodeTypeList)
            {
                if (!mNodeTypeTypeMap.ContainsKey(availableNodeType))
                {
                    mNodeTypeTypeMap.Add(availableNodeType, new List<Type>());
                }
            }
            var allNodeTypes = BTGraphConstEditor.BaseNodeAssembly.GetTypes().Where(
                nodeType => (BTGraphConstEditor.BaseNodeType.IsAssignableFrom(nodeType) && !nodeType.IsAbstract && nodeType != BTGraphConstEditor.BaseNodeType));
            foreach (var type in allNodeTypes)
            {
                var nodeInstance = ScriptableObject.CreateInstance(type) as BaseNode;
                if (IsAvalibleNodeType(nodeInstance.NodeType))
                {
                    List<Type> typeList;
                    if (mNodeTypeTypeMap.TryGetValue(nodeInstance.NodeType, out typeList))
                    {
                        typeList.Add(type);
                    }
                }
            }
        }

        /// <summary>
        /// 获取制定节点类型的节点类型信息列表
        /// </summary>
        /// <param name="nodeType"></param>
        /// <returns></returns>
        private List<Type> GetTypeListByNodeType(NodeType nodeType)
        {
            List<Type> typeList;
            if (mNodeTypeTypeMap.TryGetValue(nodeType, out typeList))
            {
                return typeList;
            }
            return typeList;
        }

        /// <summary>
        /// 是否是可用的节点类型
        /// </summary>
        /// <param name="nodeType"></param>
        /// <returns></returns>
        private bool IsAvalibleNodeType(NodeType nodeType)
        {
            return mAvailableNodeTypeMap.ContainsKey(nodeType);
        }

        private void CreateGUI()
        {
            CreateMenuUI();
            CreatePanelContentUI();
        }

        /// <summary>
        /// 绘制菜单栏UI
        /// </summary>
        private void CreateMenuUI()
        {
            CreateToolbarUI();
            CreateToolbarMenuUI();
        }

        /// <summary>
        /// 创建菜单区域
        /// </summary>
        private void CreateToolbarUI()
        {
            var menuHorizontalContainer = new VisualElement();
            menuHorizontalContainer.style.flexDirection = FlexDirection.Row;
            menuHorizontalContainer.style.height = 20;
            menuHorizontalContainer.style.left = 0;
            menuHorizontalContainer.style.right = 0;

            // 创建toolbar
            var menuToolbar = new Toolbar();
            menuToolbar.name = BTGraphElementNames.MenuToolBarName;
            menuToolbar.style.left = 0;
            menuToolbar.style.right = 0;
            menuToolbar.style.flexGrow = 1;
            menuHorizontalContainer.Add(menuToolbar);
            rootVisualElement.Add(menuHorizontalContainer);
        }

        /// <summary>
        /// 创建Toolbar菜单UI
        /// </summary>
        private void CreateToolbarMenuUI()
        {
            CreateFileNameUI();
        }

        /// <summary>
        /// 创建文件名UI
        /// </summary>
        private void CreateFileNameUI()
        {
            // 创建菜单文件名Label组件
            var toolbarFileNameTitleLabel = new Label();
            toolbarFileNameTitleLabel.text = "保存文件名:";
            toolbarFileNameTitleLabel.style.width = 70f;
            toolbarFileNameTitleLabel.style.unityTextAlign = TextAnchor.MiddleLeft;
            toolbarFileNameTitleLabel.style.alignSelf = Align.Center;
            var menuToolbar = rootVisualElement.Q<Toolbar>(BTGraphElementNames.MenuToolBarName);
            menuToolbar.Add(toolbarFileNameTitleLabel);

            var toolbarFileNameTextField = new TextField();
            toolbarFileNameTextField.name = BTGraphElementNames.ToolBarFileNameName;
            toolbarFileNameTextField.value = mGraphSaveFileName;
            toolbarFileNameTextField.style.width = 210f;
            toolbarFileNameTextField.RegisterValueChangedCallback(OnGraphSaveFileNameValueChange);
            menuToolbar.Add(toolbarFileNameTextField);
        }

        /// <summary>
        /// 响应Graph保存文件名修改
        /// </summary>
        /// <param name="changeEvent"></param>
        protected void OnGraphSaveFileNameValueChange(ChangeEvent<string> changeEvent)
        {
            Debug.Log($"修改保存文件名，从:{changeEvent.previousValue}到:{changeEvent.newValue}!");
            mGraphSaveFileName = changeEvent.newValue;
        }

        /// <summary>
        /// 创建面板UI
        /// </summary>
        private void CreatePanelContentUI()
        {
            var horizontalContentContainer = new VisualElement();
            horizontalContentContainer.name = BTGraphElementNames.HorizontalContentContainerName;
            horizontalContentContainer.style.left = 0;
            horizontalContentContainer.style.top = 0;
            horizontalContentContainer.style.bottom = 0;
            horizontalContentContainer.style.right = 0;
            horizontalContentContainer.style.flexGrow = 1;
            horizontalContentContainer.style.flexDirection = FlexDirection.Row;
            horizontalContentContainer.style.position = Position.Relative;
            rootVisualElement.Add(horizontalContentContainer);
            CreateLeftVerticalContentUI();
            CreateMiddleGraphViewUI();
            CreateRightContentUI();
        }

        /// <summary>
        /// 创建左侧竖向内容UI显示
        /// </summary>
        private void CreateLeftVerticalContentUI()
        {
            CreateLeftVerticalContainer();
            CreateLeftOperationContent();
            CreateLeftNodeContent();
        }

        /// <summary>
        /// 创建左侧竖向容器
        /// </summary>
        private void CreateLeftVerticalContainer()
        {
            var leftVerticalContentContainer = new VisualElement();
            leftVerticalContentContainer.name = BTGraphElementNames.LeftVerticalContentContainerName;
            leftVerticalContentContainer.style.left = 0;
            leftVerticalContentContainer.style.width = 280;
            leftVerticalContentContainer.style.top = 0;
            leftVerticalContentContainer.style.bottom = 0;
            leftVerticalContentContainer.style.flexDirection = FlexDirection.Column;
            leftVerticalContentContainer.AddToClassList("unity-rect-field");
            var horizontalContentContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.HorizontalContentContainerName);
            horizontalContentContainer.Add(leftVerticalContentContainer);
        }

        /// <summary>
        /// 创建左侧操作内容
        /// </summary>
        private void CreateLeftOperationContent()
        {
            CreateSavePathContent();
            CreateNodeOperationContent();
        }

        /// <summary>
        /// 创建保存路径内容
        /// </summary>
        private void CreateSavePathContent()
        {
            var leftVerticalContentContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.LeftVerticalContentContainerName);
            var savePathHorizontalContainer = new VisualElement();
            savePathHorizontalContainer.style.left = 0;
            savePathHorizontalContainer.style.right = 0;
            savePathHorizontalContainer.style.height = 20;
            savePathHorizontalContainer.style.flexDirection = FlexDirection.Row;
            savePathHorizontalContainer.AddToClassList("unity-box");

            var savePathLabelTitle = new Label();
            savePathLabelTitle.style.width = 70f;
            savePathLabelTitle.style.unityTextAlign = TextAnchor.MiddleLeft;
            savePathLabelTitle.style.alignSelf = Align.Center;
            savePathLabelTitle.text = "保存路径:";
            savePathHorizontalContainer.Add(savePathLabelTitle);
            var savePathTextField = new TextField();
            savePathTextField.name = BTGraphElementNames.SavePathTextFieldName;
            savePathTextField.style.flexGrow = 1;
            savePathTextField.style.flexShrink = 1;
            savePathTextField.value = mGraphSaveFolderPath;
            savePathHorizontalContainer.Add(savePathTextField);
            var savePathButton = new Button();
            savePathButton.style.width = 60;
            savePathButton.text = "修改";
            savePathHorizontalContainer.Add(savePathButton);
            // 注册按钮点击
            savePathButton.RegisterCallback<ClickEvent>(OnSavePathButtonClick);
            leftVerticalContentContainer.Add(savePathHorizontalContainer);
        }

        /// <summary>
        /// 响应保存路径按钮点击
        /// </summary>
        /// <param name="clickEvent"></param>
        private void OnSavePathButtonClick(ClickEvent clickEvent)
        {
            Debug.Log($"保存路径按钮点击！");
            var newSavePath = EditorUtility.OpenFolderPanel("保存路径", mGraphSaveFolderPath, string.Empty);
            if (!AssetDatabase.IsValidFolder(newSavePath))
            {
                Debug.LogError($"请选择有效的Asset路径，此:{newSavePath}路径无效，修改保存路径失败！");
                return;
            }
            if (!string.IsNullOrEmpty(newSavePath))
            {
                mGraphSaveFolderPath = newSavePath;
                Debug.Log($"更新保存路径:{newSavePath}");
                var savePathTextField = rootVisualElement.Q<TextField>(BTGraphElementNames.SavePathTextFieldName);
                savePathTextField.value = mGraphSaveFolderPath;
            }
        }

        /// <summary>
        /// 创建节点操作面板
        /// </summary>
        private void CreateNodeOperationContent()
        {
            var leftVerticalContentContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.LeftVerticalContentContainerName);
            var assetSelectionHorizontalContainer = UIToolkitUtilities.CreateHorizontalContainer(BTGraphElementNames.AssetSelectionHorizontalContainerName,
                                                                                                    0f, null, 0, 0, 0, 0, default(StyleColor));
            assetSelectionHorizontalContainer.style.height = 20f;
            leftVerticalContentContainer.Add(assetSelectionHorizontalContainer);

            var assetSelectionTitleLabel = new Label();
            assetSelectionTitleLabel.text = "Asset:";
            assetSelectionTitleLabel.style.width = 70f;
            assetSelectionTitleLabel.style.unityTextAlign = TextAnchor.MiddleLeft;
            assetSelectionTitleLabel.style.alignSelf = Align.Center;
            assetSelectionHorizontalContainer.Add(assetSelectionTitleLabel);

            var assetSelectionObjectField = new ObjectField();
            assetSelectionObjectField.objectType = GetGraphViewDataType();
            assetSelectionObjectField.name = BTGraphElementNames.AssetSelectionName;
            assetSelectionObjectField.style.flexGrow = 1;
            assetSelectionObjectField.style.width = 210;
            assetSelectionObjectField.allowSceneObjects = false;
            assetSelectionObjectField.RegisterValueChangedCallback(OnAssetSelectionValueChange);
            assetSelectionHorizontalContainer.Add(assetSelectionObjectField);
            UpdateAssetSelectionValue();

            var newGraphButton = new Button();
            newGraphButton.name = BTGraphElementNames.SaveButtonName;
            newGraphButton.text = "新建";
            newGraphButton.style.height = 20;
            leftVerticalContentContainer.Add(newGraphButton);
            // 注册导出按钮点击
            newGraphButton.RegisterCallback<ClickEvent>(OnNewGraphButtonClick);

            var saveButton = new Button();
            saveButton.name = BTGraphElementNames.SaveButtonName;
            saveButton.text = "保存";
            saveButton.style.height = 20;
            leftVerticalContentContainer.Add(saveButton);
            // 注册导出按钮点击
            saveButton.RegisterCallback<ClickEvent>(OnSaveButtonClick);
        }

        /// <summary>
        /// 更新选中Asset
        /// </summary>
        private void UpdateAssetSelectionValue()
        {
            var assetSelectionObjectField = rootVisualElement.Q<ObjectField>(BTGraphElementNames.AssetSelectionName); ;
            if (assetSelectionObjectField != null)
            {
                assetSelectionObjectField.value = mSelectedGraphData;
            }
        }

        /// <summary>
        /// 响应选中Asset变化
        /// </summary>
        /// <param name="assetSelected"></param>
        private void OnAssetSelectionValueChange(ChangeEvent<UnityEngine.Object> assetSelected)
        {
            var previewAssetName = assetSelected.previousValue != null ? assetSelected.previousValue.name : string.Empty;
            var newAssetName = assetSelected.newValue != null ? assetSelected.newValue.name : string.Empty;
            Debug.Log($"响应Asset选择变化，从:{previewAssetName}到:{newAssetName}");
            UpdateSelectedGraphData(assetSelected.newValue as BehaviourTreeGraphData);
        }

        /// <summary>
        /// 响应新建按钮点击
        /// </summary>
        /// <param name="clickEvent"></param>
        private void OnNewGraphButtonClick(ClickEvent clickEvent)
        {
            if(Application.isPlaying)
            {
                Debug.LogWarning($"运行时不允许新建操作！");
                return;
            }
            ClearSelectedGraphData();
            CreateGraphView();
        }

        /// <summary>
        /// 响应保存按钮点击
        /// </summary>
        /// <param name="clickEvent"></param>
        private void OnSaveButtonClick(ClickEvent clickEvent)
        {
            if (Application.isPlaying)
            {
                Debug.LogWarning($"运行时不允许保存操作！");
                return;
            }
            if (string.IsNullOrEmpty(mGraphSaveFileName))
            {
                Debug.LogError($"不允许保存空文件名！");
                return;
            }
            var graphDataPath = Path.Combine(mGraphSaveFolderPath, mGraphSaveFileName);
            graphDataPath = PathUtilities.GetRegularPath(graphDataPath);
            var graphData = mGraphView.SourceGraphData;
            if (graphData != null)
            {
                var saveFodlerFullPath = PathUtilities.GetAssetFullPath(mGraphSaveFolderPath);
                FolderUtilities.CheckAndCreateSpecificFolder(saveFodlerFullPath);
                var assetSourcePath = AssetDatabase.GetAssetPath(graphData);
                assetSourcePath = PathUtilities.GetRegularPath(assetSourcePath);
                if (!string.IsNullOrEmpty(assetSourcePath))
                {
                    Debug.Log($"目标Asset:{assetSourcePath}已存在本地！");
                    if (string.Equals(assetSourcePath, graphDataPath))
                    {
                        Debug.Log($"目标Asset:{graphDataPath}保存路径相同，直接保存！");
                    }
                    else
                    {
                        Debug.Log($"目标Asset:{assetSourcePath}保存路径不同，移动到:{graphDataPath}并保存！");
                        AssetDatabase.MoveAsset(assetSourcePath, graphDataPath);
                    }
                }
                else
                {
                    var targetPathAsset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(graphDataPath);
                    if (targetPathAsset != null)
                    {
                        AssetDatabase.DeleteAsset(graphDataPath);
                        Debug.Log($"目标Assets不存在，但目标位置:{graphDataPath}存在同名Asset，删除同名Asset！");
                    }
                    AssetDatabase.CreateAsset(graphData, graphDataPath);
                    Debug.Log($"目标Asset不存在，创建新Asset！");
                }
                // BaseNode是继承至ScriptableObject的
                // 要想数据被正确序列化，需要将所有节点数据作为GraphData的SubAsset存储
                foreach (var node in graphData.AllNodeList)
                {
                    AssetDatabase.RemoveObjectFromAsset(node);
                    AssetDatabase.AddObjectToAsset(node, graphData);
                }
                // 删除因为Undo系统导致未及时删除的Node Asset
                var allSubAssets = AssetDatabase.LoadAllAssetsAtPath(graphDataPath);
                foreach(var subAsset in allSubAssets)
                {
                    var subNode = subAsset as BaseNode;
                    if(subNode != null && graphData.GetNodeByGUID(subNode.GUID) == null)
                    {
                        Debug.Log($"节点GUID:{subNode.GUID}Asset的逻辑对象不存在了，需要删除冗余Node Asset!");
                        AssetDatabase.RemoveObjectFromAsset(subAsset);
                    }
                }
                //调用Import更新状态
                AssetDatabase.ImportAsset(graphDataPath);
                AssetDatabase.SaveAssets();
                Debug.Log($"保存图:{graphDataPath}数据成功！");
            }
            else
            {
                Debug.LogError($"不允许保存空图数据，保存图:{graphDataPath}数据失败！");
            }
        }

        /// <summary>
        /// 创建左侧节点内容
        /// </summary>
        private void CreateLeftNodeContent()
        {
            var nodeVerticalContainer = new VisualElement();
            nodeVerticalContainer.name = BTGraphElementNames.NodeVerticalContainerName;
            nodeVerticalContainer.style.flexDirection = FlexDirection.Column;
            nodeVerticalContainer.style.flexGrow = 1;
            nodeVerticalContainer.AddToClassList("unity-popup-window");
            var leftVerticalContentContaienr = rootVisualElement.Q<VisualElement>(BTGraphElementNames.LeftVerticalContentContainerName);
            leftVerticalContentContaienr.Add(nodeVerticalContainer);

            var actionLabelTitle = new Label();
            actionLabelTitle.style.height = 20;
            actionLabelTitle.text = "节点列表";
            actionLabelTitle.AddToClassList("unity-button");
            nodeVerticalContainer.Add(actionLabelTitle);

            CreateAllNodeTypeUI();
        }

        /// <summary>
        /// 创建所有节点类型的UI
        /// </summary>
        private void CreateAllNodeTypeUI()
        {
            foreach (var availableNodeType in mAvailableNodeTypeList)
            {
                var nodeFoldOut = new Foldout();
                var nodeTypeTitle = BTGraphUtilitiesEditor.GetNodeTypeTitle(availableNodeType);
                nodeFoldOut.name = nodeTypeTitle;
                nodeFoldOut.viewDataKey = BTGraphUtilitiesEditor.GetNodeTypeFoldOutViewDataKeyName(availableNodeType);
                nodeFoldOut.text = nodeTypeTitle;
                nodeFoldOut.AddToClassList("unity-box");
                var typeList = GetTypeListByNodeType(availableNodeType);
                var nodeListView = new ListView(typeList, 20, OnMakeNodeItem, (item, index) =>
                {
                    OnBindNodeItem(item, index, availableNodeType);
                });
                nodeFoldOut.Add(nodeListView);
                // 注册节点FoldOut值变化回调
                nodeFoldOut.RegisterValueChangedCallback(OnNodeFoldOutValueChange);
                var nodeVerticalContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.NodeVerticalContainerName);
                nodeVerticalContainer.Add(nodeFoldOut);
            }
        }

        /// <summary>
        /// 响应节点FoldOut值变化
        /// </summary>
        /// <param name="changeEvent"></param>
        private void OnNodeFoldOutValueChange(ChangeEvent<bool> changeEvent)
        {
            Debug.Log($"响应节点折叠值更新:{changeEvent.newValue}");
        }

        /// <summary>
        /// 响应节点Item构建
        /// </summary>
        /// <returns></returns>
        private VisualElement OnMakeNodeItem()
        {
            return new Button();
        }

        /// <summary>
        /// 响应节点Item绑定
        /// </summary>
        /// <param name="item"></param>
        /// <param name="index"></param>
        /// <param name="nodeType"></param>
        private void OnBindNodeItem(VisualElement item, int index, NodeType nodeType)
        {
            var nodeItemButton = item as Button;
            nodeItemButton.name = GetNodeItemName(nodeType, index);
            var typeList = GetTypeListByNodeType(nodeType);
            var type = typeList[index];
            nodeItemButton.text = type.Name;
            nodeItemButton.userData = new NodeItemUserData(nodeType, index, type);
            nodeItemButton.RegisterCallback<ClickEvent>(OnNodeItemButtonClick);
        }

        /// <summary>
        /// 响应节点按钮点击
        /// </summary>
        /// <param name="clickEvent"></param>
        private void OnNodeItemButtonClick(ClickEvent clickEvent)
        {
            var nodeItemButton = clickEvent.target as Button;
            var nodeItemUserData = (NodeItemUserData)nodeItemButton.userData;
            Debug.Log($"点击了节点类型:{nodeItemUserData.NodeType} 节点索引:{nodeItemUserData.NodeIndex} 类型信息:{nodeItemUserData.TypeInfo.Name}的按钮！");
            mGraphView.CreateNodeByType(nodeItemUserData.TypeInfo, BTGraphConstEditor.NodeDefaultPos);
        }

        /// <summary>
        /// 获取指定节点类型和索引的节点Item名
        /// </summary>
        /// <param name="nodeType"></param>
        /// <param name="index"></param>
        /// <returns></returns>
        private string GetNodeItemName(NodeType nodeType, int index)
        {
            var typeList = GetTypeListByNodeType(nodeType);
            return index < typeList.Count ? typeList[index].Name : "未知";
        }

        /// <summary>
        /// 创建中间节点UI显示
        /// </summary>
        private void CreateMiddleGraphViewUI()
        {
            var middleGraphViewContainer = new VisualElement();
            middleGraphViewContainer.name = BTGraphElementNames.MiddleGraphViewContainerName;
            middleGraphViewContainer.style.left = 0;
            middleGraphViewContainer.style.right = 0;
            middleGraphViewContainer.style.top = 0;
            middleGraphViewContainer.style.bottom = 0;
            middleGraphViewContainer.style.flexDirection = FlexDirection.Column;
            middleGraphViewContainer.style.flexGrow = 1;
            middleGraphViewContainer.AddToClassList("unity-box");

            var middleGraphViewLabelTitle = new Label();
            middleGraphViewLabelTitle.text = "节点面板";
            middleGraphViewLabelTitle.style.height = 20;
            middleGraphViewLabelTitle.style.alignSelf = Align.Center;
            middleGraphViewContainer.Add(middleGraphViewLabelTitle);

            var horizontalContentContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.HorizontalContentContainerName);
            horizontalContentContainer.Add(middleGraphViewContainer);

            CreateGraphView();
        }

        /// <summary>
        /// 创建节点编辑器GraphView
        /// </summary>
        protected void CreateGraphView()
        {
            ClearGraphView();
            var middleGraphViewContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.MiddleGraphViewContainerName);
            mGraphView = GetNewGraphView();
            mGraphView.StretchToParentSize();
            middleGraphViewContainer.Add(mGraphView);
        }

        /// <summary>
        /// 创建新的GraphView
        /// </summary>
        /// <returns></returns>
        protected BehaviourTreeGraphView GetNewGraphView()
        {
            return new BehaviourTreeGraphView(mAvailableNodeTypeList, this.OnSelectedNodeChange);
        }

        /// <summary>
        /// 创建右侧内容UI显示
        /// </summary>
        private void CreateRightContentUI()
        {
            var rightVerticalContentContainer = new VisualElement();
            rightVerticalContentContainer.name = BTGraphElementNames.RightVerticalContentContainerName;
            rightVerticalContentContainer.style.left = 0;
            rightVerticalContentContainer.style.width = 300f;
            rightVerticalContentContainer.style.top = 0;
            rightVerticalContentContainer.style.bottom = 0;
            rightVerticalContentContainer.style.flexDirection = FlexDirection.Column;
            rightVerticalContentContainer.AddToClassList("unity-rect-field");

            var toolbarMenu = new Toolbar();
            toolbarMenu.name = BTGraphElementNames.RightToolbarMenuName;
            toolbarMenu.style.flexDirection = FlexDirection.Row;
            toolbarMenu.style.height = 25f;

            var rightNodeConfigMenuButton = new Button(OnNodeConfigMenuClick);
            rightNodeConfigMenuButton.name = BTGraphElementNames.RightNodeConfigMenuButtonName;
            rightNodeConfigMenuButton.text = "节点参数";
            rightNodeConfigMenuButton.style.flexGrow = 1f;
            rightNodeConfigMenuButton.AddToClassList("unity-toggle");
            toolbarMenu.Add(rightNodeConfigMenuButton);


            var rightBlackboardMenuButton = new Button(OnBlackboardMenuClick);
            rightBlackboardMenuButton.name = BTGraphElementNames.RightBlackboardMenuButtonName;
            rightBlackboardMenuButton.text = "黑板";
            rightBlackboardMenuButton.style.flexGrow = 1f;
            rightBlackboardMenuButton.AddToClassList("unity-toggle");
            toolbarMenu.Add(rightBlackboardMenuButton);

            rightVerticalContentContainer.Add(toolbarMenu);

            var rightPanelVerticalContainer = UIToolkitUtilities.CreateVerticalContainer(BTGraphElementNames.RightPanelVerticalContainerName, 1f, "unity-rect-field");
            rightVerticalContentContainer.Add(rightPanelVerticalContainer);

            var horizontalContentContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.HorizontalContentContainerName);
            horizontalContentContainer.Add(rightVerticalContentContainer);

            UpdateSelectedPanelUI();
        }

        /// <summary>
        /// 更新选中面板类型
        /// </summary>
        /// <param name="panelType"></param>
        protected void UpdateSelectedPanel(PanelType panelType)
        {
            mCurrentSelectedPanelType = panelType;
            UpdateSelectedPanelUI();
        }

        /// <summary>
        /// 响应节点配置菜单按钮点击
        /// </summary>
        private void OnBlackboardMenuClick()
        {
            Debug.Log($"节点黑板面板菜单按钮点击！");
            UpdateSelectedPanel(PanelType.BLACKBOARD_PANEL);
        }

        /// <summary>
        /// 响应节点配置菜单按钮点击
        /// </summary>
        private void OnNodeConfigMenuClick()
        {
            Debug.Log($"节点参数面板菜单按钮点击！");
            UpdateSelectedPanel(PanelType.NODE_CONFIG_PANEL);
        }

        /// <summary>
        /// 更新选中面板显示UI
        /// </summary>
        protected void UpdateSelectedPanelUI()
        {
            var rightPanelVerticalContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.RightPanelVerticalContainerName);
            rightPanelVerticalContainer.Clear();
            if (mCurrentSelectedPanelType == PanelType.NODE_CONFIG_PANEL)
            {
                CreateSelectionNodeConfigUI();
            }
            else if (mCurrentSelectedPanelType == PanelType.BLACKBOARD_PANEL)
            {
                CreateBlackboardUI();
            }
            else
            {
                CreateNotSupportPanelTypeUI();
            }
        }

        /// <summary>
        /// 创建选择节点配置UI
        /// </summary>
        protected void CreateSelectionNodeConfigUI()
        {
            var rightPanelVerticalContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.RightPanelVerticalContainerName);
            var rightNodeConfigVerticalContainer = UIToolkitUtilities.CreateVerticalContainer(BTGraphElementNames.RightNodeConfigVerticalContainerName);
            rightPanelVerticalContainer.Add(rightNodeConfigVerticalContainer);

            var rightVerticalContentLableTitle = new Label();
            rightVerticalContentLableTitle.text = "参数面板";
            rightVerticalContentLableTitle.style.height = 20f;
            rightVerticalContentLableTitle.style.alignSelf = Align.Center;
            rightNodeConfigVerticalContainer.Add(rightVerticalContentLableTitle);

            if (mGraphView.SelectedNode != null)
            {
                CreateSelectedNodeInspector(rightNodeConfigVerticalContainer, mGraphView.SelectedNode);
            }
            else
            {
                var rightSelectedNodeTipLabelTitle = new Label();
                rightSelectedNodeTipLabelTitle.text = "无选中节点";
                rightSelectedNodeTipLabelTitle.style.height = 20;
                rightSelectedNodeTipLabelTitle.style.alignSelf = Align.Center;
                rightNodeConfigVerticalContainer.Add(rightSelectedNodeTipLabelTitle);
            }
        }

        /// <summary>
        /// 给指定面板添加选中节点GUI显示
        /// </summary>
        /// <param name="nodeInspectorContainer"></param>
        /// <param name="selectedNodeView"></param>
        protected void CreateSelectedNodeInspector(VisualElement nodeInspectorContainer, NodeView selectedNodeView)
        {
            if(nodeInspectorContainer == null || selectedNodeView == null)
            {
                Debug.Log($"不允许给空容器或空显示节点创建选中节点GUI显示！");
                return;
            }
            mTempBindPropertyDataMap.Clear();
            var inspectorUIElement = selectedNodeView.CreateInspectorGUIElement(mTempBindPropertyDataMap);
            nodeInspectorContainer.Add(inspectorUIElement);
        }

        /// <summary>
        /// 创建黑板UI面板
        /// </summary>
        protected void CreateBlackboardUI()
        {
            var rightPanelVerticalContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.RightPanelVerticalContainerName);
            var rightVerticalContentLabelTitle = new Label();
            rightVerticalContentLabelTitle.text = "黑板面板";
            rightVerticalContentLabelTitle.style.height = 20f;
            rightVerticalContentLabelTitle.style.alignSelf = Align.Center;
            rightPanelVerticalContainer.Add(rightVerticalContentLabelTitle);

            CreateBlackboardOperationUI();
            CreateBlackboardDataUI();
        }

        /// <summary>
        /// 创建黑板操作UI
        /// </summary>
        protected void CreateBlackboardOperationUI()
        {
            var rightPanelVerticalContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.RightPanelVerticalContainerName);
            var blackboardOperationHorizontalContainer1 = UIToolkitUtilities.CreateHorizontalContainer(BTGraphElementNames.BlackboardOperationHorizontalContainer1Name, 0f);
            blackboardOperationHorizontalContainer1.style.height = 20f;

            var blackboardLabel = new Label();
            blackboardLabel.text = "变量名:";
            blackboardLabel.style.width = 60f;
            blackboardOperationHorizontalContainer1.Add(blackboardLabel);

            var blackboardVariableInputText = new TextField();
            blackboardVariableInputText.name = BTGraphElementNames.BlackboardInputVariableTextName;
            blackboardVariableInputText.value = string.Empty;
            blackboardVariableInputText.style.flexGrow = 1f;
            blackboardOperationHorizontalContainer1.Add(blackboardVariableInputText);

            rightPanelVerticalContainer.Add(blackboardOperationHorizontalContainer1);

            var blackboardOperationHorizontalContainer2 = UIToolkitUtilities.CreateHorizontalContainer(BTGraphElementNames.BlackboardOperationHorizontalContainer2Name, 0f);
            blackboardOperationHorizontalContainer2.style.height = 20f;

            var blackboardVariableTypeLabel = new Label();
            blackboardVariableTypeLabel.text = "变量类型:";
            blackboardVariableTypeLabel.style.width = 60f;
            blackboardOperationHorizontalContainer2.Add(blackboardVariableTypeLabel);

            PopupField<BlackboardDataType> variableTypePopField = new PopupField<BlackboardDataType>(mAvalibleVariableTypeList, mAvalibleVariableTypeList[0]);
            variableTypePopField.name = BTGraphElementNames.BlackboardVariableTypePopName;
            variableTypePopField.style.flexGrow = 1f;
            blackboardOperationHorizontalContainer2.Add(variableTypePopField);

            var addVariableDataButton = new Button();
            addVariableDataButton.name = BTGraphElementNames.BlackboardAddVariableDataButtonName;
            addVariableDataButton.text = "+";
            addVariableDataButton.style.width = 40f;
            addVariableDataButton.RegisterCallback<ClickEvent>(OnBlackboardAddVariableDataClick);
            blackboardOperationHorizontalContainer2.Add(addVariableDataButton);

            rightPanelVerticalContainer.Add(blackboardOperationHorizontalContainer2);
        }

        /// <summary>
        /// 响应添加黑板变量数据按钮点击
        /// </summary>
        /// <param name="evt"></param>
        private void OnBlackboardAddVariableDataClick(ClickEvent evt)
        {
            Debug.Log($"响应黑板添加变量数据按钮点击！");
            var blackboardVariableInputText = rootVisualElement.Q<TextField>(BTGraphElementNames.BlackboardInputVariableTextName);
            var newVariableName = blackboardVariableInputText.value;
            if (string.IsNullOrEmpty(newVariableName))
            {
                Debug.LogError($"不允许添加空变量名的黑板数据！");
                return;
            }
            Undo.RecordObject(mGraphView.SourceGraphData, "AddBlackboardData");
            PopupField<BlackboardDataType> variableTypePopField = rootVisualElement.Q<PopupField<BlackboardDataType>>(BTGraphElementNames.BlackboardVariableTypePopName);
            var newVariableType = variableTypePopField.value;
            if(mGraphView.SourceGraphData.BlackboardData.AddDataByTyoe(newVariableType, newVariableName))
            {
                UpdateBlackboardDataUI();
                EditorUtility.SetDirty(mGraphView.SourceGraphData);
            }
        }

        /// <summary>
        /// 创建黑板数据UI
        /// </summary>
        protected void CreateBlackboardDataUI()
        {
            var rightPanelVerticalContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.RightPanelVerticalContainerName);
            var blackboardDataVerticalContainer = UIToolkitUtilities.CreateVerticalContainer(BTGraphElementNames.BlackboardDataVerticalContainerName);
            rightPanelVerticalContainer.Add(blackboardDataVerticalContainer);

            UpdateBlackboardDataUI();
        }

        /// <summary>
        /// 更新黑板数据UI
        /// </summary>
        protected void UpdateBlackboardDataUI()
        {
            if (mCurrentSelectedPanelType != PanelType.BLACKBOARD_PANEL)
            {
                return;
            }
            var blackboardDataVerticalContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.BlackboardDataVerticalContainerName);
            blackboardDataVerticalContainer.Clear();
            foreach (var variabletype in mAvalibleVariableTypeList)
            {
                var variableTypeFoldOut = new Foldout();
                var variableTypeTitle = BTGraphUtilitiesEditor.GetVariableTypeFoldTitle(variabletype);
                variableTypeFoldOut.name = variableTypeTitle;
                variableTypeFoldOut.viewDataKey = BTGraphUtilitiesEditor.GetVariableTypeFoldOutViewDataKeyName(variabletype);
                variableTypeFoldOut.text = variableTypeTitle;
                variableTypeFoldOut.AddToClassList("unity-box");

                blackboardDataVerticalContainer.Add(variableTypeFoldOut);
                CreateVariableDataUI(variabletype);
            }
        }

        /// <summary>
        /// 创建指定变量类型的变量数据UI
        /// </summary>
        /// <param name="variableType"></param>
        private void CreateVariableDataUI(BlackboardDataType variableType)
        {
            if(!Application.isPlaying)
            {
                CreateEditorVariableDataUI(variableType);
            }
            else
            {
                CreateRuntimeVariableDataUI(variableType);
            }
        }

        /// <summary>
        /// 创建指定黑板数据类型的编辑器黑板数据UI显示
        /// </summary>
        /// <param name="variableType"></param>
        private void CreateEditorVariableDataUI(BlackboardDataType variableType)
        {
            if (mGraphView != null)
            {
                var variableTypeTitle = BTGraphUtilitiesEditor.GetVariableTypeFoldTitle(variableType);
                var variabletypeFoldOut = rootVisualElement.Q<Foldout>(variableTypeTitle);
                var blackboardData = mGraphView.SourceGraphData.BlackboardData;
                if (variableType == BlackboardDataType.Bool)
                {
                    for (int i = 0; i < blackboardData.AllBoolDataList.Count; i++)
                    {
                        var boolVariableDataUI = CreateOneBoolVariableDataUI(blackboardData.AllBoolDataList[i], UpdateEditorVariableData<bool>, RemoveEditorVariableData<bool>);
                        variabletypeFoldOut.Add(boolVariableDataUI);
                    }
                }
                else if (variableType == BlackboardDataType.Int)
                {
                    for (int i = 0; i < blackboardData.AllIntDataList.Count; i++)
                    {
                        var intVariableDataUI = CreateOneIntVariableDataUI(blackboardData.AllIntDataList[i], UpdateEditorVariableData<int>, RemoveEditorVariableData<int>);
                        variabletypeFoldOut.Add(intVariableDataUI);
                    }
                }
                else if (variableType == BlackboardDataType.Float)
                {
                    for (int i = 0; i < blackboardData.AllFloatDataList.Count; i++)
                    {
                        var floatVariableDataUI = CreateOneFloatVariableDataUI(blackboardData.AllFloatDataList[i], UpdateEditorVariableData<float>, RemoveEditorVariableData<float>);
                        variabletypeFoldOut.Add(floatVariableDataUI);
                    }
                }
                else if (variableType == BlackboardDataType.String)
                {
                    for (int i = 0; i < blackboardData.AllStringDataList.Count; i++)
                    {
                        var stringVariableDataUI = CreateOneStringVariableDataUI(blackboardData.AllStringDataList[i], UpdateEditorVariableData<string>, RemoveEditorVariableData<string>);
                        variabletypeFoldOut.Add(stringVariableDataUI);
                    }
                }
            }
        }

        /// <summary>
        /// 临时黑板数据Key列表(用于优化GC)
        /// </summary>
        private static List<string> TempBlackboardKeyList = new List<string>();

        /// <summary>
        /// 创建指定黑板数据类型的运行时黑板数据UI显示
        /// </summary>
        /// <param name="variableType"></param>
        private void CreateRuntimeVariableDataUI(BlackboardDataType variableType)
        {
            if (mGraphView != null && mGraphView.SourceGraphData.Blackboard != null)
            {
                var variableTypeTitle = BTGraphUtilitiesEditor.GetVariableTypeFoldTitle(variableType);
                var variabletypeFoldOut = rootVisualElement.Q<Foldout>(variableTypeTitle);
                if (variableType == BlackboardDataType.Bool)
                {
                    mGraphView.SourceGraphData.Blackboard.GetAllBlackboardKeyList<bool>(ref TempBlackboardKeyList); ;
                    for (int i = 0; i < TempBlackboardKeyList.Count; i++)
                    {
                        var blackboardDataKey = TempBlackboardKeyList[i];
                        var blackboardData = mGraphView.SourceGraphData.Blackboard.GetBlackboardData(blackboardDataKey) as BlackboardData<bool>;
                        var boolVariableDataUI = CreateOneBoolVariableDataUI(blackboardData, UpdateRuntimeVariableData<bool>, RemoveRuntimeVariableData<bool>);
                        variabletypeFoldOut.Add(boolVariableDataUI);
                    }
                }
                else if (variableType == BlackboardDataType.Int)
                {
                    mGraphView.SourceGraphData.Blackboard.GetAllBlackboardKeyList<int>(ref TempBlackboardKeyList); ;
                    for (int i = 0; i < TempBlackboardKeyList.Count; i++)
                    {
                        var blackboardDataKey = TempBlackboardKeyList[i];
                        var blackboardData = mGraphView.SourceGraphData.Blackboard.GetBlackboardData(blackboardDataKey) as BlackboardData<int>;
                        var boolVariableDataUI = CreateOneIntVariableDataUI(blackboardData, UpdateRuntimeVariableData<int>, RemoveRuntimeVariableData<int>);
                        variabletypeFoldOut.Add(boolVariableDataUI);
                    }
                }
                else if (variableType == BlackboardDataType.Float)
                {
                    mGraphView.SourceGraphData.Blackboard.GetAllBlackboardKeyList<float>(ref TempBlackboardKeyList); ;
                    for (int i = 0; i < TempBlackboardKeyList.Count; i++)
                    {
                        var blackboardDataKey = TempBlackboardKeyList[i];
                        var blackboardData = mGraphView.SourceGraphData.Blackboard.GetBlackboardData(blackboardDataKey) as BlackboardData<float>;
                        var boolVariableDataUI = CreateOneFloatVariableDataUI(blackboardData, UpdateRuntimeVariableData<float>, RemoveRuntimeVariableData<float>);
                        variabletypeFoldOut.Add(boolVariableDataUI);
                    }
                }
                else if (variableType == BlackboardDataType.String)
                {
                    mGraphView.SourceGraphData.Blackboard.GetAllBlackboardKeyList<string>(ref TempBlackboardKeyList); ;
                    for (int i = 0; i < TempBlackboardKeyList.Count; i++)
                    {
                        var blackboardDataKey = TempBlackboardKeyList[i];
                        var blackboardData = mGraphView.SourceGraphData.Blackboard.GetBlackboardData(blackboardDataKey) as BlackboardData<string>;
                        var boolVariableDataUI = CreateOneStringVariableDataUI(blackboardData, UpdateRuntimeVariableData<string>, RemoveRuntimeVariableData<string>);
                        variabletypeFoldOut.Add(boolVariableDataUI);
                    }
                }
            }
        }

        /// <summary>
        /// 添加指定黑板数据类型和黑板Key的编辑器黑板数据
        /// </summary>
        /// <param name="variableType"></param>
        /// <param name="variableName"></param>
        /// <returns></returns>
        private bool AddEditorVariableData(BlackboardDataType variableType, string variableName)
        {
            var result = mGraphView != null ? mGraphView.SourceGraphData.BlackboardData.AddDataByTyoe(variableType, variableName) : false;
            return result;
        }

        /// <summary>
        /// 更新指定黑板变量和值的编辑器黑板数据值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="variableName"></param>
        /// <param name="newValue"></param>
        /// <returns></returns>
        private bool UpdateEditorVariableData<T>(string variableName, T newValue)
        {
            Debug.Log($"编辑器变量类型:{typeof(T).Name}的变量名:{variableName}值更新:{newValue}");
            Undo.RecordObject(mGraphView.SourceGraphData, "UpdateBlackboardData");
            var result = mGraphView.SourceGraphData.BlackboardData.UpdaetValue<T>(variableName, newValue);
            EditorUtility.SetDirty(mGraphView.SourceGraphData);
            return result;
        }

        /// <summary>
        /// 移除指定黑板变量编辑器黑板数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="variableName"></param>
        /// <returns></returns>
        private bool RemoveEditorVariableData<T>(string variableName)
        {
            Debug.Log($"编辑器删除变量类型:{typeof(T).Name}的变量名:{variableName}数据！");
            Undo.RecordObject(mGraphView.SourceGraphData, "RemoveBlackboardData");
            var result = mGraphView.SourceGraphData.BlackboardData.RemoveData<T>(variableName);
            UpdateBlackboardDataUI();
            EditorUtility.SetDirty(mGraphView.SourceGraphData);
            return result;
        }

        /// <summary>
        /// 添加指定黑板数据类型和黑板Key的运行时黑板数据
        /// </summary>
        /// <param name="variableType"></param>
        /// <param name="variableName"></param>
        /// <returns></returns>
        private bool AddRuntimeVariableData(BlackboardDataType variableType, string variableName)
        {
            var result = false;
            if(variableType == BlackboardDataType.Bool)
            {
                result = mGraphView != null ? mGraphView.SourceGraphData.Blackboard.UpdateData<bool>(variableName) : false;
            }
            else if (variableType == BlackboardDataType.Int)
            {
                result = mGraphView != null ? mGraphView.SourceGraphData.Blackboard.UpdateData<int>(variableName) : false;
            }
            else if (variableType == BlackboardDataType.Float)
            {
                result = mGraphView != null ? mGraphView.SourceGraphData.Blackboard.UpdateData<float>(variableName) : false;
            }
            else if (variableType == BlackboardDataType.String)
            {
                result = mGraphView != null ? mGraphView.SourceGraphData.Blackboard.UpdateData<string>(variableName) : false;
            }
            return result;
        }

        /// <summary>
        /// 更新指定黑板变量和值的运行时黑板数据值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="variableName"></param>
        /// <param name="newValue"></param>
        /// <returns></returns>
        private bool UpdateRuntimeVariableData<T>(string variableName, T newValue)
        {
            Debug.Log($"运行时变量类型:{typeof(T).Name}的变量名:{variableName}值更新:{newValue}");
            Undo.RecordObject(mGraphView.SourceGraphData, "UpdateBlackboardData");
            var result = mGraphView.SourceGraphData.Blackboard.UpdateData<T>(variableName, newValue);
            EditorUtility.SetDirty(mGraphView.SourceGraphData);
            return result;
        }

        /// <summary>
        /// 移除指定黑板变量运行时黑板数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="variableName"></param>
        /// <returns></returns>
        private bool RemoveRuntimeVariableData<T>(string variableName)
        {
            Debug.Log($"运行时删除变量类型:{typeof(T).Name}的变量名:{variableName}数据！");
            Undo.RecordObject(mGraphView.SourceGraphData, "RemoveBlackboardData");
            var result = mGraphView.SourceGraphData.Blackboard.RemoveData(variableName);
            EditorUtility.SetDirty(mGraphView.SourceGraphData);
            return result;
        }

        /// <summary>
        /// 创建一个bool变量类型数据显示组件
        /// </summary>
        /// <param name="variableData"></param>
        /// <returns></returns>
        private VisualElement CreateOneBoolVariableDataUI(BlackboardData<bool> variableData,
                                                            Func<string, bool, bool> updateDataDelegate = null,
                                                                Func<string, bool> removeDataDelegate = null)
        {
            var variableDataHorizontalContainer = UIToolkitUtilities.CreateHorizontalContainer(null, 0);
            variableDataHorizontalContainer.style.height = 20f;
            var titleLabel = new Label();
            titleLabel.text = $"变量名:{variableData.Name}";
            titleLabel.style.width = 120f;
            variableDataHorizontalContainer.Add(titleLabel);

            var variableValueToggle = new Toggle();
            variableValueToggle.value = variableData.Data;
            variableValueToggle.RegisterValueChangedCallback((evt) =>
            {
                Debug.Log($"变量类型:Bool的变量名:{variableData.Name}值更新:{evt.newValue}");
                Undo.RecordObject(mGraphView.SourceGraphData, "UpdateBlackboardData");
                mGraphView.SourceGraphData.BlackboardData.UpdaetValue<bool>(variableData.Name, evt.newValue);
                EditorUtility.SetDirty(mGraphView.SourceGraphData);
            });
            variableValueToggle.style.flexGrow = 1f;
            variableDataHorizontalContainer.Add(variableValueToggle);

            var deleteButton = new Button();
            deleteButton.text = "-";
            deleteButton.style.width = 40f;
            deleteButton.RegisterCallback<ClickEvent>((evt) =>
            {
                Debug.Log($"删除变量类型:Bool的变量名:{variableData.Name}数据！");
                Undo.RecordObject(mGraphView.SourceGraphData, "RemoveBlackboardData");
                mGraphView.SourceGraphData.BlackboardData.RemoveData<bool>(variableData.Name);
                UpdateBlackboardDataUI();
                EditorUtility.SetDirty(mGraphView.SourceGraphData);
            });
            variableDataHorizontalContainer.Add(deleteButton);

            return variableDataHorizontalContainer;
        }

        /// <summary>
        /// 创建一个int变量类型数据显示组件
        /// </summary>
        /// <param name="variableData">黑板变量数据</param>
        /// <param name="updateDataDelegate">黑板数据更新委托</param>
        /// <param name="removeDataDelegate">黑板数据删除委托</param>
        /// <returns></returns>
        private VisualElement CreateOneIntVariableDataUI(BlackboardData<int> variableData,
                                                            Func<string, int, bool> updateDataDelegate = null,
                                                                Func<string, bool> removeDataDelegate = null)
        {
            var variableDataHorizontalContainer = UIToolkitUtilities.CreateHorizontalContainer(null, 0);
            variableDataHorizontalContainer.style.height = 20f;
            var titleLabel = new Label();
            titleLabel.text = $"变量名:{variableData.Name}";
            titleLabel.style.width = 120f;
            variableDataHorizontalContainer.Add(titleLabel);

            var variableValueIntField = new IntegerField();
            variableValueIntField.value = variableData.Data;
            variableValueIntField.RegisterValueChangedCallback((evt) =>
            {
                if(updateDataDelegate != null)
                {
                    updateDataDelegate(variableData.Name, evt.newValue);
                }
            });
            variableValueIntField.style.flexGrow = 1f;
            variableDataHorizontalContainer.Add(variableValueIntField);

            var deleteButton = new Button();
            deleteButton.text = "-";
            deleteButton.style.width = 40f;
            deleteButton.RegisterCallback<ClickEvent>((evt) =>
            {
                if(removeDataDelegate != null)
                {
                    removeDataDelegate(variableData.Name);
                }
            });
            variableDataHorizontalContainer.Add(deleteButton);

            return variableDataHorizontalContainer;
        }

        /// <summary>
        /// 创建一个float变量类型数据显示组件
        /// </summary>
        /// <param name="variableData"></param>
        /// <returns></returns>
        private VisualElement CreateOneFloatVariableDataUI(BlackboardData<float> variableData,
                                                            Func<string, float, bool> updateDataDelegate = null,
                                                                Func<string, bool> removeDataDelegate = null)
        {
            var variableDataHorizontalContainer = UIToolkitUtilities.CreateHorizontalContainer(null, 0);
            variableDataHorizontalContainer.style.height = 20f;
            var titleLabel = new Label();
            titleLabel.text = $"变量名:{variableData.Name}";
            titleLabel.style.width = 120f;
            variableDataHorizontalContainer.Add(titleLabel);

            var variableValueFloatField = new FloatField();
            variableValueFloatField.value = variableData.Data;
            variableValueFloatField.RegisterValueChangedCallback((evt) =>
            {
                Debug.Log($"变量类型:Float的变量名:{variableData.Name}值更新:{evt.newValue}");
                Undo.RecordObject(mGraphView.SourceGraphData, "UpdateBlackboardData");
                mGraphView.SourceGraphData.BlackboardData.UpdaetValue<float>(variableData.Name, evt.newValue);
                EditorUtility.SetDirty(mGraphView.SourceGraphData);
            });
            variableValueFloatField.style.flexGrow = 1f;
            variableDataHorizontalContainer.Add(variableValueFloatField);

            var deleteButton = new Button();
            deleteButton.text = "-";
            deleteButton.style.width = 40f;
            deleteButton.RegisterCallback<ClickEvent>((evt) =>
            {
                Debug.Log($"删除变量类型:Float的变量名:{variableData.Name}数据！");
                Undo.RecordObject(mGraphView.SourceGraphData, "RemoveBlackboardData");
                mGraphView.SourceGraphData.BlackboardData.RemoveData<float>(variableData.Name);
                UpdateBlackboardDataUI();
                EditorUtility.SetDirty(mGraphView.SourceGraphData);
            });
            variableDataHorizontalContainer.Add(deleteButton);

            return variableDataHorizontalContainer;
        }

        /// <summary>
        /// 创建一个string变量类型数据显示组件
        /// </summary>
        /// <param name="variableData"></param>
        /// <returns></returns>
        private VisualElement CreateOneStringVariableDataUI(BlackboardData<string> variableData,
                                                                Func<string, string, bool> updateDataDelegate = null,
                                                                    Func<string, bool> removeDataDelegate = null)
        {
            var variableDataHorizontalContainer = UIToolkitUtilities.CreateHorizontalContainer(null, 0);
            variableDataHorizontalContainer.style.height = 20f;
            var titleLabel = new Label();
            titleLabel.text = $"变量名:{variableData.Name}";
            titleLabel.style.width = 120f;
            variableDataHorizontalContainer.Add(titleLabel);

            var variableValueTextField = new TextField();
            variableValueTextField.value = variableData.Data;
            variableValueTextField.RegisterValueChangedCallback((evt) =>
            {
                Debug.Log($"变量类型:String的变量名:{variableData.Name}值更新:{evt.newValue}");
                Undo.RecordObject(mGraphView.SourceGraphData, "UpdateBlackboardData");
                mGraphView.SourceGraphData.BlackboardData.UpdaetValue<string>(variableData.Name, evt.newValue);
                EditorUtility.SetDirty(mGraphView.SourceGraphData);

            });
            variableValueTextField.style.flexGrow = 1f;
            variableDataHorizontalContainer.Add(variableValueTextField);

            var deleteButton = new Button();
            deleteButton.text = "-";
            deleteButton.style.width = 40f;
            deleteButton.RegisterCallback<ClickEvent>((evt) =>
            {
                Debug.Log($"删除变量类型:String的变量名:{variableData.Name}数据！");
                Undo.RecordObject(mGraphView.SourceGraphData, "RemoveBlackboardData");
                mGraphView.SourceGraphData.BlackboardData.RemoveData<string>(variableData.Name);
                UpdateBlackboardDataUI();
                EditorUtility.SetDirty(mGraphView.SourceGraphData);
            });
            variableDataHorizontalContainer.Add(deleteButton);

            return variableDataHorizontalContainer;
        }

        /// <summary>
        /// 创建不支持面板类型UI面板
        /// </summary>
        protected void CreateNotSupportPanelTypeUI()
        {
            var rightPanelVerticalContainer = rootVisualElement.Q<VisualElement>(BTGraphElementNames.RightPanelVerticalContainerName);
            var notSupportPanelTypeLabel = new Label();
            notSupportPanelTypeLabel.text = $"不支持的面板类型:{mCurrentSelectedPanelType.ToString()}";
            notSupportPanelTypeLabel.style.alignSelf = Align.Center;
            rightPanelVerticalContainer.Add(notSupportPanelTypeLabel);
        }

        /// <summary>
        /// 响应节点根节点设置变化
        /// </summary>
        /// <param name="nodeView"></param>
        protected void OnNodeEntryPointValueChange(NodeView nodeView)
        {
            var nodeData = nodeView.NodeData;
            Debug.Log($"响应节点GUID:{nodeData.GUID}的根节点设置变化到:{nodeData.EntryPoint}");
            nodeView.style.backgroundColor = BTGraphUtilitiesEditor.GetBackgroundColorByNodeData(nodeData);
            // 根节点不允许输入端连接任何节点
            if (nodeData.EntryPoint)
            {
                DisconnectAllInputPort(nodeView);
            }
        }

        /// <summary>
        /// 断开制定显示节点的所有Input端口连接
        /// </summary>
        /// <param name="nodeView"></param>
        protected void DisconnectAllInputPort(NodeView nodeView)
        {
            if (mGraphView == null)
            {
                Debug.LogError($"找不到有效GraphView，断开指定显示节点的所有Input端口连接失败！");
                return;
            }
            Debug.Log($"断开节点GUID:{nodeView.NodeData.GUID}的所有输入端口连接！");
            var inputPortList = nodeView.GetAllInputPorts();
            foreach (var inputPort in inputPortList)
            {
                // 需要Port.DisconnectAll()貌似不会自动移除相关边需要手动移除
                mGraphView.DeleteElements(inputPort.connections);
                inputPort.DisconnectAll();
            }
        }

        /// <summary>
        /// 断开制定显示节点的所有Output端口连接
        /// </summary>
        /// <param name="nodeView"></param>
        protected void DisconnectAllOutputPort(NodeView nodeView)
        {
            if (mGraphView == null)
            {
                Debug.LogError($"找不到有效GraphView，断开指定显示节点的所有Output端口连接失败！");
                return;
            }
            Debug.Log($"断开节点GUID:{nodeView.NodeData.GUID}的所有输出端口连接！");
            var outputPortList = nodeView.GetAllOutputPorts();
            foreach (var outputPort in outputPortList)
            {
                // 需要Port.DisconnectAll()貌似不会自动移除相关边需要手动移除
                mGraphView.DeleteElements(outputPort.connections);
                outputPort.DisconnectAll();
            }
        }

        /// <summary>
        /// 响应选择节点变化
        /// </summary>
        /// <param name="selectedNode"></param>
        protected void OnSelectedNodeChange(NodeView selectedNode)
        {
            Debug.Log($"UICommonGraphEditorWindow.OnSelectedNodeChange()响应选中节点变化！");
            UpdateSelectedPanelUI();
        }

        /// <summary>
        /// 更新选中图数据
        /// </summary>
        /// <param name="graphData"></param>
        protected void UpdateSelectedGraphData(BehaviourTreeGraphData graphData)
        {
            if(mGraphView == null)
            {
                Debug.Log($"未创建有效GraphView，清理之前的选中数据！");
                ClearSelectedGraphData();
                return;
            }
            if (mSelectedGraphData == graphData)
            {
                Debug.Log($"选中了相同GraphData，不重复加载!");
                return;
            }
            if (graphData != null)
            {
                mSelectedGraphData = graphData;
                mSelectedGraphAssetPath = mSelectedGraphData == null ? null : AssetDatabase.GetAssetPath(mSelectedGraphData);
                mGraphSaveFolderPath = string.IsNullOrEmpty(mSelectedGraphAssetPath) ? BTGraphConstEditor.DefaultGraphSaveFolderPath : Path.GetDirectoryName(mSelectedGraphAssetPath);
                mGraphSaveFileName = string.IsNullOrEmpty(mSelectedGraphAssetPath) ? BTGraphConstEditor.DefaultGraphSaveFileName : Path.GetFileName(mSelectedGraphAssetPath);
                mGraphView.LoadGraphData(mSelectedGraphData);
                UpdateAssetSelectionValue();
                UpdateSelectedPanelUI();
            }
        }

        /// <summary>
        /// 更新可调式选中图数据
        /// </summary>
        /// <param name="graphData"></param>
        protected void UpdateDebugSelectedGraphData(BehaviourTreeGraphData graphData)
        {
            if (mGraphView == null)
            {
                Debug.Log($"未创建有效GraphView，清理之前的选中数据！");
                ClearSelectedGraphData();
                return;
            }
            if (mSelectedGraphData == graphData)
            {
                Debug.Log($"选中了相同GraphData，不重复加载!");
                return;
            }
            if (graphData != null)
            {
                mSelectedGraphData = graphData;
                mGraphView.LoadGraphData(mSelectedGraphData);
                UpdateSelectedPanelUI();
            }
        }
    }
}