﻿/*
 * Description:             BaseCompareShareNode.cs
 * Author:                  TONYTANG
 * Create Date:             2023/10/04
 */

using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// BaseCompareShareNode.cs
    /// 比较黑板数据基类
    /// </summary>
    public abstract class BaseCompareShareNode : BaseObservingDecorationNode
    {
        /// <summary>
        /// 比较类型
        /// </summary>
        [Header("比较类型")]
        public OperatorType OperatorType = OperatorType.IS_EQUAL;

        /// <summary>
        /// 比较变量名
        /// </summary>
        [Header("比较变量名")]
        public string VariableName = "";

        /// <summary>
        /// 开始监听
        /// </summary>
        protected override void StartObserving()
        {
            mOwnerTreeData.Blackboard.AddObserver(VariableName, OnValueChanged);
        }

        /// <summary>
        /// 停止监听
        /// </summary>
        protected override void StopObserving()
        {
            mOwnerTreeData.Blackboard.RemoveObserver(VariableName, OnValueChanged);
        }

        /// <summary>
        /// 响应黑板监听变量名值变化
        /// </summary>
        /// <param name="operationType"></param>
        /// <param name="newValue"></param>
        private void OnValueChanged(Blackboard.BBOperationType operationType, object newValue)
        {
            Evaluate();
        }

        /// <summary>
        /// 条件是否满足
        /// </summary>
        protected override bool IsConditionMet()
        {
            if (OperatorType == OperatorType.ALWAYS_TRUE)
            {
                return true;
            }

            if (!mOwnerTreeData.Blackboard.ExistData(VariableName))
            {
                return OperatorType == OperatorType.IS_NOT_SET;
            }

            switch (OperatorType)
            {
                case OperatorType.IS_SET:
                    return true;
                case OperatorType.IS_EQUAL:
                    return IsValueEqual();
                case OperatorType.IS_NOT_EQUAL:
                    return !IsValueEqual();

                case OperatorType.IS_GREATER_OR_EQUAL:
                    return IsValueEqual() || IsValueGreater();
                case OperatorType.IS_GREATER:
                    return IsValueGreater();
                case OperatorType.IS_SMALLER_OR_EQUAL:
                    return IsValueEqual() || !IsValueGreater();
                case OperatorType.IS_SMALLER:
                    return !IsValueEqual() && !IsValueGreater();
                default:
                    return false;
            }
        }

        /// <summary>
        /// 值是否相等
        /// </summary>
        /// <returns></returns>
        protected abstract bool IsValueEqual();

        /// <summary>
        /// 值是否大于
        /// </summary>
        /// <returns></returns>
        protected abstract bool IsValueGreater();

        /// <summary>
        /// 字符串表达
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return $"OperatorType:{OperatorType},VariableName:{VariableName}";
        }
    }
}