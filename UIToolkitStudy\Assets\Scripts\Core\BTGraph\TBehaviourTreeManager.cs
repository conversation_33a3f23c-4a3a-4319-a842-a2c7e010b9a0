﻿/*
 * Description:     TBehaviourTreeManager.cs
 * Author:          TonyTang
 * Create Date:     2023/06/19
 */

using System;
using System.Collections.Generic;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// TBehaviourTreeManager.cs
    /// 行为树单例管理类
    /// </summary>
    public class TBehaviourTreeManager : SingletonTemplate<TBehaviourTreeManager>
    {
        // Node:
        // 通过延迟到下一帧Update里添加和删除的方案，确保行为树Update的正确更新逻辑

        /// <summary>
        /// 所有有效的行为树列表
        /// </summary>
        public List<TBehaviourTree> AllBehaviourTreeList
        {
            get;
            private set;
        }

        /// <summary>
        /// 等待添加的行为树Map
        /// </summary>
        private Dictionary<TBehaviourTree, TBehaviourTree> mWaitAddBehaviourTreeMap;

        /// <summary>
        /// 等待移除的行为树Map
        /// </summary>
        private Dictionary<TBehaviourTree, TBehaviourTree> mWaitRemoveBehaviourTreeMap;

        /// <summary>
        /// 是否暂停所有
        /// </summary>
        public bool IsPauseAll
        {
            get;
            set;
        }

        /// <summary>
        /// 行为树数据对象缓存Map(优化相同行为树Asset重复反序列化Json对象问题)
        /// Key为资源路径，Value为行为树BehaviourTreeGraohData原始数据对象
        /// </summary>
        private Dictionary<string, BehaviourTreeGraphData> mBTGraphCacheMap;

        public TBehaviourTreeManager()
        {
            AllBehaviourTreeList = new List<TBehaviourTree>();
            mWaitAddBehaviourTreeMap = new Dictionary<TBehaviourTree, TBehaviourTree>();
            mWaitRemoveBehaviourTreeMap = new Dictionary<TBehaviourTree, TBehaviourTree>();
            IsPauseAll = false;
            mBTGraphCacheMap = new Dictionary<string, BehaviourTreeGraphData>();
        }

        /// <summary>
        /// 清理所有行为树
        /// </summary>
        private void ClearAll()
        {
            Debug.Log($"TBehaviourTreeManager:ClearAll()");
            // TODO:入池重用
            AllBehaviourTreeList.Clear();
            ClearBTGraphCache();
        }

        /// <summary>
        /// 注册指定行为树对象
        /// </summary>
        /// <param name="behaviourTree"></param>
        /// <returns></returns>
        public bool RegisterTBehaviourTree(TBehaviourTree behaviourTree)
        {
            if(behaviourTree == null)
            {
                Debug.LogError($"不允许注册空行为树对象，注册失败！");
                return false;
            }
            if(IsWaitRemoveBehaviourTree(behaviourTree))
            {
                RemoveWaitRemoveBehaviourTree(behaviourTree);
            }
            return AddWaitAddBehaviourTree(behaviourTree);
        }

        /// <summary>
        /// 取消注册指定行为树对象
        /// </summary>
        /// <param name="behaviourTree"></param>
        /// <returns></returns>
        public bool UnregisterTBehaviourTree(TBehaviourTree behaviourTree)
        {
            if(behaviourTree == null)
            {
                Debug.LogError($"不允许取消注册空行为树对象，取消注册失败！");
                return false;
            }
            if(IsWaitAddBehaviourTree(behaviourTree))
            {
                RemoveWaitAddBehaviourTree(behaviourTree);
            }
            return AddWaitRemoveBehaviourTree(behaviourTree);
        }

        /// <summary>
        /// 暂停所有行为树
        /// </summary>
        public void PauseAll()
        {
            IsPauseAll = true;
            foreach(var bt in AllBehaviourTreeList)
            {
                bt.Pause();
            }
        }

        /// <summary>
        /// 继续所有行为树
        /// </summary>
        public void ResumeAll()
        {
            IsPauseAll = false;
            foreach (var bt in AllBehaviourTreeList)
            {
                bt.Resume();
            }
        }

        /// <summary>
        /// 打断所有行为树
        /// </summary>
        public void AbortAll()
        {
            foreach(var bt in AllBehaviourTreeList)
            {
                bt.Stop();
            }
        }

        /// <summary>
        /// 所有行为树定时器更新驱动
        /// </summary>
        /// <param name="deltaTime"></param>
        public void Update(float deltaTime)
        {
            DoAllWaitRemoveBehaviourTree();
            DoAllWaitAddBehaviourTree();
            UpdateAllBehaviourTree(deltaTime);
        }

        /// <summary>
        /// 执行移除所有待移除行为树
        /// </summary>
        private void DoAllWaitRemoveBehaviourTree()
        {
            foreach(var waitRemoveBehaviourTree in mWaitRemoveBehaviourTreeMap)
            {
                RemoveBehaviourTree(waitRemoveBehaviourTree.Key);
            }
        }

        /// <summary>
        /// 执行所有待添加行为树
        /// </summary>
        private void DoAllWaitAddBehaviourTree()
        {
            foreach (var waitAddBehaviourTree in mWaitAddBehaviourTreeMap)
            {
                var behaviourTree = waitAddBehaviourTree.Key;
                var result = AddBehaviourTree(behaviourTree);
                if(result && IsPauseAll)
                {
                    behaviourTree.Pause();
                }
            }
        }

        /// <summary>
        /// 更新所有行为树
        /// </summary>
        /// <param name="deltaTime"></param>
        private void UpdateAllBehaviourTree(float deltaTime)
        {
            if(IsPauseAll)
            {
                return;
            }
            for (int index = 0, length = AllBehaviourTreeList.Count; index < length; index++)
            {
                var behaviourTree = AllBehaviourTreeList[index];
                behaviourTree?.Update(deltaTime);
            }
        }

        /// <summary>
        /// 添加行为树
        /// </summary>
        /// <param name="behaviourTree"></param>
        /// <returns></returns>
        private bool AddBehaviourTree(TBehaviourTree behaviourTree)
        {
            if (IsContainBehaviourTree(behaviourTree))
            {
                Debug.Log($"已包含行为树哈希值:{behaviourTree.GetHashCode()}，添加指定行为树对象失败！");
                return false;
            }
            AllBehaviourTreeList.Add(behaviourTree);
            return true;
        }

        /// <summary>
        /// 移除行为树
        /// </summary>
        /// <param name="behaviourTree"></param>
        /// <returns></returns>
        private bool RemoveBehaviourTree(TBehaviourTree behaviourTree)
        {
            if (!IsContainBehaviourTree(behaviourTree))
            {
                Debug.Log($"未包含行为树哈希值:{behaviourTree.GetHashCode()}，移除指定行为树对象失败！");
                return false;
            }
            AllBehaviourTreeList.Remove(behaviourTree);
            return true;
        }

        /// <summary>
        /// 是否包含指定行为树
        /// </summary>
        /// <param name="behaviourTree"></param>
        /// <returns></returns>
        private bool IsContainBehaviourTree(TBehaviourTree behaviourTree)
        {
            return AllBehaviourTreeList.Contains(behaviourTree);
        }

        /// <summary>
        /// 添加等待添加行为树
        /// </summary>
        /// <param name="behaviourTree"></param>
        /// <returns></returns>
        private bool AddWaitAddBehaviourTree(TBehaviourTree behaviourTree)
        {
            if(mWaitAddBehaviourTreeMap.ContainsKey(behaviourTree))
            {
                Debug.LogError($"不允许重复添加待添加行为树HashCode:{behaviourTree.GetHashCode()}，添加待添加行为树失败！");
                return false;
            }
            mWaitAddBehaviourTreeMap.Add(behaviourTree, behaviourTree);
            return true;
        }

        /// <summary>
        /// 移除等待添加行为树
        /// </summary>
        /// <param name="behaviourTree"></param>
        /// <returns></returns>
        private bool RemoveWaitAddBehaviourTree(TBehaviourTree behaviourTree)
        {
            if (!mWaitAddBehaviourTreeMap.ContainsKey(behaviourTree))
            {
                Debug.LogError($"找不到待添加行为树HashCode:{behaviourTree.GetHashCode()}，移除待添加行为树失败！");
                return false;
            }
            mWaitAddBehaviourTreeMap.Remove(behaviourTree);
            return true;
        }

        /// <summary>
        /// 是否包含等待添加行为树
        /// </summary>
        /// <param name="behaviourTree"></param>
        /// <returns></returns>
        private bool IsWaitAddBehaviourTree(TBehaviourTree behaviourTree)
        {
            return mWaitAddBehaviourTreeMap.ContainsKey(behaviourTree);
        }

        /// <summary>
        /// 添加等待移除行为树
        /// </summary>
        /// <param name="behaviourTree"></param>
        /// <returns></returns>
        private bool AddWaitRemoveBehaviourTree(TBehaviourTree behaviourTree)
        {
            if (mWaitRemoveBehaviourTreeMap.ContainsKey(behaviourTree))
            {
                Debug.LogError($"不允许重复添加待移除行为树HashCode:{behaviourTree.GetHashCode()}，添加待移除行为树失败！");
                return false;
            }
            mWaitRemoveBehaviourTreeMap.Add(behaviourTree, behaviourTree);
            return true;
        }

        /// <summary>
        /// 移除等待移除行为树
        /// </summary>
        /// <param name="behaviourTree"></param>
        /// <returns></returns>
        private bool RemoveWaitRemoveBehaviourTree(TBehaviourTree behaviourTree)
        {
            if (!mWaitRemoveBehaviourTreeMap.ContainsKey(behaviourTree))
            {
                Debug.LogError($"找不到待移除行为树HashCode:{behaviourTree.GetHashCode()}，移除待移除行为树失败！");
                return false;
            }
            mWaitRemoveBehaviourTreeMap.Remove(behaviourTree);
            return true;
        }

        /// <summary>
        /// 是否包含等待移除行为树
        /// </summary>
        /// <param name="behaviourTree"></param>
        /// <returns></returns>
        private bool IsWaitRemoveBehaviourTree(TBehaviourTree behaviourTree)
        {
            return mWaitRemoveBehaviourTreeMap.ContainsKey(behaviourTree);
        }

        #region 行为树BehaviourTreeGraphData缓存部分
        /// <summary>
        /// 缓存指定Asset路径的行为树BehaviourTreeGraphData数据
        /// </summary>
        /// <param name="assetPath"></param>
        /// <param name="btGraph"></param>
        /// <returns></returns>
        public bool CacheBTGraph(string assetPath, BehaviourTreeGraphData btGraph)
        {
            if(string.IsNullOrEmpty(assetPath) || btGraph == null)
            {
                Debug.LogError($"不允许缓存空Asset路径或空BehaviourTreeGraphData对象，缓存失败！");
                return false;
            }
            if(!mBTGraphCacheMap.ContainsKey(assetPath))
            {
                mBTGraphCacheMap.Add(assetPath, btGraph);
                Debug.Log($"缓存行为树Asset:{assetPath}的BehaviourTreeGraphData对象成功！");
                return true;
            }
            else
            {
                Debug.LogError($"重复缓存行为树Asset:{assetPath}的BehaviourTreeGraphData对象，请检查代码！");
                return false;
            }
        }

        /// <summary>
        /// 获取指定行为树Asset路径的BehaviourTreeGraphData数据
        /// </summary>
        /// <param name="assetPath"></param>
        /// <returns></returns>
        public BehaviourTreeGraphData GetCacheBTGraph(string assetPath)
        {
            BehaviourTreeGraphData btGraph = null;
            mBTGraphCacheMap.TryGetValue(assetPath, out btGraph);
            return btGraph;
        }

        /// <summary>
        /// 清理行为树反序列化缓存Cache(建议切场景时调用，方便释放老的行为树反序列化数据)
        /// </summary>
        public void ClearBTGraphCache()
        {
            // TODO: 释放相关资源
            mBTGraphCacheMap.Clear();
        }
        #endregion
    }
}
