﻿/*
 * Description:     ActiveNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/07/03
 */

using System;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// ActiveNode.cs
    /// 激活条件节点
    /// </summary>
    [Serializable]
    public class ActiveNode : BaseConditionNode
    {
        /// <summary>
        /// 是否激活
        /// </summary>
        [Header("是否激活")]
        [SerializeReference]
        public bool IsActive;

        /// <summary>
        /// 执行开始
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            var actor = mOwnerTreeData.OwnerGraphData.OwnerBT.GetBindActor();
            if(actor == null)
            {
                var bindUID = mOwnerTreeData.OwnerGraphData.OwnerBT.BindUID;
                Debug.LogError($"找不到UID:{bindUID}的对象，激活检查失败！");
                Stop(false);
                return;
            }
            var isActive = actor.GO.activeSelf;
            var result = isActive == IsActive;
            Stop(result);
        }
    }
}