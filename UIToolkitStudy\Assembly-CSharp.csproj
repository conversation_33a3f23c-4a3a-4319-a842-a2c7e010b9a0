﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>9.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{baad0588-286b-af06-ce4d-45b8f04bd48d}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE;UNITY_2021_3_4;UNITY_2021_3;UNITY_2021;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_INCLUDE_TESTS;USE_SEARCH_ENGINE_API;USE_SEARCH_TABLE;USE_SEARCH_MODULE;USE_PROPERTY_DATABASE;USE_SEARCH_EXTENSION_API;SCENE_TEMPLATE_MODULE;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_RUNTIME_PERMISSIONS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;PLATFORM_ANDROID;TEXTCORE_1_0_OR_NEWER;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;UNITY_CAN_SHOW_SPLASH_SCREEN;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;ENABLE_ETC_COMPRESSION;PLATFORM_EXTENDS_VULKAN_DEVICE;PLATFORM_HAS_MULTIPLE_SWAPCHAINS;UNITY_ANDROID_SUPPORTS_SHADOWFILES;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;ENABLE_PROFILER;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_IG;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
     <Compile Include="Assets\Scripts\Utilities\PathUtilities.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\BaseCompareShareNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\BehaviourTreeGraphData.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\ConditionNodes\ActiveNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\BaseConditionNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\DecorationNodes\CompareShareBoolNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\DecorationNodes\CompareShareStringNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\DecorationNodes\HealthValueNode.cs" />
     <Compile Include="Assets\Scripts\Extension\ScriptableObjectExtension.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\NodeState.cs" />
     <Compile Include="Assets\Scripts\Core\CommonGraph\Blackboard.cs" />
     <Compile Include="Assets\Scripts\Utilities\FolderUtilities.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\NodePortData.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\DecorationNodes\CompareShareFloatNode.cs" />
     <Compile Include="Assets\Scripts\Logic\GameLuancher.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\EdgeData.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\Clock.cs" />
     <Compile Include="Assets\Scripts\Core\Utilities\TypeCache.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\ActionNodes\DialogueNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\ActionNodes\SetShareFloatNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\DecorationNodes\InverterNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\BaseParentNode.cs" />
     <Compile Include="Assets\Scripts\Core\Pool\ObjectPool.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\DecorationNodes\ActorOutRangeNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\ActionNodes\ChasePlayerNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\ComparisonType.cs" />
     <Compile Include="Assets\Scripts\Core\Singleton\SingletonMonoBehaviourTemplate.cs" />
     <Compile Include="Assets\Scripts\Core\Singleton\SingletonTemplate.cs" />
     <Compile Include="Assets\Scripts\Logic\Actor\PlayerActor.cs" />
     <Compile Include="Assets\Scripts\Core\GuideGraph\GuideGraphData.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\ActionNodes\WaitTimeNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\UnityContext.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\CommonGraphConst.cs" />
     <Compile Include="Assets\Scripts\Core\StoryGraph\StoryGraphData.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\RootNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\BTDebugger.cs" />
     <Compile Include="Assets\Scripts\Core\CommonGraph\BlackboardData.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\DecorationNodes\OperatorType.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\DecorationNodes\ActorInRangeNode.cs" />
     <Compile Include="Assets\Scripts\Logic\Actor\ActorManager.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\ActionNodes\SetShareStringNode.cs" />
     <Compile Include="Assets\Scripts\Utilities\FileUtilities.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\BaseDecorationNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\NodeType.cs" />
     <Compile Include="Assets\Scripts\Core\Utilities\TypeUtilities.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\CompositionNodes\RandomSelector.cs" />
     <Compile Include="Assets\Scripts\Logic\Actor\BaseActor.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\TBehaviourTree.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\BaseActionNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\TreeData.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\CompositionNodes\SelectorNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\ActionNodes\SetShareBoolNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\AbortType.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\DecorationNodes\CompareShareIntNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\DecorationNodes\RepeatedNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\BaseNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\BaseConditionDecorationNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\BaseSetShareNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\ActionNodes\LogNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\BaseObservingDecorationNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\CompositionNodes\SequenceNode.cs" />
     <Compile Include="Assets\Scripts\Logic\Actor\MonsterActor.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\ActionNodes\RandomMoveNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\ActionNodes\SetShareIntNode.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\TBehaviourTreeManager.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\BaseCompositionNode.cs" />
     <Compile Include="Assets\Scripts\Logic\Actor\ActorType.cs" />
     <Compile Include="Assets\Scripts\Core\BTGraph\CompositionNodes\ParalNode.cs" />
    <Reference Include="UnityEngine">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AutoStreamingModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AutoStreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsNativeModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsNativeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UNETModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PackageManagerUIModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.PackageManagerUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIServiceModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIServiceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Types">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.Net">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="System.Web">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Serialization">
        <HintPath>G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Updater">
        <HintPath>G:\Projects\GitHubProjects\UIToolkitStudy\UIToolkitStudy\Library\ScriptAssemblies\Unity.AI.Navigation.Updater.dll</HintPath>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Editor.ConversionSystem">
        <HintPath>G:\Projects\GitHubProjects\UIToolkitStudy\UIToolkitStudy\Library\ScriptAssemblies\Unity.AI.Navigation.Editor.ConversionSystem.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VSCode.Editor">
        <HintPath>G:\Projects\GitHubProjects\UIToolkitStudy\UIToolkitStudy\Library\ScriptAssemblies\Unity.VSCode.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
        <HintPath>G:\Projects\GitHubProjects\UIToolkitStudy\UIToolkitStudy\Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
        <HintPath>G:\Projects\GitHubProjects\UIToolkitStudy\UIToolkitStudy\Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
        <HintPath>G:\Projects\GitHubProjects\UIToolkitStudy\UIToolkitStudy\Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline">
        <HintPath>G:\Projects\GitHubProjects\UIToolkitStudy\UIToolkitStudy\Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
        <HintPath>G:\Projects\GitHubProjects\UIToolkitStudy\UIToolkitStudy\Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Editor">
        <HintPath>G:\Projects\GitHubProjects\UIToolkitStudy\UIToolkitStudy\Library\ScriptAssemblies\Unity.AI.Navigation.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.AI.Navigation">
        <HintPath>G:\Projects\GitHubProjects\UIToolkitStudy\UIToolkitStudy\Library\ScriptAssemblies\Unity.AI.Navigation.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UI">
        <HintPath>G:\Projects\GitHubProjects\UIToolkitStudy\UIToolkitStudy\Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
    </Reference>
    <Reference Include="Unity.PlasticSCM.Editor">
        <HintPath>G:\Projects\GitHubProjects\UIToolkitStudy\UIToolkitStudy\Library\ScriptAssemblies\Unity.PlasticSCM.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
        <HintPath>G:\Projects\GitHubProjects\UIToolkitStudy\UIToolkitStudy\Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UI">
        <HintPath>G:\Projects\GitHubProjects\UIToolkitStudy\UIToolkitStudy\Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
