﻿/*
 * Description:     BaseActionNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/06/19
 */

using System;

namespace TCommonGraph
{
    /// <summary>
    /// BaseActionNode.cs
    /// 行为树行为节点基类
    /// </summary>
    [Serializable]
    public abstract class BaseActionNode : BaseNode
    {
        /// <summary>
        /// 节点类型
        /// </summary>
        public override NodeType NodeType
        {
            get
            {
                return NodeType.Action;
            }
        }
    }
}