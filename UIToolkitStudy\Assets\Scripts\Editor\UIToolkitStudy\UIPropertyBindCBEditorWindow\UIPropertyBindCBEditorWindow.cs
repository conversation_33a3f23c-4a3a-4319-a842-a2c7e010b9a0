﻿/*
 * Description:     UIPropertyBindCBEditorWindow.cs
 * Author:          TonyTang
 * Create Date:     2023/06/05
 */

using UnityEditor;
using UnityEditor.UIElements;
using UnityEngine;
using UnityEngine.UIElements;

/// <summary>
/// UIPropertyBindCBEditorWindow.cs
/// 属性修改绑定窗口
/// </summary>
public class UIPropertyBindCBEditorWindow : EditorWindow
{
    /// <summary>
    /// 选中的ScriptableObject
    /// </summary>
    private ScriptableObject mSelectedScriptableObject;

    [MenuItem("Window/UI Toolkit/UIToolkitStudy/UIPropertyBindCBEditorWindow")]
    public static void ShowUIPropertyBindCBEditorWindow()
    {
        UIPropertyBindCBEditorWindow wnd = GetWindow<UIPropertyBindCBEditorWindow>();
        wnd.titleContent = new GUIContent("UIPropertyBindCBEditorWindow");
    }

    public void CreateGUI()
    {
        CreatePropertyBindCBUI();
        OnSelectionChange();
    }

    /// <summary>
    /// 选中Asset变化回调
    /// </summary>
    private void OnSelectionChange()
    {
        mSelectedScriptableObject = Selection.activeObject as ScriptableObject;
        var propertyBindAssetNameTextField = rootVisualElement.Q<TextField>(PropertyBindCBElementNames.PropertyBindCBTextFieldName);
        var serializedBindAssetNameTextField = rootVisualElement.Q<TextField>(PropertyBindCBElementNames.SerializedBindCBTextFieldName);
        if(mSelectedScriptableObject != null)
        {
            var selectedSerializedObject = new SerializedObject(mSelectedScriptableObject);
            if(propertyBindAssetNameTextField != null)
            {
                var nameProperty = selectedSerializedObject.FindProperty("Name");
                propertyBindAssetNameTextField.TrackPropertyValue(nameProperty, OnNamePropertyChange);
                propertyBindAssetNameTextField.BindProperty(nameProperty);
            }
            if(serializedBindAssetNameTextField != null)
            {
                serializedBindAssetNameTextField.TrackSerializedObjectValue(selectedSerializedObject, OnPropertyChange);
            }
        }
        else
        {
            if(propertyBindAssetNameTextField != null)
            {
                propertyBindAssetNameTextField.Unbind();
                propertyBindAssetNameTextField.value = string.Empty;
            }
            if(serializedBindAssetNameTextField != null)
            {
                serializedBindAssetNameTextField.Unbind();
            }
        }
    }

    /// <summary>
    /// 创建属性绑定回调UI
    /// </summary>
    private void CreatePropertyBindCBUI()
    {
        var propertyBindCBAssetInfoHContainer = new VisualElement();
        propertyBindCBAssetInfoHContainer.style.flexDirection = FlexDirection.Row;
        propertyBindCBAssetInfoHContainer.style.height = 20;
        rootVisualElement.Add(propertyBindCBAssetInfoHContainer);

        var proeprtyBindLabelTitle = new Label();
        proeprtyBindLabelTitle.text = "名字属性值:";
        proeprtyBindLabelTitle.style.width = 160;
        propertyBindCBAssetInfoHContainer.Add(proeprtyBindLabelTitle);

        var propertyBindAssetNameTextField = new TextField();
        propertyBindAssetNameTextField.name = PropertyBindCBElementNames.PropertyBindCBTextFieldName;
        propertyBindAssetNameTextField.style.flexGrow = 1;
        propertyBindAssetNameTextField.value = string.Empty;
        propertyBindCBAssetInfoHContainer.Add(propertyBindAssetNameTextField);

        var serializedObjectBindCBAssetInfoHContainer = new VisualElement();
        serializedObjectBindCBAssetInfoHContainer.style.flexDirection = FlexDirection.Row;
        serializedObjectBindCBAssetInfoHContainer.style.height = 20;
        rootVisualElement.Add(serializedObjectBindCBAssetInfoHContainer);

        var serializedBindLabelTitle = new Label();
        serializedBindLabelTitle.text = "属性变化SerializeObject名:";
        serializedBindLabelTitle.style.width = 160;
        serializedObjectBindCBAssetInfoHContainer.Add(serializedBindLabelTitle);

        var serializedBindAssetNameTextField = new TextField();
        serializedBindAssetNameTextField.name = PropertyBindCBElementNames.SerializedBindCBTextFieldName;
        serializedBindAssetNameTextField.style.flexGrow = 1;
        serializedBindAssetNameTextField.value = string.Empty;
        serializedObjectBindCBAssetInfoHContainer.Add(serializedBindAssetNameTextField);
    }

    /// <summary>
    /// 响应名字属性变化
    /// </summary>
    /// <param name="property"></param>
    private void OnNamePropertyChange(SerializedProperty property)
    {
        Debug.Log($"名字属性发生变化！");
    }

    /// <summary>
    /// 响应属性变化
    /// </summary>
    /// <param name="serializedObject"></param>
    private void OnPropertyChange(SerializedObject serializedObject)
    {
        Debug.Log($"SerializedObject.name:{serializedObject.targetObject.name}属性变化！");
        var serializedBindAssetNameTextField = rootVisualElement.Q<TextField>(PropertyBindCBElementNames.SerializedBindCBTextFieldName);
        if(serializedBindAssetNameTextField != null)
        {
            serializedBindAssetNameTextField.value = serializedObject.targetObject.name;
        }
    }
}