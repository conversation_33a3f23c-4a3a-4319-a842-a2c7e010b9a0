﻿/*
 * Description:     BaseObservingDecorationNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/06/19
 */

using System;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// BaseObservingDecorationNode.cs
    /// 可被观察条件打断的装饰节点基类
    /// </summary>
    [Serializable]
    public abstract class BaseObservingDecorationNode : BaseDecorationNode
    {
        /// <summary>
        /// 打断类型
        /// </summary>
        [Header("打断类型")]
        public AbortType AbortType;

        /// <summary>
        /// 是否正在观察
        /// </summary>
        protected bool mIsObserving;

        /// <summary>
        /// 响应开始
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            if (AbortType != AbortType.NONE)
            {
                if(!mIsObserving)
                {
                    mIsObserving = true;
                    StartObserving();
                }
            }
            if(!IsConditionMet())
            {
                Stop(false);
            }
            else
            {
                DecorateNode.Start();
            }
        }

        /// <summary>
        /// 响应终止
        /// </summary>
        protected override void DoAbort()
        {
            base.DoAbort();
            DecorateNode.Abort();
        }

        /// <summary>
        /// 响应子节点停止
        /// </summary>
        /// <param name="child"></param>
        /// <param name="success"></param>
        protected override void DoChildStop(BaseNode child, bool success)
        {
            base.DoChildStop(child, success);
            if(AbortType == AbortType.NONE || AbortType == AbortType.SELF)
            {
                if(mIsObserving)
                {
                    mIsObserving = false;
                    StopObserving();
                }
            }
            Stop(success);
        }

        /// <summary>
        /// 响应父组合节点停止
        /// </summary>
        /// <param name="parentNode"></param>
        protected override void DoParentCompositeStop(BaseCompositionNode parentNode)
        {
            base.DoParentCompositeStop(parentNode);
            if(mIsObserving)
            {
                mIsObserving = false;
                StopObserving();
            }
        }

        /// <summary>
        /// 评估条件流程
        /// </summary>
        protected void Evaluate()
        {
            if(IsRunning && !IsConditionMet())
            {
                if(AbortType == AbortType.SELF ||
                    AbortType == AbortType.BOTH ||
                    AbortType == AbortType.IMMEDIATE_RESTART)
                {
                    Abort();
                }
            }
            else if(!IsRunning && IsConditionMet())
            {
                if(AbortType == AbortType.LOWER_PRIORITY ||
                    AbortType == AbortType.BOTH ||
                    AbortType == AbortType.IMMEDIATE_RESTART ||
                    AbortType == AbortType.LOWER_PRIORITY_IMMEDIATE_RESTART)
                {
                    BaseParentNode parentNode = ParentNode;
                    BaseNode childNode = this;
                    while(parentNode != null && !(parentNode is BaseCompositionNode))
                    {
                        childNode = parentNode;
                        parentNode = parentNode.ParentNode;
                    }
                    if(parentNode is ParalNode)
                    {
                        if(AbortType != AbortType.NONE && AbortType != AbortType.IMMEDIATE_RESTART)
                        {
                            Debug.LogError($"ParalNode的所有子节点优先级一致，不支持配置AbortType.Node和AbortType.IMMEDIATE_RESTART以外打断类型,请检查配置！");
                        }
                    }
                    var isImmediateRestart = AbortType == AbortType.IMMEDIATE_RESTART || AbortType == AbortType.LOWER_PRIORITY_IMMEDIATE_RESTART;
                    var parentCompositionNode = parentNode as BaseCompositionNode;
                    parentCompositionNode?.StopLowerPriorityChildrenForChild(childNode, isImmediateRestart);
                }
            }
        }

        /// <summary>
        /// 开始条件监听流程
        /// </summary>
        protected abstract void StartObserving();

        /// <summary>
        /// 停止条件监听观察
        /// </summary>
        protected abstract void StopObserving();

        /// <summary>
        /// 条件是否满足
        /// </summary>
        protected abstract bool IsConditionMet();
    }
}
