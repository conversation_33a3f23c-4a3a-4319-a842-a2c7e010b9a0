﻿/*
 * Description:     EventHandleElementNames.cs
 * Author:          TonyT<PERSON>
 * Create Date:     2023/05/29
 */

/// <summary>
/// EventHandleElementNames.cs
/// 事件处理组件成员们
/// </summary>
public static class EventHandleElementNames
{
    /// <summary>
    /// 横向内容容器名字
    /// </summary>
    public const string HorizontalContentContainerName = "HorizontalContentContainer";

    /// <summary>
    /// 菜单栏名字
    /// </summary>
    public const string MenuToolBarName = "MenuToolBar";

    /// <summary>
    /// 菜单栏菜单名字
    /// </summary>
    public const string ToolBarMenuName = "ToolBarMenu";

    /// <summary>
    /// 左侧竖向容器名字
    /// </summary>
    public const string LeftVerticalContentContainerName = "LeftVerticalContentContainer";

    /// <summary>
    /// 中间竖向容器名字
    /// </summary>
    public const string MiddleGraphViewContainerName = "MiddleGraphViewContainer";

    /// <summary>
    /// 右侧竖向容器名字
    /// </summary>
    public const string RightVerticalContentContainerName = "RightVerticalContentContainer";

    /// <summary>
    /// 节点竖向容器名字
    /// </summary>
    public const string NodeVerticalContainerName = "NodeVerticalContainer";

    /// <summary>
    /// 条件节点折叠名字
    /// </summary>
    public const string ConditionNodeFoldOutName = "ConditionNodeFoldOut";

    /// <summary>
    /// 行为节点折叠名字
    /// </summary>
    public const string ActionNodeFoldOutName = "ActionNodeFoldOut";

    /// <summary>
    /// 保存路径文本名字
    /// </summary>
    public const string SavePathTextFieldName = "SavePathTextField";

    /// <summary>
    /// 导出按钮名字
    /// </summary>
    public const string ExportButtonName = "ExportButton";

    /// <summary>
    /// Action Item名字前缀
    /// </summary>
    public const string ActionItemPrefixName = "ActionType";

    /// <summary>
    /// Condition Item名字前缀
    /// </summary>
    public const string ConditionItemPrefixName = "ConditionType";
}