﻿/*
 * Description:             BaseCompareShareNodeView.cs
 * Author:                  TONYTANG
 * Create Date:             2023/10/05
 */

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;

namespace TCommonGraph
{
    /// <summary>
    /// BaseCompareShareNodeView.cs
    /// 黑板数据比较显示节点基类
    /// </summary>
    public class BaseCompareShareNodeView : BaseObservingDecorationNodeView
    {
        /// <summary>
        /// 创建节点自定义UI
        /// </summary>
        protected override void CreateCustomUI()
        {
            base.CreateCustomUI();
            var nodeVerticalUIContainer = this.Q<VisualElement>(BTGraphElementNames.NodeUIVerticalContainerName);
            var nodeDesDivider = UIToolkitUtilities.CreateHorizontalDivider(BTGraphConstEditor.DividerColor);
            nodeVerticalUIContainer.Add(nodeDesDivider);

            UIToolkitUtilities.CreateBindSOPropertyField(nodeVerticalUIContainer, NodeData, "OperatorType");
            UIToolkitUtilities.CreateBindSOPropertyField(nodeVerticalUIContainer, NodeData, "VariableName");
        }
    }
}