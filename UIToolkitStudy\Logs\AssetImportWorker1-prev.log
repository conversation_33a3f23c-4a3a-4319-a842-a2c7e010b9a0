Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.4f1c1 (64682593795a) revision 6580261'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Core' Language: 'zh' Physical Memory: 16302 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
G:/Projects/GitHubProjects/UIToolkitStudy/UIToolkitStudy
-logFile
Logs/AssetImportWorker1.log
-srvPort
56792
Successfully changed project path to: G:/Projects/GitHubProjects/UIToolkitStudy/UIToolkitStudy
G:/Projects/GitHubProjects/UIToolkitStudy/UIToolkitStudy
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 31.83 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.4f1c1 (64682593795a)
[Subsystems] Discovering subsystems at path G:/Software/UnityAllVersions/2021.3.4f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path G:/Projects/GitHubProjects/UIToolkitStudy/UIToolkitStudy/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1050 Ti (ID=0x1c82)
    Vendor:   NVIDIA
    VRAM:     4004 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'G:/Software/UnityAllVersions/2021.3.4f1c1/Editor/Data/Managed'
Mono path[1] = 'G:/Software/UnityAllVersions/2021.3.4f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'G:/Software/UnityAllVersions/2021.3.4f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56420
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: G:/Software/UnityAllVersions/2021.3.4f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: G:/Software/UnityAllVersions/2021.3.4f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005836 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 29.30 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.742 seconds
Domain Reload Profiling:
	ReloadAssembly (743ms)
		BeginReloadAssembly (90ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (506ms)
			LoadAssemblies (87ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (139ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (38ms)
			SetupLoadedEditorAssemblies (265ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (51ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (29ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (128ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.004669 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.32 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.357 seconds
Domain Reload Profiling:
	ReloadAssembly (1359ms)
		BeginReloadAssembly (132ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (1066ms)
			LoadAssemblies (86ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (250ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (68ms)
			SetupLoadedEditorAssemblies (568ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (21ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (0ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (421ms)
				ProcessInitializeOnLoadMethodAttributes (39ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (21ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.44 seconds
Refreshing native plugins compatible for Editor in 0.33 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1506 Unused Serialized files (Serialized files now loaded: 0)
Unloading 16 unused Assets / (55.5 KB). Loaded Objects now: 1979.
Memory consumption went from 67.4 MB to 67.3 MB.
Total: 2.997200 ms (FindLiveObjects: 0.158900 ms CreateObjectMapping: 0.072800 ms MarkObjects: 2.680900 ms  DeleteObjects: 0.083100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 75287.308660 seconds.
  path: Assets/Scripts/Editor/UIToolkitStudy/UIBTGraphEditorWindow/UIBTGraphEditorWindow.cs
  artifactKey: Guid(f8e0cf2a5979ef849892e77a95bc49c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Editor/UIToolkitStudy/UIBTGraphEditorWindow/UIBTGraphEditorWindow.cs using Guid(f8e0cf2a5979ef849892e77a95bc49c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1137503908b478e7215a4ac1a76911e6') in 0.105593 seconds 
========================================================================
Received Import Request.
  Time since last request: 256.253643 seconds.
  path: Assets/Resources/BTGraph/BTExecuteFlowIntro.asset
  artifactKey: Guid(9c07ac80d1cd2d241b7cbb0c84eb8d91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/BTGraph/BTExecuteFlowIntro.asset using Guid(9c07ac80d1cd2d241b7cbb0c84eb8d91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0320089dfb6ea05502b7aa95d561eea2') in 0.067040 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.010444 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.34 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.370 seconds
Domain Reload Profiling:
	ReloadAssembly (1371ms)
		BeginReloadAssembly (303ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (18ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (109ms)
		EndReloadAssembly (928ms)
			LoadAssemblies (130ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (285ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (67ms)
			SetupLoadedEditorAssemblies (409ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (31ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (0ms)
				BeforeProcessingInitializeOnLoad (63ms)
				ProcessInitializeOnLoadAttributes (277ms)
				ProcessInitializeOnLoadMethodAttributes (25ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.33 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1500 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9 unused Assets / (24.6 KB). Loaded Objects now: 1982.
Memory consumption went from 67.2 MB to 67.1 MB.
Total: 4.219800 ms (FindLiveObjects: 0.280900 ms CreateObjectMapping: 0.059600 ms MarkObjects: 3.830600 ms  DeleteObjects: 0.047300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 666.320647 seconds.
  path: Assets/Scripts/Editor/UIToolkitStudy/UIBTGraphEditorWindow/UIBTGraphEditorWindow.cs
  artifactKey: Guid(f8e0cf2a5979ef849892e77a95bc49c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Editor/UIToolkitStudy/UIBTGraphEditorWindow/UIBTGraphEditorWindow.cs using Guid(f8e0cf2a5979ef849892e77a95bc49c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a213cfb5aa971f3d8bbedcbc5c897b32') in 0.004579 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.008493 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.36 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.373 seconds
Domain Reload Profiling:
	ReloadAssembly (1375ms)
		BeginReloadAssembly (214ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (12ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (49ms)
		EndReloadAssembly (1005ms)
			LoadAssemblies (122ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (271ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (62ms)
			SetupLoadedEditorAssemblies (476ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (36ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (0ms)
				BeforeProcessingInitializeOnLoad (76ms)
				ProcessInitializeOnLoadAttributes (318ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (21ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.80 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1500 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9 unused Assets / (24.6 KB). Loaded Objects now: 1985.
Memory consumption went from 67.2 MB to 67.1 MB.
Total: 4.078300 ms (FindLiveObjects: 0.489200 ms CreateObjectMapping: 0.099300 ms MarkObjects: 3.434900 ms  DeleteObjects: 0.025000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.004668 seconds.
