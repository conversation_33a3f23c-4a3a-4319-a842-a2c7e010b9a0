﻿/*
 * Description:     NodePortData.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/06/19
 */

using System;
using System.Collections.Generic;
using UnityEditor.Experimental.GraphView;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// NodePortData.cs
    /// 节点端口数据
    /// </summary>
    public class NodePortData : IRecycle
    {
        /// <summary>
        /// 所属节点
        /// </summary>
        public BaseNode OwnerNode
        {
            get;
            private set;
        }

        /// <summary>
        /// 端口名
        /// </summary>
        public string PortName
        {
            get;
            private set;
        }

        /// <summary>
        /// Port类型
        /// </summary>
        public Direction PortDirection
        {
            get;
            private set;
        }

        /// <summary>
        /// 连接节点列表
        /// </summary>
        public List<BaseNode> ConnectedNodeList
        {
            get;
            private set;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public NodePortData()
        {
            ConnectedNodeList = new List<BaseNode>();
        }

        /// <summary>
        /// 出池
        /// </summary>
        public void OnCreate()
        {
            ResetData();
        }

        /// <summary>
        /// 入池
        /// </summary>
        public void OnDispose()
        {
            ResetData();
        }

        /// <summary>
        /// 重置数据
        /// </summary>
        private void ResetData()
        {
            OwnerNode = null;
            PortName = null;
            PortDirection = Direction.Input;
            ClearConnectedNodeData();
        }

        /// <summary>
        /// 设置数据
        /// </summary>
        /// <param name="ownerNode"></param>
        /// <param name="portName"></param>
        /// <param name="portDirection"></param>
        public void SetData(BaseNode ownerNode, string portName, Direction portDirection)
        {
            OwnerNode = ownerNode;
            PortName = portName;
            PortDirection = PortDirection;
        }

        /// <summary>
        /// 清除连接节点数据
        /// </summary>
        public void ClearConnectedNodeData()
        {
            ConnectedNodeList.Clear();
        }

        /// <summary>
        /// 添加连接节点
        /// </summary>
        /// <param name="node"></param>
        /// <returns></returns>
        public bool AddConnectNode(BaseNode node)
        {
            if (node == null)
            {
                Debug.LogError($"不允许添加空连接节点，所属节点GUID:{OwnerNode.GUID}的端口名:{PortName}添加连接节点失败！");
                return false;
            }
            if (ConnectedNodeList.Contains(node))
            {
                Debug.LogError($"所属节点GUID:{OwnerNode.GUID}，端口名:{PortName}，端口类型:{PortDirection}重复添加连接节点GUID:{node.GUID}，添加连接节点失败！");
                return false;
            }
            ConnectedNodeList.Add(node);
            return true;
        }
    }
}
