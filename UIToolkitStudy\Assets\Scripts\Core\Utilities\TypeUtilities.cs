﻿/*
 * Description:     TypeUtilities.cs
 * Author:          TonyTang
 * Create Date:     2023/07/05
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;
/// <summary>
/// TypeUtilities.cs
/// 类型静态工具类
/// </summary>
public static class TypeUtilities
{
    /// <summary>
    /// 当前类所在Assembly
    /// </summary>
    private static Assembly CurrentAssembly = typeof(TypeCache).Assembly;

    /// <summary>
    /// 当前Assemble所有类型信息数组
    /// </summary>
    private static Type[] CurrentAssemblyAllTypes = CurrentAssembly.GetTypes();

    /// <summary>
    /// 获取指定类型信息的所有子类型信息列表
    /// </summary>
    /// <param name="targetType"></param>
    /// <returns></returns>
    public static List<Type> GetAllSubTypes(Type targetType)
    {
        if (targetType == null)
        {
            return null;
        }
        if (targetType.Assembly != CurrentAssembly)
        {
            Debug.LogError($"不支持获取非同Assembly的字类型信息列表！当前Assembly名:{CurrentAssembly.FullName}，目标所在Assembly名:{targetType.Assembly.FullName}!");
            return null;
        }
        var subTypeList = CurrentAssemblyAllTypes.Where(type => type.IsSubclassOf(targetType)).ToList();
        return subTypeList;
    }
}