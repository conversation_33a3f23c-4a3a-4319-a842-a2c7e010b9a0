﻿/*
 * Description:     AbortType.cs
 * Author:          TonyTang
 * Create Date:     2023/11/10
 */

namespace TCommonGraph
{
    // Note:
    // 打断类型介绍(参考的NPBehave):
    // 1. NONE
    //      装饰器只会在启动时检查一次它的状态，并且永远不会停止任何正在运行的节点。
    // 2. SELF
    //      装饰器将在启动时检查一次它的条件状态，如果满足，它将继续观察黑板的变化。
    //      一旦不再满足该条件，它将终止自身，并让父组合继续处理它的下一个节点。
    // 3. LOWER_PRIORITY
    //      装饰器将在启动时检查它的状态，如果不满足，它将观察黑板的变化。
    //      一旦条件满足，它将停止比此结点优先级较低的节点，允许父组合继续处理下一个节点
    // 4. BOTH
    //      装饰器将同时停止:self和优先级较低的节点。
    // 5. LOWER_PRIORITY_IMMEDIATE_RESTART
    //      一旦启动，装饰器将检查它的状态，如果不满足，它将观察黑板的变化。
    //      一旦条件满足，它将停止优先级较低的节点，并命令父组合立即重启此装饰器。
    // 6. IMMEDIATE_RESTART
    //      一旦启动，装饰器将检查它的状态，如果不满足，它将观察黑板的变化。
    //      一旦条件满足，它将停止优先级较低的节点，并命令父组合立即重启装饰器。正如在这两种情况下，一旦不再满足条件，它也将停止自己。

    /// <summary>
    /// AbortType.cs
    /// 打断枚举类型
    /// </summary>
    public enum AbortType
    {
        NONE = 0,
        SELF,
        LOWER_PRIORITY,
        BOTH,
        LOWER_PRIORITY_IMMEDIATE_RESTART,
        IMMEDIATE_RESTART,
    }
}
