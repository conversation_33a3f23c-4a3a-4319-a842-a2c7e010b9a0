﻿/*
 * Description:     ParalNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/12/04
 */


using System;
using System.Collections.Generic;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// ParalNode.cs
    /// 行为树并发节点
    /// </summary>
    [Serializable]
    public class ParalNode : BaseCompositionNode
    {
        /// <summary>
        /// 并发策略
        /// </summary>
        public enum ParalPolicy
        {
            ONE_SUCCESS,            // 单个成功
            ALL_SUCCESS,            // 所有成功
        }

        /// <summary>
        /// 并发策略
        /// </summary>
        [Header("并发策略")]
        public ParalPolicy Policy;

        /// <summary>
        /// 运行子节点数量
        /// </summary>
        protected int mRunningCount = 0;

        /// <summary>
        /// 运行成功的子节点数量
        /// </summary>
        protected int mSucceededCount = 0;

        /// <summary>
        /// 运行失败的子节点数量
        /// </summary>
        protected int mFailedCount = 0;

        /// <summary>
        /// 子节点运行结果Map
        /// </summary>
        protected Dictionary<BaseNode, bool> mChildrenResultsMap = new Dictionary<BaseNode, bool>();

        /// <summary>
        /// 节点运行是否成功
        /// </summary>
        protected bool mIsSuccess;

        /// <summary>
        /// 是否有子节点被打断过
        /// </summary>
        protected bool mIsChildAborted;

        /// <summary>
        /// 响应开始
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            mIsChildAborted = false;
            mRunningCount = 0;
            mSucceededCount = 0;
            mFailedCount = 0;
            mChildrenResultsMap.Clear();
            foreach(BaseNode child in mChildNodeList)
            {
                mRunningCount++;
                child.Start();
            }
        }

        /// <summary>
        /// 响应打断
        /// </summary>
        protected override void DoAbort()
        {
            base.DoAbort();
            foreach(BaseNode child in mChildNodeList)
            {
                if(child.IsRunning)
                {
                    child.Abort();
                }
            }
        }

        /// <summary>
        /// 响应子节点停止
        /// </summary>
        /// <param name="child"></param>
        /// <param name="success"></param>
        protected override void DoChildStop(BaseNode child, bool success)
        {
            base.DoChildStop(child, success);
            mRunningCount--;
            if(success)
            {
                mSucceededCount++;
            }
            else
            {
                mFailedCount++;
            }
            mChildrenResultsMap[child] = success;
            bool allChildrenStarted = (mRunningCount + mSucceededCount + mFailedCount) == ChildNodeCount;
            if(allChildrenStarted)
            {
                if(mRunningCount == 0)
                {
                    // 如果子节点已经终止过，我们沿用上一次评估结果作为并发节点运行结果
                    // ??这里暂时没想明白为什么不用最新的结果??
                    if(!mIsChildAborted)
                    {
                        if(Policy == ParalPolicy.ONE_SUCCESS && mSucceededCount > 0)
                        {
                            mIsSuccess = true;
                        }
                        else if(Policy == ParalPolicy.ALL_SUCCESS && mSucceededCount == ChildNodeCount)
                        {
                            mIsSuccess = true;
                        }
                        else
                        {
                            mIsSuccess = false;
                        }
                    }
                    Stop(mIsSuccess);
                }
            }
            else if(!mIsChildAborted)
            {
                if(Policy == ParalPolicy.ONE_SUCCESS && mSucceededCount > 0)
                {
                    mIsSuccess = true;
                    mIsChildAborted = true;
                }
                if(mIsChildAborted)
                {
                    foreach(BaseNode currentChild in mChildNodeList)
                    {
                        if(currentChild.IsRunning)
                        {
                            currentChild.Abort();
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 停止比指定子节点优先级低的节点，并决定是否重新开启组合节点
        /// </summary>
        /// <param name="child"></param>
        /// <param name="immediateRestart"></param>
        public override void StopLowerPriorityChildrenForChild(BaseNode child, bool immediateRestart)
        {
            // 并发节点打断低优先级重置开始运行采用只重置当前需要打断的子节点
            // 不充值其他已经运行完成的节点
            if(immediateRestart)
            {
                if(mChildrenResultsMap[child])
                {
                    mSucceededCount--;
                }
                else
                {
                    mFailedCount--;
                }
                mRunningCount++;
                child.Start();
            }
            else
            {
                Debug.LogError($"并发节点所有子节点优先级一致，不支持打断比指定子节点优先级低的节点，只支持重新开始！");
            }
        }

        /// <summary>
        /// 字符串表达
        /// </summary>
        public override string ToString()
        {
            return $"{base.ToString()},mRunningCount:{mRunningCount},mSucceededCount:{mSucceededCount},mFailedCount:{mFailedCount}";
        }
    }
}
