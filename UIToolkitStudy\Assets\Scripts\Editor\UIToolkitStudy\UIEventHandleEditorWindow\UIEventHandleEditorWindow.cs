﻿/*
 * Description:     UIEventHandleEditorWindow.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/05/29
 */

using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;
using UnityEditor.UIElements;
using System.Collections.Generic;
using System;

/// <summary>
/// 事件处理Editor Window
/// </summary>
public class UIEventHandleEditorWindow : EditorWindow
{
    /// <summary>
    /// Toolbar菜单枚举类型
    /// </summary>
    public enum ToolbarMenuType
    {
        PANEL1,
        PANEL2,
        PANEL3,
    }

    /// <summary>
    /// Popup弹窗枚举类型
    /// </summary>
    public enum PopupMenuType
    {
        POPUP_1,
        POPUP_2,
        POPUP_3,
    }

    /// <summary>
    /// 行为类型
    /// </summary>
    public enum ActionType
    {
        ACTION1,
        ACTION2,
        ACTION3,
        ACTION4,
        ACTION5,
        ACTION6,
        ACTION7,
        ACTION8,
        ACTION9,
        ACTION10,
        ACTION11,
        ACTION12,
        ACTION13,
        ACTION14,
        ACTION15,
        ACTION16,
        ACTION17,
        ACTION18,
        ACTION19,
        ACTION20,
    }

    /// <summary>
    /// 条件类型
    /// </summary>
    public enum ConditionType
    {
        CONDITION1,
        CONDITION2,
        CONDITION3,
        CONDITION4,
        CONDITION5,
        CONDITION6,
        CONDITION7,
        CONDITION8,
        CONDITION9,
        CONDITION10,
        CONDITION11,
        CONDITION12,
        CONDITION13,
        CONDITION14,
        CONDITION15,
        CONDITION16,
        CONDITION17,
        CONDITION18,
        CONDITION19,
        CONDITION20,
    }

    /// <summary>
    /// 菜单栏信息数据列表
    /// </summary>
    private List<KeyValuePair<string, ToolbarMenuType>> mToolbarMenuDatas = new List<KeyValuePair<string, ToolbarMenuType>>()
    {
        new KeyValuePair<string, ToolbarMenuType>("界面1", ToolbarMenuType.PANEL1),
        new KeyValuePair<string, ToolbarMenuType>("界面2", ToolbarMenuType.PANEL2),
        new KeyValuePair<string, ToolbarMenuType>("界面3", ToolbarMenuType.PANEL3),
    };

    /// <summary>
    /// 弹窗菜单数据
    /// </summary>
    private List<PopupMenuType> mPopupMenuDatas = new List<PopupMenuType>
    {
        PopupMenuType.POPUP_1,
        PopupMenuType.POPUP_2,
        PopupMenuType.POPUP_3,
    };

    /// <summary>
    /// 弹出菜单选择索引
    /// </summary>
    private int mPopupSelectedIndex;

    /// <summary>
    /// 行为类型列表
    /// </summary>
    private List<ActionType> mActionTypeList = new List<ActionType>();

    /// <summary>
    /// 条件类型列表
    /// </summary>
    private List<ConditionType> mConditionTypeList = new List<ConditionType>();

    /// <summary>
    /// 选择菜单数据
    /// </summary>
    public KeyValuePair<string, ToolbarMenuType> SelectedMenuData;

    /// <summary>
    /// Graph数据保存路径
    /// </summary>
    public string GraphSavePath;

    [MenuItem("Window/UI Toolkit/UIToolkitStudy/UIEventHandleEditorWindow")]
    public static void ShowUIEventHandleEditorWindow()
    {
        UIEventHandleEditorWindow wnd = GetWindow<UIEventHandleEditorWindow>();
        wnd.titleContent = new GUIContent("UIEventHandleEditorWindow");
    }

    private void OnEnable()
    {
        InitNodeDatas();
    }

    /// <summary>
    /// 初始化节点数据
    /// </summary>
    private void InitNodeDatas()
    {
        mActionTypeList.Clear();
        var actionTypes = Enum.GetValues(typeof(ActionType));
        foreach (var actionType in actionTypes)
        {
            mActionTypeList.Add((ActionType)actionType);
        }

        mConditionTypeList.Clear();
        var conditionTypes = Enum.GetValues(typeof(ConditionType));
        foreach (var conditionType in conditionTypes)
        {
            mConditionTypeList.Add((ConditionType)conditionType);
        }
    }

    public void CreateGUI()
    {
        CreateEventHandleUI();
    }

    /// <summary>
    /// 创建事件处理UI
    /// </summary>
    private void CreateEventHandleUI()
    {
        CreateMenuUI();
        CreatePanelContentUI();
    }

    /// <summary>
    /// 绘制菜单栏UI
    /// </summary>
    private void CreateMenuUI()
    {
        CreateToolbarUI();
        CreateToolbarMenuUI();
        CreatePopupMenuUI();
    }

    /// <summary>
    /// 创建菜单区域
    /// </summary>
    private void CreateToolbarUI()
    {
        var menuHorizontalContainer = new VisualElement();
        menuHorizontalContainer.style.flexDirection = FlexDirection.Row;
        menuHorizontalContainer.style.height = 20f;
        menuHorizontalContainer.style.left = 0;
        menuHorizontalContainer.style.right = 0;

        // 创建toolbar
        var menuToolbar = new Toolbar();
        menuToolbar.name = EventHandleElementNames.MenuToolBarName;
        menuToolbar.style.left = 0;
        menuToolbar.style.right = 0;
        menuToolbar.style.flexGrow = 1;
        menuHorizontalContainer.Add(menuToolbar);
        rootVisualElement.Add(menuHorizontalContainer);
    }

    /// <summary>
    /// 创建Toolbar菜单UI
    /// </summary>
    private void CreateToolbarMenuUI()
    {
        // 创建菜单组件
        SelectedMenuData = mToolbarMenuDatas[0];
        var toolbarMenu = new ToolbarMenu();
        toolbarMenu.name = EventHandleElementNames.ToolBarMenuName;
        toolbarMenu.text = SelectedMenuData.Key;
        toolbarMenu.variant = ToolbarMenu.Variant.Popup;
        toolbarMenu.style.width = 100f;
        foreach (var toolbarData in mToolbarMenuDatas)
        {
            toolbarMenu.menu.AppendAction(toolbarData.Key, OnToolbarMenuChoice, OnToolbarStatusObtain, toolbarData);
        }
        var menuToolbar = rootVisualElement.Q<Toolbar>(EventHandleElementNames.MenuToolBarName);
        menuToolbar.Add(toolbarMenu);
    }

    /// <summary>
    /// 响应菜单栏选择事件
    /// </summary>
    /// <param name="menuAction"></param>
    /// <returns></returns>
    private void OnToolbarMenuChoice(DropdownMenuAction menuAction)
    {
        var menuData = (KeyValuePair<string, ToolbarMenuType>)menuAction.userData;
        Debug.Log($"菜单显示名:{menuData.Key} 菜单类型:{menuData.Value}选中！");
        SelectedMenuData = menuData;
        var toolbarMenu = rootVisualElement.Q<ToolbarMenu>(EventHandleElementNames.ToolBarMenuName);
        toolbarMenu.text = SelectedMenuData.Key;
    }

    /// <summary>
    /// 响应菜单状态获取
    /// </summary>
    private DropdownMenuAction.Status OnToolbarStatusObtain(DropdownMenuAction menuAction)
    {
        var menuData = (KeyValuePair<string, ToolbarMenuType>)menuAction.userData;
        Debug.Log($"菜单显示名:{menuData.Key} 菜单类型:{menuData.Value}响应菜单状态获取！");
        if (SelectedMenuData.Value == menuData.Value)
        {
            return DropdownMenuAction.Status.Checked;
        }
        return DropdownMenuAction.Status.Normal;
    }

    /// <summary>
    /// 创建弹出下拉菜单UI
    /// </summary>
    private void CreatePopupMenuUI()
    {
        // 创建下拉框菜单组件
        var popupMenu = new PopupField<PopupMenuType>(mPopupMenuDatas, mPopupSelectedIndex);
        popupMenu.style.width = 100f;
        popupMenu.RegisterCallback<ChangeEvent<PopupMenuType>>(OnPopupMenuChange);
        var menuToolbar = rootVisualElement.Q<Toolbar>(EventHandleElementNames.MenuToolBarName);
        menuToolbar.Add(popupMenu);
    }

    /// <summary>
    /// 响应弹出菜单变化
    /// </summary>
    /// <param name="evt"></param>
    private void OnPopupMenuChange(ChangeEvent<PopupMenuType> evt)
    {
        Debug.Log($"弹出菜单选中:{evt.newValue}");
        var popupMenu = evt.target as PopupField<PopupMenuType>;
        popupMenu.value = evt.newValue;
    }

    /// <summary>
    /// 创建面板UI
    /// </summary>
    private void CreatePanelContentUI()
    {
        var horizontalContentContainer = new VisualElement();
        horizontalContentContainer.name = EventHandleElementNames.HorizontalContentContainerName;
        horizontalContentContainer.style.left = 0;
        horizontalContentContainer.style.top = 0;
        horizontalContentContainer.style.bottom = 0;
        horizontalContentContainer.style.right = 0;
        horizontalContentContainer.style.flexGrow = 1;
        horizontalContentContainer.style.flexDirection = FlexDirection.Row;
        horizontalContentContainer.style.position = Position.Relative;
        rootVisualElement.Add(horizontalContentContainer);
        CreateLeftVerticalContentUI();
        CreateMiddleGraphViewUI();
        CreateRightContentUI();
    }

    /// <summary>
    /// 创建左侧竖向内容显示
    /// </summary>
    private void CreateLeftVerticalContentUI()
    {
        CreateLeftVerticalContainer();
        CreateLeftOperationContent();
        CreateLeftNodeContent();
    }

    /// <summary>
    /// 创建左侧竖向容器
    /// </summary>
    private void CreateLeftVerticalContainer()
    {
        var leftVerticalContentContainer = new VisualElement();
        leftVerticalContentContainer.name = EventHandleElementNames.LeftVerticalContentContainerName;
        leftVerticalContentContainer.style.left = 0;
        leftVerticalContentContainer.style.width = 300;
        leftVerticalContentContainer.style.top = 0;
        leftVerticalContentContainer.style.bottom = 0;
        leftVerticalContentContainer.style.flexDirection = FlexDirection.Column;
        leftVerticalContentContainer.AddToClassList("unity-rect-field");
        var horizontalContentContainer = rootVisualElement.Q<VisualElement>(EventHandleElementNames.HorizontalContentContainerName);
        horizontalContentContainer.Add(leftVerticalContentContainer);
    }

    /// <summary>
    /// 创建左侧操作内容
    /// </summary>
    private void CreateLeftOperationContent()
    {
        CreateSavePathContent();
        CreateNodesOperationContent();
    }

    /// <summary>
    /// 创建保存路径内容
    /// </summary>
    private void CreateSavePathContent()
    {
        var leftVerticalContentContainer = rootVisualElement.Q<VisualElement>(EventHandleElementNames.LeftVerticalContentContainerName);
        var savePathHorizontalContainer = new VisualElement();
        savePathHorizontalContainer.style.left = 0;
        savePathHorizontalContainer.style.right = 0;
        savePathHorizontalContainer.style.height = 20;
        savePathHorizontalContainer.style.flexDirection = FlexDirection.Row;
        savePathHorizontalContainer.AddToClassList("unity-box");

        var savePathLabelTitle = new Label();
        savePathLabelTitle.style.width = 60;
        savePathLabelTitle.text = "保存路径:";
        savePathHorizontalContainer.Add(savePathLabelTitle);

        var savePathTextField = new TextField();
        savePathTextField.name = EventHandleElementNames.SavePathTextFieldName;
        savePathTextField.style.flexGrow = 1;
        savePathTextField.style.flexShrink = 1;
        savePathTextField.value = GraphSavePath;
        savePathHorizontalContainer.Add(savePathTextField);

        var savePathButton = new Button();
        savePathButton.style.width = 60;
        savePathButton.text = "修改";
        savePathHorizontalContainer.Add(savePathButton);

        // 注册按钮点击
        savePathButton.RegisterCallback<ClickEvent>(OnSavePathButtonClick);
        leftVerticalContentContainer.Add(savePathHorizontalContainer);
    }

    /// <summary>
    /// 相应保存路径按钮点击
    /// </summary>
    /// <param name="clickEvent"></param>
    private void OnSavePathButtonClick(ClickEvent clickEvent)
    {
        Debug.Log($"保存路径按钮点击！");
        var newSavePath = EditorUtility.OpenFolderPanel("保存路径", GraphSavePath, string.Empty);
        if(!string.IsNullOrEmpty(newSavePath))
        {
            GraphSavePath = newSavePath;
            Debug.Log($"更新保存路径:{newSavePath}");
            var savePathTextField = rootVisualElement.Q<TextField>(EventHandleElementNames.SavePathTextFieldName);
            savePathTextField.value = GraphSavePath;
        }
    }

    /// <summary>
    /// 创建节点操作面板
    /// </summary>
    private void CreateNodesOperationContent()
    {
        var exportButton = new Button();
        exportButton.name = EventHandleElementNames.ExportButtonName;
        exportButton.text = "导出";
        exportButton.style.height = 20;
        var leftVerticalContentContainer = rootVisualElement.Q<VisualElement>(EventHandleElementNames.LeftVerticalContentContainerName);
        leftVerticalContentContainer.Add(exportButton);

        // 注册导出按钮点击
        exportButton.RegisterCallback<ClickEvent>(OnExportButtonClick);
    }

    /// <summary>
    /// 响应导出按钮点击
    /// </summary>
    /// <param name="clickEvent"></param>
    private void OnExportButtonClick(ClickEvent clickEvent)
    {
        Debug.Log($"导出按钮点击！");
    }

    /// <summary>
    /// 创建左侧节点内容
    /// </summary>
    private void CreateLeftNodeContent()
    {
        var nodeVerticalContainer = new VisualElement();
        nodeVerticalContainer.name = EventHandleElementNames.NodeVerticalContainerName;
        nodeVerticalContainer.style.flexDirection = FlexDirection.Column;
        nodeVerticalContainer.style.flexGrow = 1;
        nodeVerticalContainer.AddToClassList("unity-popup-window");
        var leftVerticalContentContainer = rootVisualElement.Q<VisualElement>(EventHandleElementNames.LeftVerticalContentContainerName);
        leftVerticalContentContainer.Add(nodeVerticalContainer);

        var actionLabelTitle = new Label();
        actionLabelTitle.style.height = 20;
        actionLabelTitle.text = "节点列表";
        actionLabelTitle.AddToClassList("unity-button");
        nodeVerticalContainer.Add(actionLabelTitle);

        CreateConditionNodeUI();
        CreateActionNodeUI();
    }

    /// <summary>
    /// 创建条件节点UI
    /// </summary>
    private void CreateConditionNodeUI()
    {
        var conditionNodeFoldOut = new Foldout();
        conditionNodeFoldOut.name = EventHandleElementNames.ConditionNodeFoldOutName;
        conditionNodeFoldOut.viewDataKey = EventHandleViewDataKeys.ConditionNodeFoldOutViewDataKeyName;
        conditionNodeFoldOut.text = "条件节点";
        conditionNodeFoldOut.AddToClassList("unity-box");
        var conditionListView = new ListView(mConditionTypeList, 20, OnMakeNodeItem, OnBindConditionItem);
        conditionListView.style.height = 300;
        conditionNodeFoldOut.Add(conditionListView);

        // 注册条件节点FoldOut值变化回调
        conditionNodeFoldOut.RegisterValueChangedCallback(OnConditionNodeFoldOutValueChange);
        var nodeVerticalContainer = rootVisualElement.Q<VisualElement>(EventHandleElementNames.NodeVerticalContainerName);
        nodeVerticalContainer.Add(conditionNodeFoldOut);
    }

    /// <summary>
    /// 创建行为节点UI
    /// </summary>
    private void CreateActionNodeUI()
    {
        var actionNodeFoldOut = new Foldout();
        actionNodeFoldOut.name = EventHandleElementNames.ActionNodeFoldOutName;
        actionNodeFoldOut.viewDataKey = EventHandleViewDataKeys.ActionNodeFoldOutViewDataKeyName;
        actionNodeFoldOut.text = "行为节点";
        actionNodeFoldOut.AddToClassList("unity-box");
        var actionListView = new ListView(mConditionTypeList, 20, OnMakeNodeItem, OnBindActionItem);
        actionListView.style.height = 300;
        actionNodeFoldOut.Add(actionListView);

        // 注册行为节点FoldOut值变化回调
        actionNodeFoldOut.RegisterValueChangedCallback(OnActionNodeFoldOutValueChange);
        var nodeVerticalContainer = rootVisualElement.Q<VisualElement>(EventHandleElementNames.NodeVerticalContainerName);
        nodeVerticalContainer.Add(actionNodeFoldOut);
    }

    /// <summary>
    /// 响应绑定条件节点
    /// </summary>
    /// <param name="conditionItem"></param>
    /// <param name="index"></param>
    private void OnBindConditionItem(VisualElement conditionItem, int index)
    {
        var conditionButton = conditionItem as Button;
        conditionButton.name = GetConditionItemNameByIndex(index);
        conditionButton.text = mConditionTypeList[index].ToString();
        conditionButton.userData = mConditionTypeList[index];
        conditionButton.RegisterCallback<ClickEvent>(OnBindConditionItemButtonClick);
    }

    /// <summary>
    /// 响应绑定行为节点
    /// </summary>
    /// <param name="conditionItem"></param>
    /// <param name="index"></param>
    private void OnBindActionItem(VisualElement conditionItem, int index)
    {
        var actionButton = conditionItem as Button;
        actionButton.name = GetActionItemNameByIndex(index);
        actionButton.text = mActionTypeList[index].ToString();
        actionButton.userData = mActionTypeList[index];
        actionButton.RegisterCallback<ClickEvent>(OnBindActionItemButtonClick);
    }

    /// <summary>
    /// 响应条件节点FoldOut值变化
    /// </summary>
    /// <param name="changeEvent"></param>
    private void OnConditionNodeFoldOutValueChange(ChangeEvent<bool> changeEvent)
    {
        Debug.Log($"响应条件节点折叠值更新：{changeEvent.newValue}");
    }

    /// <summary>
    /// 响应行为节点FoldOut值变化
    /// </summary>
    /// <param name="changeEvent"></param>
    private void OnActionNodeFoldOutValueChange(ChangeEvent<bool> changeEvent)
    {
        Debug.Log($"响应行为节点折叠值更新：{changeEvent.newValue}");
    }

    /// <summary>
    /// 响应节点Item构建
    /// </summary>
    /// <returns></returns>
    private VisualElement OnMakeNodeItem()
    {
        return new Button();
    }

    /// <summary>
    /// 响应Condition按钮点击
    /// </summary>
    /// <param name="clickEvent"></param>
    private void OnBindConditionItemButtonClick(ClickEvent clickEvent)
    {
        var conditionItemButton = clickEvent.target as Button;
        var clickConditionType = (ConditionType)conditionItemButton.userData;
        Debug.Log($"点击了ConditionType:{clickConditionType}按钮！");
    }

    /// <summary>
    /// 响应Action按钮点击
    /// </summary>
    /// <param name="clickEvent"></param>
    private void OnBindActionItemButtonClick(ClickEvent clickEvent)
    {
        var actionItemButton = clickEvent.target as Button;
        var clickActionType = (ActionType)actionItemButton.userData;
        Debug.Log($"点击了ActionType:{clickActionType}按钮！");
    }

    /// <summary>
    /// 获取制定索引的Condition Item名
    /// </summary>
    /// <param name="index"></param>
    /// <returns></returns>
    private string GetConditionItemNameByIndex(int index)
    {
        return $"{EventHandleElementNames.ConditionItemPrefixName}{mConditionTypeList[index]}";
    }

    /// <summary>
    /// 获取制定索引的Action Item名
    /// </summary>
    /// <param name="index"></param>
    /// <returns></returns>
    private string GetActionItemNameByIndex(int index)
    {
        return $"{EventHandleElementNames.ActionItemPrefixName}{mActionTypeList[index]}";
    }

    /// <summary>
    /// 创建中间节点UI
    /// </summary>
    private void CreateMiddleGraphViewUI()
    {
        var middleGraphViewContainer = new VisualElement();
        middleGraphViewContainer.name = EventHandleElementNames.MiddleGraphViewContainerName;
        middleGraphViewContainer.style.left = 0;
        middleGraphViewContainer.style.right = 0;
        middleGraphViewContainer.style.top = 0;
        middleGraphViewContainer.style.bottom = 0;
        middleGraphViewContainer.style.flexDirection = FlexDirection.Column;
        middleGraphViewContainer.style.flexGrow = 1;
        middleGraphViewContainer.AddToClassList("unity-box");

        var middleGraphViewLabelTitle = new Label();
        middleGraphViewLabelTitle.text = "节点面板";
        middleGraphViewLabelTitle.style.height = 20;
        middleGraphViewLabelTitle.style.alignSelf = Align.Center;
        middleGraphViewContainer.Add(middleGraphViewLabelTitle);

        var horizontalContentContainer = rootVisualElement.Q<VisualElement>(EventHandleElementNames.HorizontalContentContainerName);
        horizontalContentContainer.Add(middleGraphViewContainer);
    }

    /// <summary>
    /// 创建右侧参数UI
    /// </summary>
    private void CreateRightContentUI()
    {
        var rightVerticalContentContainer = new VisualElement();
        rightVerticalContentContainer.name = EventHandleElementNames.RightVerticalContentContainerName;
        rightVerticalContentContainer.style.left = 0;
        rightVerticalContentContainer.style.right = 0;
        rightVerticalContentContainer.style.top = 0;
        rightVerticalContentContainer.style.bottom = 0;
        rightVerticalContentContainer.style.flexDirection = FlexDirection.Column;
        rightVerticalContentContainer.style.flexGrow = 1;
        rightVerticalContentContainer.AddToClassList("unity-rect-field");

        var rightVerticalContentLabelTitle = new Label();
        rightVerticalContentLabelTitle.text = "参数面板";
        rightVerticalContentLabelTitle.style.height = 20;
        rightVerticalContentLabelTitle.style.alignSelf = Align.Center;
        rightVerticalContentContainer.Add(rightVerticalContentLabelTitle);

        var horizontalContentContainer = rootVisualElement.Q<VisualElement>(EventHandleElementNames.HorizontalContentContainerName);
        horizontalContentContainer.Add(rightVerticalContentContainer);

    }
}