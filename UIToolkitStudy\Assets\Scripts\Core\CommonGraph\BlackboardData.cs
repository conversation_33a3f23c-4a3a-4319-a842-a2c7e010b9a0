﻿/*
 * Description:     BlackboardData.cs
 * Author:          TonyTang
 * Create Date:     2023/09/14
 */

using System;
using System.Collections.Generic;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// BlackboardDataType.cs
    /// 黑板数据类型枚举
    /// </summary>
    public enum BlackboardDataType
    {
        Bool = 1,
        Int,
        Float,
        String,
    }

    /// <summary>
    /// BlackboardData.cs
    /// 黑板数据
    /// </summary>
    [Serializable]
    public class BlackboardData
    {
        /// <summary>
        /// 所有Bool变量定义数据列表
        /// </summary>
        [Header("Bool变量黑板数据")]
        public List<BlackboardData<bool>> AllBoolDataList;

        /// <summary>
        /// 所有Int变量定义数据列表
        /// </summary>
        [Header("Int变量黑板数据")]
        public List<BlackboardData<int>> AllIntDataList;

        /// <summary>
        /// 所有Float变量定义数据列表
        /// </summary>
        [Header("Float变量黑板数据")]
        public List<BlackboardData<float>> AllFloatDataList;

        /// <summary>
        /// 所有String变量定义数据列表
        /// </summary>
        [Header("String变量黑板数据")]
        public List<BlackboardData<string>> AllStringDataList;

        public BlackboardData()
        {
            AllBoolDataList = new List<BlackboardData<bool>>();
            AllIntDataList = new List<BlackboardData<int>>();
            AllFloatDataList = new List<BlackboardData<float>>();
            AllStringDataList = new List<BlackboardData<string>>();
        }

        /// <summary>
        /// 添加指定黑板数据类型和数据名的黑板数据
        /// </summary>
        /// <param name="blackboardDataType"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public bool AddDataByTyoe(BlackboardDataType blackboardDataType, string key)
        {
            if (ExistData(key))
            {
                Debug.LogError($"黑板数据里已存在Key:{key}的数据，添加数据失败！");
                return false;
            }
            if (blackboardDataType == BlackboardDataType.Bool)
            {
                return AddData<bool>(key);
            }
            else if (blackboardDataType == BlackboardDataType.Int)
            {
                return AddData<int>(key);
            }
            else if (blackboardDataType == BlackboardDataType.Float)
            {
                return AddData<float>(key);
            }
            else if (blackboardDataType == BlackboardDataType.String)
            {
                return AddData<string>(key);
            }
            else
            {
                Debug.LogError($"不支持的黑板变量类型:{blackboardDataType}，添加数据名:{key}数据失败！");
                return false;
            }
        }

        /// <summary>
        /// 添加指定变量类型和数据名的黑板数据
        /// </summary>
        /// <param name="key"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public bool AddData<T>(string key, T value = default(T))
        {
            if (ExistData(key))
            {
                Debug.LogError($"黑板数据里已存在Key:{key}的数据，添加数据失败！");
                return false;
            }
            var dataType = typeof(T);
            if(dataType == CommonGraphConst.BoolType)
            {
                var data = new BlackboardData<T>(key, value);
                AllBoolDataList.Add(data as BlackboardData<bool>);
                return true;
            }
            else if (dataType == CommonGraphConst.IntType)
            {
                var data = new BlackboardData<T>(key, value);
                AllIntDataList.Add(data as BlackboardData<int>);
                return true;
            }
            else if (dataType == CommonGraphConst.FloatType)
            {
                var data = new BlackboardData<T>(key, value);
                AllFloatDataList.Add(data as BlackboardData<float>);
                return true;
            }
            else if (dataType == CommonGraphConst.StringType)
            {
                var data = new BlackboardData<T>(key, value);
                AllStringDataList.Add(data as BlackboardData<string>);
                return true;
            }
            else
            {
                Debug.LogError($"不支持的黑板变量类型:{dataType.Name}，添加数据名:{key}数据失败！");
                return false;
            }
        }

        /// <summary>
        /// 获取指定类型和数据名的数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <returns></returns>
        public BlackboardData<T> GetData<T>(string key)
        {
            var dataType = typeof(T);
            if (dataType == CommonGraphConst.BoolType)
            {
                var findData = AllBoolDataList.Find((data) => data.Name == key);
                return findData as BlackboardData<T>;
            }
            else if (dataType == CommonGraphConst.IntType)
            {
                var findData = AllIntDataList.Find((data) => data.Name == key);
                return findData as BlackboardData<T>;
            }
            else if (dataType == CommonGraphConst.FloatType)
            {
                var findData = AllFloatDataList.Find((data) => data.Name == key);
                return findData as BlackboardData<T>;
            }
            else if (dataType == CommonGraphConst.StringType)
            {
                var findData = AllFloatDataList.Find((data) => data.Name == key);
                return findData as BlackboardData<T>;
            }
            else
            {
                Debug.LogError($"不支持的黑板变量类型:{dataType.Name}，获取数据名:{key}的数据失败！");
                return null;
            }
        }

        /// <summary>
        /// 获取指定类型和数据名的数据值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <returns></returns>
        public T GetValue<T>(string key)
        {
            var data = GetData<T>(key);
            if(data == null)
            {
                Debug.LogError($"不存在Key:{key}的数据，获取数据值失败！");
                return default(T);
            }
            return data.Data;
        }

        /// <summary>
        /// 指定数据名的数据是否存在
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public bool ExistData(string key)
        {
            if(AllBoolDataList.Exists((data) => string.Equals(data.Name, key)))
            {
                return true;
            }
            else if (AllIntDataList.Exists((data) => string.Equals(data.Name, key)))
            {
                return true;
            }
            else if (AllFloatDataList.Exists((data) => string.Equals(data.Name, key)))
            {
                return true;
            }
            else if (AllStringDataList.Exists((data) => string.Equals(data.Name, key)))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 指定数据类型和数据名的数据是否存在
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public bool ExistData<T>(string key)
        {
            var dataType = typeof(T);
            if (dataType == CommonGraphConst.BoolType)
            {
                return AllBoolDataList.Exists((data) => string.Equals(data.Name, key));
            }
            else if (dataType == CommonGraphConst.IntType)
            {
                return AllIntDataList.Exists((data) => string.Equals(data.Name, key));
            }
            else if (dataType == CommonGraphConst.FloatType)
            {
                return AllFloatDataList.Exists((data) => string.Equals(data.Name, key));
            }
            else if (dataType == CommonGraphConst.StringType)
            {
                return AllStringDataList.Exists((data) => string.Equals(data.Name, key));
            }
            else
            {
                Debug.LogError($"不支持的黑板变量类型:{dataType.Name}，判定数据名:{key}是否存在失败！");
                return false;
            }
        }

        /// <summary>
        /// 移除指定变量类型和数据名的数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <returns></returns>
        public bool RemoveData<T>(string key)
        {
            if (!ExistData(key))
            {
                Debug.LogError($"不存在Key:{key}的数据，添加数据失败！");
                return false;
            }
            var removeResult = false;
            var data = GetData<T>(key);
            var dataType = typeof(T);
            if (dataType == CommonGraphConst.BoolType)
            {
                removeResult = AllBoolDataList.Remove(data as BlackboardData<bool>);
            }
            else if (dataType == CommonGraphConst.IntType)
            {
                removeResult = AllIntDataList.Remove(data as BlackboardData<int>);
            }
            else if (dataType == CommonGraphConst.FloatType)
            {
                removeResult = AllFloatDataList.Remove(data as BlackboardData<float>);
            }
            else if (dataType == CommonGraphConst.StringType)
            {
                removeResult = AllStringDataList.Remove(data as BlackboardData<string>);
            }
            else
            {
                Debug.LogError($"不支持的黑板数据类型:{dataType.Name}，移除数据Key:{key}数据失败！");
                return false;
            }
            if(!removeResult)
            {
                Debug.LogError($"数据类型:{dataType.Name}里找不到黑板数据Key:{key}的数据，移除数据失败！");
            }
            return removeResult;
        }

        /// <summary>
        /// 更新指定类型和数据名的值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="newValue"></param>
        /// <returns></returns>
        public bool UpdaetValue<T>(string key, T newValue)
        {
            var findData = GetData<T>(key);
            if(findData == null)
            {
                var dataType = typeof(T);
                Debug.LogError($"不存在黑板数据Key:{key}和数据类型:{dataType.Name}的数据，更新值:{newValue}失败！");
                return false;
            }
            findData.Data = newValue;
            return true;
        }
    }
}