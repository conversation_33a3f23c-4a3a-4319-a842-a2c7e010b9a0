﻿/*
 * Description:             SetShareIntNodeView.cs
 * Author:                  TONYTANG
 * Create Date:             2023/10/05
 */

using UnityEngine;
using UnityEngine.UIElements;

namespace TCommonGraph
{
    /// <summary>
    /// SetShareIntNodeView.cs
    /// 黑板int数据设置节点显示类型
    /// </summary>
    public class SetShareIntNodeView : BaseSetShareNodeView
    {
        /// <summary>
        /// 创建节点自定义UI
        /// </summary>
        protected override void CreateCustomUI()
        {
            base.CreateCustomUI();
            var nodeVerticalUIContainer = this.Q<VisualElement>(BTGraphElementNames.NodeUIVerticalContainerName);
            var nodeDesDivider = UIToolkitUtilities.CreateHorizontalDivider(BTGraphConstEditor.DividerColor);
            nodeVerticalUIContainer.Add(nodeDesDivider);

            UIToolkitUtilities.CreateBindSOPropertyField(nodeVerticalUIContainer, NodeD<PERSON>, "TargetValue");
        }
    }
}
