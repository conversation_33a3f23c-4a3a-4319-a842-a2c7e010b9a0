﻿/*
 * Description:     BaseNode.cs
 * Author:          TonyTang
 * Create Date:     2023/06/19
 */

using NPBehave;
using System;
using System.Collections.Generic;
using UnityEditor.Experimental.GraphView;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// BaseNode.cs
    /// 节点数据基类
    /// </summary>
    [Serializable]
    public abstract class BaseNode : ScriptableObject
    {
        /// <summary>
        /// 唯一ID
        /// </summary>
        [Header("唯一ID")]
        public string GUID;

        /// <summary>
        /// 位置
        /// </summary>
        [Header("位置")]
        [HideInInspector]
        public Rect Position;

        /// <summary>
        /// 节点描述
        /// </summary>
        [Header("节点描述")]
        [HideInInspector]
        public string Des = "节点描述";

        /// <summary>
        /// 是否是根节点
        /// </summary>
        public virtual bool EntryPoint
        {
            get
            {
                return false;
            }
        }

        /// <summary>
        /// 节点类型(子类重写自定义节点类型)
        /// </summary>
        public virtual NodeType NodeType
        {
            get
            {
                return NodeType.Composition;
            }
        }

        /// <summary>
        /// 节点状态
        /// </summary>
        public NodeState NodeState
        {
            get;
            protected set;
        }

        /// <summary>
        /// 是否进入过打断(辅助调试信息显示)
        /// </summary>
        public bool IsEnteredAbort
        {
            get;
            protected set;
        }

        #region 运行时
        /// <summary>
        /// 所属树数据
        /// </summary>
        protected TreeData mOwnerTreeData;

        /// <summary>
        /// 父节点(仅限能连接子节点的节点类型，比如:复合节点和装饰节点)
        /// </summary>
        public BaseParentNode ParentNode
        {
            get;
            private set;
        }

        /// <summary>
        /// 所有输出端口边数据列表
        /// </summary>
        private List<EdgeData> mAllOutputEdgeDataList;

        /// <summary>
        /// 所有输出端口端口数据列表
        /// </summary>
        private List<NodePortData> mAllOutputPortDataList;

        /// <summary>
        /// 输出端口数据Map<输出端口名, 端口数据>
        /// </summary>
        private Dictionary<string, NodePortData> mAllOutputPortDataMap;

        public Clock Clock
        {
            get
            {
                return mClock != null ? mClock : UnityContext.GetClock();
            }
            private set
            {
                mClock = value;
            }
        }
        private Clock mClock;

        /// <summary>
        /// 是否在运行
        /// </summary>
        public bool IsRunning
        {
            get
            {
                return NodeState == NodeState.Running;
            }
        }

        /// <summary>
        /// 是否被打断
        /// </summary>
        public bool IsAbort
        {
            get
            {
                return NodeState == NodeState.Abort;
            }
        }

        /// <summary>
        /// 是否挂起
        /// </summary>
        public bool IsSuspend
        {
            get
            {
                return NodeState == NodeState.Suspend;
            }
        }

        /// <summary>
        /// 是否完成
        /// </summary>
        public bool IsComplete
        {
            get
            {
                return NodeState == NodeState.Failed || NodeState == NodeState.Success;
            }
        }
        #endregion

        /// <summary>
        /// 无参构造
        /// </summary>
        public BaseNode()
        {
            mAllOutputEdgeDataList = new List<EdgeData>();
            mAllOutputPortDataList = new List<NodePortData>();
            mAllOutputPortDataMap = new Dictionary<string, NodePortData>();
        }

        /// <summary>
        /// 克隆自身
        /// Note:
        /// 此方法实现嵌套存储的SO Asset也全部Clone
        /// </summary>
        /// <returns></returns>
        public virtual BaseNode CloneSelf()
        {
            return this.Clone();
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="guid">唯一ID</param>
        /// <param name="position"></param>
        /// <param name="nodeState">节点状态</param>
        public void Init(string guid, Rect position, NodeState nodeState = NodeState.Suspend)
        {
            GUID = guid;
            Position = position;
            NodeState = nodeState;
            IsEnteredAbort = false;
            name = GetType().Name;
        }

        /// <summary>
        /// 更新节点GUID
        /// </summary>
        /// <param name="guid"></param>
        public void UpdateGUID(string guid)
        {
            GUID = guid;
        }

        #region 运行时
        /// <summary>
        /// 设置父节点
        /// </summary>
        /// <param name="parentNode"></param>
        public void SetParentNode(BaseParentNode parentNode)
        {
            ParentNode = parentNode;
        }

        /// <summary>
        /// 设置所属树数据
        /// </summary>
        /// <param name="treeData"></param>
        public void SetOwnerTreeData(TreeData treeData)
        {
            mOwnerTreeData = treeData;
            InitPortDatas();
        }

        /// <summary>
        /// 设置时钟
        /// </summary>
        /// <param name="clock"></param>
        public void SetClock(Clock clock)
        {
            Clock = clock;
        }

        /// <summary>
        /// 重置节点运行时数据
        /// </summary>
        public void ResetNodeRuntimeData()
        {
            NodeState = NodeState.Suspend;
            IsEnteredAbort = false;
        }

        /// <summary>
        /// 初始化端口数据
        /// </summary>
        protected void InitPortDatas()
        {
            mAllOutputEdgeDataList.Clear();
            mOwnerTreeData.GetAllChildEdgeDataList(ref mAllOutputEdgeDataList, this);
            mAllOutputPortDataList.Clear();
            mAllOutputPortDataMap.Clear();
            foreach (var outputEdgeData in mAllOutputEdgeDataList)
            {
                var outputPortName = outputEdgeData.OutputPortName;
                NodePortData nodePortData;
                if (!mAllOutputPortDataMap.TryGetValue(outputPortName, out nodePortData))
                {
                    nodePortData = ObjectPool.Singleton.Pop<NodePortData>();
                    nodePortData.SetData(this, outputPortName, Direction.Output);
                    AddOutputPortData(nodePortData);
                }
                var inputNode = mOwnerTreeData.GetNodeByGUID(outputEdgeData.InputNodeGUID);
                nodePortData.AddConnectNode(inputNode);
            }
        }

        /// <summary>
        /// 添加输出端口数据
        /// </summary>
        /// <param name="nodePortData"></param>
        /// <returns></returns>
        public bool AddOutputPortData(NodePortData nodePortData)
        {
            if (nodePortData == null)
            {
                Debug.LogError($"不允许添加空节点输出端口数据，添加输出端口数据失败！");
                return false;
            }
            if (!string.Equals(nodePortData.OwnerNode.GUID, GUID))
            {
                Debug.LogError($"节点输出端口所属节点GUID:{nodePortData.OwnerNode.GUID}和当前节点GUID:{GUID}不一致，添加输出端口数据失败！");
                return false;
            }
            if (mAllOutputPortDataMap.ContainsKey(nodePortData.PortName))
            {
                Debug.LogError($"添加已存在的节点端口数据，端口所属节点GUID:{nodePortData.OwnerNode.GUID}，端口名:{nodePortData.PortName}，端口类型:{nodePortData.PortDirection}，添加输出端口数据失败！");
                return false;
            }
            mAllOutputPortDataList.Add(nodePortData);
            mAllOutputPortDataMap.Add(nodePortData.PortName, nodePortData);
            return true;
        }

        #region 行为树流程相关
        /// <summary>
        /// 节点开始运行
        /// </summary>
        public void Start()
        {
            Debug.Log($"节点GUID:{GUID}开始运行！");
            NodeState = NodeState.Running;
            DoStart();
        }

        /// <summary>
        /// 执行开始运行流程
        /// </summary>
        protected virtual void DoStart()
        {

        }

        /// <summary>
        /// 节点打断
        /// </summary>
        public void Abort()
        {
            if (!IsRunning)
            {
                Debug.LogError($"节点GUID:{GUID}未处于运行时，不应该触发Abort，请检查代码！");
                return;
            }
            Debug.Log($"节点GUID:{GUID}打断运行！");
            NodeState = NodeState.Abort;
            IsEnteredAbort = true;
            DoAbort();
        }

        /// <summary>
        /// 执行打断流程
        /// </summary>
        protected virtual void DoAbort()
        {

        }

        /// <summary>
        /// 节点运行完毕
        /// </summary>
        /// <param name="success"></param>
        protected virtual void Stop(bool success)
        {
            if(IsComplete)
            {
                Debug.LogError($"节点GUID:{GUID}已经运行完成过，不应该重复Stop，请检查代码！");
                return;
            }
            Debug.Log($"节点GUID:{GUID}停止运行，success:{success}");
            NodeState = success ? NodeState.Success : NodeState.Failed;
            DoStop();
            if(ParentNode != null)
            {
                ParentNode.ChildStop(this, success);
            }
        }

        /// <summary>
        /// 执行运行完毕流程
        /// </summary>
        protected virtual void DoStop()
        {

        }

        /// <summary>
        /// 父组合节点停止
        /// </summary>
        /// <param name="parentNode"></param>
        public virtual void ParentCompositeStop(BaseCompositionNode parentNode)
        {
            DoParentCompositeStop(parentNode);
        }

        /// <summary>
        /// 执行父组合节点停止流程
        /// </summary>
        /// <param name="parentNode"></param>
        protected virtual void DoParentCompositeStop(BaseCompositionNode parentNode)
        {

        }

        /// <summary>
        /// 子节点结束
        /// </summary>
        /// <param name="child"></param>
        /// <param name="success"></param>
        public void ChildStop(BaseNode child, bool success)
        {
            DoChildStop(child, success);
        }

        /// <summary>
        /// 执行子节点结束流程
        /// </summary>
        /// <param name="child"></param>
        /// <param name="success"></param>
        protected virtual void DoChildStop(BaseNode child, bool success)
        {

        }
        #endregion
        #endregion

        /// <summary>
        /// 字符串表达
        /// </summary>
        public virtual string ToString()
        {
            return $"NodeType:{NodeType},name:{name},GUID:{GUID},NodeState:{NodeState},EntryPoint:{EntryPoint},Des:{Des}";
        }

        /// <summary>
        /// 获取节点路线表达字符串
        /// </summary>
        /// <returns></returns>
        public string GetPath()
        {
            if(ParentNode != null)
            {
                return $"{ParentNode.GetPath()}/{name}({Des})";
            }
            else
            {
                return $"{name}({Des})";
            }
        }
    }
}