﻿/*
 * Description:             LogNode.cs
 * Author:                  TONYTANG
 * Create Date:             2023/12/08
 */

using System;
using System.Collections.Generic;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// LogNode.cs
    /// Log打印节点
    /// </summary>
    public class LogNode : BaseActionNode
    {
        /// <summary>
        /// Log内容
        /// </summary>
        [Header("Log内容")]
        public string LogContent = "";

        /// <summary>
        /// 执行开始
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            Debug.Log($"GUID:{GUID},LogNode:{LogContent}");
            Stop(true);
        }

        /// <summary>
        /// 执行打断
        /// </summary>
        protected override void DoAbort()
        {
            base.DoAbort();
            Stop(false);
        }
    }
}
