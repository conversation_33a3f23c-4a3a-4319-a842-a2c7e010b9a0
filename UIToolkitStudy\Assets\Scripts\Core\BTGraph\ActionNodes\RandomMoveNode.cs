﻿/*
 * Description:     RandomMoveNode.cs
 * Author:          <PERSON>Tang
 * Create Date:     2023/12/08
 */

using System;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// RandomMoveNode.cs
    /// 随机移动节点
    /// </summary>
    public class RandomMoveNode : BaseActionNode
    {
        /// <summary>
        /// 目标位置
        /// </summary>
        private Vector3 mTargetDestination;

        /// <summary>
        /// 执行开始
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            DoRandomMove();
        }

        /// <summary>
        /// 执行打断
        /// </summary>
        protected override void DoAbort()
        {
            base.DoAbort();
            Stop(false);
        }

        /// <summary>
        /// 执行运行完毕流程
        /// </summary>
        protected override void DoStop()
        {
            base.DoStop();
            mOwnerTreeData.Clock.RemoveUpdateObserver(CheckMoveToDestination);
        }

        /// <summary>
        /// 执行随机移动
        /// </summary>
        private void DoRandomMove()
        {
            var randomXPos = UnityEngine.Random.Range(-100, 100);
            var randomZPos = UnityEngine.Random.Range(-100, 100);
            mTargetDestination = new Vector3(randomXPos, 0, randomZPos);
            MoveToDestination();
            mOwnerTreeData.Clock.AddUpdateObserver(CheckMoveToDestination);
        }

        /// <summary>
        /// 移动到目标位置
        /// </summary>
        private void MoveToDestination()
        {
            var bindActor = mOwnerTreeData.OwnerGraphData.OwnerBT.GetBindActor();
            if (bindActor == null)
            {
                var bindUID = mOwnerTreeData.OwnerGraphData.OwnerBT.BindUID;
                Debug.LogError($"找不到绑定UID:{bindUID}的对象，移动到目标位置失败1！");
                Stop(false);
                return;
            }
            bindActor.GoToPosition(mTargetDestination);
        }

        /// <summary>
        /// 检查移动到目标位置Update
        /// </summary>
        private void CheckMoveToDestination()
        {
            var bindActor = mOwnerTreeData.OwnerGraphData.OwnerBT.GetBindActor();
            if (bindActor == null)
            {
                var bindUID = mOwnerTreeData.OwnerGraphData.OwnerBT.BindUID;
                Debug.LogError($"找不到绑定UID:{bindUID}的对象，移动到目标位置失败2！");
                Stop(false);
                return;
            }
            var actorPosition = bindActor.GetPosition();
            var destinationDistance = Vector3.Distance(actorPosition, mTargetDestination);
            if (destinationDistance < 1f)
            {
                bindActor.StopMove();
                Stop(true);
            }
        }
    }
}
