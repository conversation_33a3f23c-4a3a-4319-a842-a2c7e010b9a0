﻿/*
 * Description:     UIBaseGenericEditorWindow.cs
 * Author:          TonyTang
 * Create Date:     2023/10/04
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

/// <summary>
/// UIBaseGenericEditorWindow.cs
/// </summary>
/// <typeparam name="T"></typeparam>
public abstract class UIBaseGenericEditorWindow<T> : EditorWindow where T : SuperBaseGraphView 
{
    /// <summary>
    /// 
    /// </summary>
    public T GraphView;

    public UIBaseGenericEditorWindow()
    {
        Debug.Log($"UIBaseGenericEditorWindow()");
    }

    protected virtual void OnDestroy()
    {
        Debug.Log($"UIBaseGenericEditorWindow:OnDestroy()");
    }

    /// <summary>
    /// 获取当前GraphView的GraphData类型
    /// </summary>
    /// <returns></returns>
    protected Type GetGraphViewDataType()
    {
        var editorWindowType = this.GetType();
        var graphViewFiledInfo = editorWindowType.GetField("GraphView");
        var graphViewMemberType = graphViewFiledInfo.FieldType;
        var graphDataPropertyInfo = graphViewMemberType.GetProperty("SourceGraphData");
        var graphDataPropertyType = graphDataPropertyInfo.PropertyType;
        return graphDataPropertyType;
    }
}