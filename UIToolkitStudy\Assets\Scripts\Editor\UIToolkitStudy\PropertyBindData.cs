﻿/*
 * Description:     PropertyBindData.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/08/31
 */

using UnityEditor;
using UnityEditor.UIElements;
using UnityEngine.UIElements;

/// <summary>
/// 属性绑定数据
/// </summary>
public class PropertyBindData
{
    /// <summary>
    /// 绑定属性名
    /// </summary>
    public string BindPropertyName
    {
        get
        {
            return BindSerializedProperty?.name;
        }
    }

    /// <summary>
    /// 绑定SerializedObject
    /// </summary>
    public SerializedObject BindSerializedObject
    {
        get;
        private set;
    }

    /// <summary>
    /// 绑定属性
    /// </summary>
    public SerializedProperty BindSerializedProperty
    {
        get;
        private set;
    }

    /// <summary>
    /// 绑定属性显示组件
    /// </summary>
    public VisualElement BindVisualElement
    {
        get;
        private set;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="bindSerializedObject"></param>
    /// <param name="bindSerializedProperty"></param>
    /// <param name="bindVisualElement"></param>
    public PropertyBindData(SerializedObject bindSerializedObject, SerializedProperty bindSerializedProperty, VisualElement bindVisualElement)
    {
        BindSerializedObject = bindSerializedObject;
        BindSerializedProperty = bindSerializedProperty;
        BindVisualElement = bindVisualElement;
    }
}
