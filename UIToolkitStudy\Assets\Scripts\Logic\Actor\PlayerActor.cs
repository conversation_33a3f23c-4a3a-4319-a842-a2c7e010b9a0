﻿/*
 * Description:     MonsterActor.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/12/06
 */

using System;

/// <summary>
/// PlayerActor.cs
/// 玩家对象类
/// </summary>
public class PlayerActor : BaseActor
{
    /// <summary>
    /// 角色类型
    /// </summary>
    public override ActorType ActorType
    {
        get
        {
            return ActorType.PLAYER;
        }
    }
}