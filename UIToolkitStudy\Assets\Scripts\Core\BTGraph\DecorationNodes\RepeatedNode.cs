﻿/*
 * Description:     RepeatedNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/07/03
 */

using System;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// RepeatedNode.cs
    /// 行为树反复执行修饰节点
    /// </summary>
    [Serializable]
    public class RepeatedNode : BaseDecorationNode
    {
        /// <summary>
        /// 重复次数
        /// </summary>
        [Header("重复次数")]
        [SerializeReference]
        public int Times;
    }
}