﻿/*
 * Description:     UIToolkitUtilities.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/06/29
 */

using System.Collections.Generic;
using UnityEditor;
using UnityEditor.UIElements;
using UnityEngine;
using UnityEngine.UIElements;
using Debug = UnityEngine.Debug;

/// <summary>
/// UIToolkitUtilities.cs
/// UIToolkit工具类
/// </summary>
public static class UIToolkitUtilities
{
    /// <summary>
    /// 创建一个指定名字指定相关设置的横向容器
    /// </summary>
    /// <param name="containerName"></param>
    /// <param name="flexGrow"></param>
    /// <param name="classList"></param>
    /// <param name="styleLeft"></param>
    /// <param name="styleRight"></param>
    /// <param name="styleTop"></param>
    /// <param name="styleBottom"></param>
    /// <param name="styleColor"></param>
    /// <returns></returns>
    public static VisualElement CreateHorizontalContainer(string containerName, float flexGrow = 1f, string classList = null, float styleLeft = 0, float styleRight = 0, float styleTop = 0, float styleBottom = 0, StyleColor styleColor = default(StyleColor))
    {
        var nodeHorizontalUIContainer = new VisualElement();
        nodeHorizontalUIContainer.name = containerName;
        nodeHorizontalUIContainer.style.left = styleLeft;
        nodeHorizontalUIContainer.style.right = styleRight;
        nodeHorizontalUIContainer.style.flexGrow = flexGrow;
        nodeHorizontalUIContainer.style.top = styleTop;
        nodeHorizontalUIContainer.style.bottom = styleBottom;
        nodeHorizontalUIContainer.style.flexDirection = FlexDirection.Row;
        nodeHorizontalUIContainer.style.backgroundColor = styleColor;
        if(!string.IsNullOrEmpty(classList))
        {
            nodeHorizontalUIContainer.AddToClassList(classList);
        }
        return nodeHorizontalUIContainer;
    }

    /// <summary>
    /// 创建一个指定名字指定相关设置的竖向容器
    /// </summary>
    /// <param name="containerName"></param>
    /// <param name="flexGrow"></param>
    /// <param name="classList"></param>
    /// <param name="styleLeft"></param>
    /// <param name="styleRight"></param>
    /// <param name="styleTop"></param>
    /// <param name="styleBottom"></param>
    /// <param name="styleColor"></param>
    /// <returns></returns>
    public static VisualElement CreateVerticalContainer(string containerName, float flexGrow = 1f, string classList = null, float styleLeft = 0, float styleRight = 0, float styleTop = 0, float styleBottom = 0, StyleColor styleColor = default(StyleColor))
    {
        var nodeHorizontalUIContainer = new VisualElement();
        nodeHorizontalUIContainer.name = containerName;
        nodeHorizontalUIContainer.style.left = styleLeft;
        nodeHorizontalUIContainer.style.right = styleRight;
        nodeHorizontalUIContainer.style.flexGrow = flexGrow;
        nodeHorizontalUIContainer.style.top = styleTop;
        nodeHorizontalUIContainer.style.bottom = styleBottom;
        nodeHorizontalUIContainer.style.flexDirection = FlexDirection.Column;
        nodeHorizontalUIContainer.style.backgroundColor = styleColor;
        if (!string.IsNullOrEmpty(classList))
        {
            nodeHorizontalUIContainer.AddToClassList(classList);
        }
        return nodeHorizontalUIContainer;
    }

    /// <summary>
    /// 创建一个横向分割线
    /// </summary>
    /// <param name="color"></param>
    /// <returns></returns>
    public static VisualElement CreateHorizontalDivider(Color color, float height = 1f)
    {
        var horizontalDivider = new VisualElement();
        horizontalDivider.style.height = height;
        horizontalDivider.style.flexGrow = 1;
        horizontalDivider.style.backgroundColor = color;
        return horizontalDivider;
    }

    /// <summary>
    /// 创建一个竖向分割线
    /// </summary>
    /// <param name="color"></param>
    /// <returns></returns>
    public static VisualElement CreateVerticalDivider(Color color)
    {
        var horizontalDivider = new VisualElement();
        horizontalDivider.style.width = 1;
        horizontalDivider.style.flexGrow = 1;
        horizontalDivider.style.backgroundColor = color;
        return horizontalDivider;
    }

    /// <summary>
    /// 指定容器创建指定对象的可视化Inspector
    /// </summary>
    /// <param name="scriptableObject"></param>
    /// <param name="propertyBindDataMap"></param>
    public static VisualElement CreateBindSOInspector(ScriptableObject scriptableObject, Dictionary<string, PropertyBindData> propertyBindDataMap = null)
    {
        if(scriptableObject == null)
        {
            Debug.LogError($"不允许给空ScriptableObject创建可视化Inspector！");
            return null;
        }
        var container = CreateVerticalContainer(null, 1);
        var serializedObject = new SerializedObject(scriptableObject);
        var propertyIterator = serializedObject.GetIterator();
        propertyIterator.NextVisible(true);
        while(propertyIterator.NextVisible(false))
        {
            var propertyField = new PropertyField();
            var propertyName = propertyIterator.name;
            var property = serializedObject.FindProperty(propertyName);
            propertyField.name = propertyName;
            propertyField.BindProperty(property);
            container.Add(propertyField);
            if(propertyBindDataMap != null)
            {
                var propertyBindData = new PropertyBindData(serializedObject, property, propertyField);
                propertyBindDataMap.Add(propertyName, propertyBindData);
            }
        }
        return container;
    }

    /// <summary>
    /// 指定容器创建指定对象指定属性名的绑定PropertyField
    /// </summary>
    /// <param name="container"></param>
    /// <param name="scriptableObject"></param>
    /// <param name="propertyName"></param>
    /// <param name="flexGrow"></param>
    /// <param name="height"></param>
    public static PropertyBindData CreateBindSOPropertyField(VisualElement container, ScriptableObject scriptableObject, string propertyName, float flexGrow = 0, float height = 25f)
    {
        if (container == null)
        {
            Debug.LogError($"不允许给空容器创建绑定PropertyField！");
            return null;
        }
        if (scriptableObject == null)
        {
            Debug.LogError($"不允许给空ScriptableObject创建绑定PropertyField！");
            return null;
        }
        var serializedObject = new SerializedObject(scriptableObject);
        var property = serializedObject.FindProperty(propertyName);
        if(property == null)
        {
            Debug.LogError($"ScriptableObject:{scriptableObject.name}没找到属性名:{propertyName}的属性，创建绑定PropertyField失败！");
            return null;
        }
        var propertyField = new PropertyField();
        propertyField.name = propertyName;
        propertyField.BindProperty(property);
        propertyField.style.height = height;
        propertyField.style.flexGrow = flexGrow;
        container.Add(propertyField);
        var propertyBindData = new PropertyBindData(serializedObject, property, propertyField);
        return propertyBindData;
    }

    /// <summary>
    /// 指定容器创建指定对象指定属性名的绑定TextField
    /// </summary>
    /// <param name="container"></param>
    /// <param name="scriptableObject"></param>
    /// <param name="propertyName"></param>
    /// <param name="flexGrow"></param>
    /// <param name="height"></param>
    /// <param name="fontSize"></param>
    /// <param name="textAnchor"></param>
    public static PropertyBindData CreateBindSOTextField(VisualElement container, ScriptableObject scriptableObject, string propertyName, float flexGrow = 0, float height = 25f, float fontSize = 15f, TextAnchor textAnchor = TextAnchor.MiddleCenter)
    {
        if (container == null)
        {
            Debug.LogError($"不允许给空容器创建绑定TextField！");
            return null;
        }
        if (scriptableObject == null)
        {
            Debug.LogError($"不允许给空ScriptableObject创建绑定TextField！");
            return null;
        }
        var serializedObject = new SerializedObject(scriptableObject);
        var property = serializedObject.FindProperty(propertyName);
        if (property == null)
        {
            Debug.LogError($"ScriptableObject:{scriptableObject.name}没找到属性名:{propertyName}的属性，创建绑定TextField失败！");
            return null;
        }
        var textField = new TextField();
        textField.name = propertyName;
        textField.BindProperty(property);
        textField.style.height = height;
        textField.style.fontSize = fontSize;
        textField.style.flexGrow = flexGrow;
        textField.style.unityTextAlign = textAnchor;
        container.Add(textField);
        var propertyBindData = new PropertyBindData(serializedObject, property, textField);
        return propertyBindData;
    }
}