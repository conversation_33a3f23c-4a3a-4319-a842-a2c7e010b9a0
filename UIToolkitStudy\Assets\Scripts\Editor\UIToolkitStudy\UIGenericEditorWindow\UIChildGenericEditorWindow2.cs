﻿/*
 * Description:     UIChildGenericEditorWindow2.cs
 * Author:          TonyTang
 * Create Date:     2023/10/04
 */

using UnityEditor;
using UnityEngine;

/// <summary>
/// UIChildGenericEditorWindow2.cs
/// </summary>
public class UIChildGenericEditorWindow2 : UIBaseGenericEditorWindow<ChildGraphView2>
{
    /// <summary>
    /// 打开节点编辑器窗口
    /// </summary>
    [MenuItem("Window/UI Toolkit/UIToolkitStudy/OpenUIChildGenericEditorWindow2")]
    public static void ShowUIChildGenericEditorWindow1()
    {
        if(!EditorWindow.HasOpenInstances<UIChildGenericEditorWindow2>())
        {
            UIChildGenericEditorWindow2 wnd = GetWindow<UIChildGenericEditorWindow2>();
            wnd.titleContent = new GUIContent("UIChildGenericEditorWindow2");
            wnd.Show();
        }
    }

    /// <summary>
    /// 关闭节点编辑器窗口
    /// </summary>
    [MenuItem("Window/UI Toolkit/UIToolkitStudy/CloseUIChildGenericEditorWindow2")]
    public static void CloseUIChildGenericEditorWindow2()
    {
        UIChildGenericEditorWindow2 wnd = GetWindow<UIChildGenericEditorWindow2>();
        if(wnd != null)
        {
            wnd.Close();
        }
    }

    /// <summary>
    /// 响应Unity加载完成
    /// </summary>
    [InitializeOnLoadMethod]
    private static void OnProjectLoadedInEditor()
    {
        if (EditorWindow.HasOpenInstances<UIChildGenericEditorWindow2>())
        {
            Debug.Log($"有已打开的UIChildGenericEditorWindow2窗口！");
        }
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    public UIChildGenericEditorWindow2() : base()
    {
        Debug.Log($"UIChildGenericEditorWindow2()");
        var editorWindowType = this.GetType();
        var graphViewFiledInfo = editorWindowType.GetField("GraphView");
        var graphViewMemberType = graphViewFiledInfo.FieldType;
        Debug.Log($"GraphView Type = {graphViewMemberType.Name} GraphData Type = {GetGraphViewDataType().Name}");
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        Debug.Log($"UIChildGenericEditorWindow2:OnDestroy()");
    }
}
