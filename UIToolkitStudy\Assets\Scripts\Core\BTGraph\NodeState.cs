﻿/*
 * Description:     NodeState.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/06/29
 */

namespace TCommonGraph
{
    /// <summary>
    /// NodeState.cs
    /// 节点状态
    /// </summary>
    public enum NodeState
    {
        Suspend = 0,        // 挂起状态
        Running,            // 运行状态
        Abort,              // 被打断状态
        Success,            // 成功状态
        Failed,             // 失败状态
    }
}