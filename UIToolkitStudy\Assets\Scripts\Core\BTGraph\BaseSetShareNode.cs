﻿/*
 * Description:             BaseSetShareNode.cs
 * Author:                  TONYTANG
 * Create Date:             2023/10/04
 */

using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// BaseSetShareNode.cs
    /// 设置共享数据基类
    /// </summary>
    public abstract class BaseSetShareNode : BaseActionNode
    {
        /// <summary>
        /// 设置变量名
        /// </summary>
        [Header("设置变量名")]
        public string VariableName = "";

        /// <summary>
        /// 执行打断流程
        /// </summary>
        protected override void DoAbort()
        {
            base.DoAbort();
            Stop(false);
        }
    }
}