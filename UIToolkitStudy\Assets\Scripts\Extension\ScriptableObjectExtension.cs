﻿/*
 * Description:             ScriptableObjectExtension.cs
 * Author:                  TONYTANG
 * Create Date:             2023/08/31
 */

using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// ScriptableObjectExtension.cs
/// ScriptableObject扩展类
/// </summary>
public static class ScriptableObjectExtension
{
    /// <summary>
    /// 泛型ScriptableObject克隆扩展方法
    /// </summary>
    public static T Clone<T>(this T scriptableObject) where T : ScriptableObject
    {
        if (scriptableObject == null)
        {
            Debug.LogError($"ScriptableObject is null. Returning default {typeof(T)} object.");
            return (T)ScriptableObject.CreateInstance(typeof(T));
        }
        T instance = Object.Instantiate(scriptableObject);
        // remove (Clone) from name
        instance.name = scriptableObject.name;
        return instance;
    }
}