﻿/*
 * Description:     EventHandleViewDataKeys.cs
 * Author:          Tony<PERSON>ang
 * Create Date:     2023/05/29
 */

/// <summary>
/// EventHandleViewDataKeys.cs
/// 事件处理显示数据Key
/// </summary>
public static class EventHandleViewDataKeys
{
    /// <summary>
    /// 条件节点折叠ViewDataKey
    /// </summary>
    public const string ConditionNodeFoldOutViewDataKeyName = "ConditionNodeFoldOutViewDataKey";

    /// <summary>
    /// 行为节点折叠ViewDataKey
    /// </summary>
    public const string ActionNodeFoldOutViewDataKeyName = "ActionNodeFoldOutViewDataKey";
}
