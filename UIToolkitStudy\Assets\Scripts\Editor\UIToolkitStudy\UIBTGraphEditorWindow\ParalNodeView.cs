﻿/*
 * Description:     ParalNodeView.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/07/03
 */

using UnityEngine.UIElements;

namespace TCommonGraph
{
    /// <summary>
    /// ParalNodeView.cs
    /// 并发节点NodeView类
    /// </summary>
    public class ParalNodeView : BaseCompositionNodeView
    {
        /// <summary>
        /// 创建节点自定义UI
        /// </summary>
        protected override void CreateCustomUI()
        {
            base.CreateCustomUI();
            var nodeVerticalUIContainer = this.Q<VisualElement>(BTGraphElementNames.NodeUIVerticalContainerName);
            var nodeDesDivider = UIToolkitUtilities.CreateHorizontalDivider(BTGraphConstEditor.DividerColor);
            nodeVerticalUIContainer.Add(nodeDesDivider);

            UIToolkitUtilities.CreateBindSOPropertyField(nodeVerticalUIContainer, NodeData, "Policy");
        }
    }
}
