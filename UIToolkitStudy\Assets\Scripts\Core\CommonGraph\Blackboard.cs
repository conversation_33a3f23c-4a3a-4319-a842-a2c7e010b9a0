﻿/*
 * Description:     Blackboard.cs
 * Author:          TonyTang
 * Create Date:     2023/09/14
 */

using System;
using System.Collections.Generic;
using UnityEngine;
using NPBehave;

namespace TCommonGraph
{
    /// <summary>
    /// 黑板模式，数据共享中心
    /// </summary>
    public class Blackboard
    {
        /// <summary>
        /// 黑板操作类型
        /// </summary>
        public enum BBOperationType
        {
            ADD,
            REMOVE,
            CHANGE
        }

        /// <summary>
        /// 通知数据
        /// </summary>
        private struct Notification
        {
            /// <summary>
            /// 操作Key
            /// </summary>
            public string key;

            /// <summary>
            /// 黑板操作类型
            /// </summary>
            public BBOperationType type;

            /// <summary>
            /// 操作值
            /// </summary>
            public object value;

            public Notification(string key, BBOperationType type, object value)
            {
                this.key = key;
                this.type = type;
                this.value = value;
            }
        }

        /// <summary>
        /// 时钟
        /// </summary>
        private Clock mClock;

        /// <summary>
        /// 黑板数据集合中心
        /// </summary>
        private Dictionary<string, BaseBlackboardData> mBlackboardDataMap;

        /// <summary>
        /// 所有Key的监听Map<黑板数据Key名, 黑板监听类型回调列表>
        /// </summary>
        private Dictionary<string, List<Action<BBOperationType, object>>> mObservers;

        /// <summary>
        /// 是否正在通知
        /// </summary>
        private bool mIsNotifying;

        /// <summary>
        /// 所有待添加的监听Key的监听Map<黑板数据Key名, 黑板监听类型回调列表>
        /// </summary>
        private Dictionary<string, List<Action<BBOperationType, object>>> mAddObservers;

        /// <summary>
        /// 所有等待移除的监听Key的监听Map<黑板数据Key名, 黑板监听类型回调列表>
        /// </summary>
        private Dictionary<string, List<Action<BBOperationType, object>>> mRemoveObservers;

        /// <summary>
        /// 等待通知列表
        /// </summary>
        private List<Notification> mNotifications;

        /// <summary>
        /// 正在通知的通知列表
        /// </summary>
        private List<Notification> mNotificationsDispatch;

        public Blackboard(Clock clock)
        {
            mClock = clock;
            mBlackboardDataMap = new Dictionary<string, BaseBlackboardData>();
            mObservers = new Dictionary<string, List<Action<BBOperationType, object>>>();
            mIsNotifying = false;
            mAddObservers = new Dictionary<string, List<Action<BBOperationType, object>>>();
            mRemoveObservers = new Dictionary<string, List<Action<BBOperationType, object>>>();
            mNotifications = new List<Notification>();
            mNotificationsDispatch = new List<Notification>();
        }

        /// <summary>
        /// 获取指定黑板数据值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <returns></returns>
        public T GetValue<T>(string key)
        {
            var blackboardData = GetBlackboardData(key);
            if (blackboardData != null)
            {
                var dataType = typeof(T);
                var targetData = blackboardData as BlackboardData<T>;
                if (targetData == null)
                {
                    Debug.LogError($"数据名:{key}的数据类型与目标类型:{dataType.Name}不匹配，获取数据值失败！");
                    return default(T);
                }
                return targetData.Data;
            }
            Debug.LogError($"找不到Key:{key}的数据，获取数据值失败！");
            return default(T);
        }

        /// <summary>
        /// 更新黑板数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="noNotification">是否不通知(默认通知)</param>
        /// <returns></returns>
        public bool UpdateData<T>(string key, T value = default, bool noNotification = false)
        {
            var data = GetBlackboardData(key);
            if (data != null)
            {
                var targetData = data as BlackboardData<T>;
                if (targetData == null)
                {
                    Debug.LogError($"黑板数据里Key:{key}的数据类型和更新数据类型:{typeof(T).Name}不匹配，更新数据失败！");
                    return false;
                }
                targetData.Data = value;
                if(!noNotification)
                {
                    mNotifications.Add(new Notification(key, BBOperationType.CHANGE, value));
                    mClock.AddTimer(0f, 0, NotifiyObservers);
                }
                return true;
            }
            else
            {
                BlackboardData<T> targetData = new BlackboardData<T>(key, value);
                mBlackboardDataMap.Add(key, targetData);
                if (!noNotification)
                {
                    mNotifications.Add(new Notification(key, BBOperationType.ADD, value));
                    mClock.AddTimer(0f, 0, NotifiyObservers);
                }
                return true;
            }
        }

        /// <summary>
        /// 移除指定数据名的黑板数据
        /// </summary>
        /// <param name="key"></param>
        /// <param name="noNotification">是否不通知(默认通知)</param>
        /// <returns></returns>
        public bool RemoveData(string key, bool noNotification = false)
        {
            var result = mBlackboardDataMap.Remove(key);
            if(result && !noNotification)
            {
                mNotifications.Add(new Notification(key, BBOperationType.REMOVE, null));
                mClock.AddTimer(0f, 0, NotifiyObservers);
            }
            return result;
        }

        /// <summary>
        /// 指定黑板数据Key是否存在
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public bool ExistData(string key)
        {
            var existData = GetBlackboardData(key);
            return existData != null;
        }

        /// <summary>
        /// 清除黑板数据
        /// </summary>
        public void ClearData()
        {
            var keys = mBlackboardDataMap.Keys;
            foreach(var key in keys)
            {
                RemoveData(key);
            }
        }

        /// <summary>
        /// 获取黑板指定数据
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public BaseBlackboardData GetBlackboardData(string key)
        {
            BaseBlackboardData value;
            if (!mBlackboardDataMap.TryGetValue(key, out value))
            {
                //Debug.LogError(string.Format("找不到Key:{0}的黑板数据!", key));
            }
            return value;
        }

        /// <summary>
        /// 获取所有的黑板数据Key列表
        /// </summary>
        /// <returns></returns>
        public void GetAllBlackboardKeysList<T>(ref List<string> keyList)
        {
            keyList.Clear();
            foreach(var blackboardData in mBlackboardDataMap)
            {
                if(blackboardData.Value is BlackboardData<T>)
                {
                    keyList.Add(blackboardData.Key);
                }
            }
        }

        /// <summary>
        /// 获取所有指定黑板数据类型的黑板数据Key列表
        /// </summary>
        /// <param name="keyList"></param>
        public void GetAllBlackboardKeyList<T>(ref List<string> keyList)
        {
            keyList.Clear();
            foreach(var blackboardData in mBlackboardDataMap)
            {
                if(blackboardData.Value is BlackboardData<T>)
                {
                    keyList.Add(blackboardData.Key);
                }
            }
        }

        /// <summary>
        /// 打印所有黑板数据
        /// </summary>
        public void PrintAllBlackBoardDatas()
        {
            Debug.Log($"打印黑板数据:");
            foreach (var blackboarddata in mBlackboardDataMap)
            {
                var blackboarddatatype = blackboarddata.Value.GetType();
                if (blackboarddatatype == CommonGraphConst.BlackboardBoolType)
                {
                    var realblackboarddata = blackboarddata.Value as BlackboardData<bool>;
                    Debug.Log($"变量名:{blackboarddata.Key}变量值:{realblackboarddata.Data}");
                }
                else if (blackboarddatatype == CommonGraphConst.BlackboardIntType)
                {
                    var realblackboarddata = blackboarddata.Value as BlackboardData<int>;
                    Debug.Log($"变量名:{blackboarddata.Key}变量值:{realblackboarddata.Data}");
                }
                else if (blackboarddatatype == CommonGraphConst.BlackboardFloatType)
                {
                    var realblackboarddata = blackboarddata.Value as BlackboardData<float>;
                    Debug.Log($"变量名:{blackboarddata.Key}变量值:{realblackboarddata.Data}");
                }
                else if (blackboarddatatype == CommonGraphConst.BlackboardStringType)
                {
                    var realblackboarddata = blackboarddata.Value as BlackboardData<string>;
                    Debug.Log($"变量名:{blackboarddata.Key}变量值:{realblackboarddata.Data}");
                }
                else
                {
                    Debug.LogError($"不支持的黑板数据类型:{blackboarddatatype},打印失败!");
                }
            }
        }

        /// <summary>
        /// 添加黑板数据操作监听
        /// </summary>
        /// <param name="key"></param>
        /// <param name="observer"></param>
        public void AddObserver(string key, System.Action<BBOperationType, object> observer)
        {
            List<System.Action<BBOperationType, object>> observers = GetObserverList(this.mObservers, key);
            if (!mIsNotifying)
            {
                if (!observers.Contains(observer))
                {
                    observers.Add(observer);
                }
            }
            else
            {
                if (!observers.Contains(observer))
                {
                    List<System.Action<BBOperationType, object>> addObservers = GetObserverList(this.mAddObservers, key);
                    if (!addObservers.Contains(observer))
                    {
                        addObservers.Add(observer);
                    }
                }

                List<System.Action<BBOperationType, object>> removeObservers = GetObserverList(this.mRemoveObservers, key);
                if (removeObservers.Contains(observer))
                {
                    removeObservers.Remove(observer);
                }
            }
        }

        /// <summary>
        /// 移除黑板数据操作监听
        /// </summary>
        /// <param name="key"></param>
        /// <param name="observer"></param>
        public void RemoveObserver(string key, System.Action<BBOperationType, object> observer)
        {
            List<System.Action<BBOperationType, object>> observers = GetObserverList(this.mObservers, key);
            if (!mIsNotifying)
            {
                if (observers.Contains(observer))
                {
                    observers.Remove(observer);
                }
            }
            else
            {
                List<System.Action<BBOperationType, object>> removeObservers = GetObserverList(this.mRemoveObservers, key);
                if (!removeObservers.Contains(observer))
                {
                    if (observers.Contains(observer))
                    {
                        removeObservers.Add(observer);
                    }
                }

                List<System.Action<BBOperationType, object>> addObservers = GetObserverList(this.mAddObservers, key);
                if (addObservers.Contains(observer))
                {
                    addObservers.Remove(observer);
                }
            }
        }

        /// <summary>
        /// 获取制定黑板数据Key的操作监听列表
        /// </summary>
        /// <param name="target"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        private List<System.Action<BBOperationType, object>> GetObserverList(Dictionary<string, List<System.Action<BBOperationType, object>>> target, string key)
        {
            List<System.Action<BBOperationType, object>> observers;
            if (target.ContainsKey(key))
            {
                observers = target[key];
            }
            else
            {
                observers = new List<System.Action<BBOperationType, object>>();
                target[key] = observers;
            }
            return observers;
        }

        /// <summary>
        /// 通知
        /// </summary>
        private void NotifiyObservers()
        {
            if (mNotifications.Count == 0)
            {
                return;
            }

            mNotificationsDispatch.Clear();
            mNotificationsDispatch.AddRange(mNotifications);
            mNotifications.Clear();

            mIsNotifying = true;
            foreach (Notification notification in mNotificationsDispatch)
            {
                if (!this.mObservers.ContainsKey(notification.key))
                {
                    //                Debug.Log("1 do not notify for key:" + notification.key + " value: " + notification.value);
                    continue;
                }

                List<System.Action<BBOperationType, object>> observers = GetObserverList(this.mObservers, notification.key);
                foreach (System.Action<BBOperationType, object> observer in observers)
                {
                    if (this.mRemoveObservers.ContainsKey(notification.key) && this.mRemoveObservers[notification.key].Contains(observer))
                    {
                        continue;
                    }
                    observer(notification.type, notification.value);
                }
            }

            foreach (string key in this.mAddObservers.Keys)
            {
                GetObserverList(this.mObservers, key).AddRange(this.mAddObservers[key]);
            }
            foreach (string key in this.mRemoveObservers.Keys)
            {
                foreach (System.Action<BBOperationType, object> action in mRemoveObservers[key])
                {
                    GetObserverList(this.mObservers, key).Remove(action);
                }
            }
            this.mAddObservers.Clear();
            this.mRemoveObservers.Clear();

            mIsNotifying = false;
        }
    }

    /// <summary>
    /// 黑板数据接口抽象
    /// </summary>
    [Serializable]
    public abstract class BaseBlackboardData
    {
        /// <summary>
        /// 黑板数据变量名
        /// </summary>
        [Header("变量名")]
        public string Name;

        public BaseBlackboardData(string name)
        {
            Name = name;
        }
    }

    /// <summary>
    /// 黑板数据泛型基类
    /// </summary>
    [Serializable]
    public class BlackboardData<T> : BaseBlackboardData
    {
        /// <summary>
        /// 数据
        /// </summary>
        [Header("数据")]
        public T Data;

        public BlackboardData(string name, T data) : base(name)
        {
            Data = data;
        }
    }
}