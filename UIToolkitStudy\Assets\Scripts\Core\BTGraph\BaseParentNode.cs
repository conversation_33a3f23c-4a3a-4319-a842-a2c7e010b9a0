﻿/*
 * Description:     BaseCompositionNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/11/13
 */

using System.Collections.Generic;

namespace TCommonGraph
{
    /// <summary>
    /// BaseParentNode.cs
    /// 父节点基类抽象(所有有子节点的类型都继承这个)
    /// </summary>
    public abstract class BaseParentNode : BaseNode
    {
        /// <summary>
        /// 子节点数量
        /// </summary>
        public int ChildNodeCount
        {
            get
            {
                return mChildNodeList != null ? mChildNodeList.Count : 0;
            }
        }

        /// <summary>
        /// 子节点列表
        /// </summary>
        protected List<BaseNode> mChildNodeList;

        /// <summary>
        /// 设置子节点列表
        /// </summary>
        /// <param name="childNodeList"></param>
        public virtual void SetChildNodeList(List<BaseNode> childNodeList)
        {
            mChildNodeList = childNodeList;
        }
    }
}
