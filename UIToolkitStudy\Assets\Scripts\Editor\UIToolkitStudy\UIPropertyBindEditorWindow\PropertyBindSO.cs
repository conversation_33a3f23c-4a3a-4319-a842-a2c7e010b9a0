﻿/*
 * Description:     PropertyBindSO.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/05/29
 */

using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// PropertyBindSO.cs
/// 属性绑定ScriptableObject
/// </summary>
[CreateAssetMenu(fileName = "PropertyBindSO", menuName = "ScriptableObjects/DIY/PropertyBindSO", order = 1)]
public class PropertyBindSO : ScriptableObject
{
    /// <summary>
    /// 自定义枚举
    /// </summary>
    public enum CustomEnum
    {
        ENUM1,
        ENUM2,
        ENUM3,
    }

    /// <summary>
    /// 名字
    /// </summary>
    [Header("名字")]
    public string Name;

    /// <summary>
    /// 颜色
    /// </summary>
    [Header("颜色")]
    public Color Color;

    /// <summary>
    /// 绑定GameObject
    /// </summary>
    [Header("绑定GameObject")]
    public GameObject GO;

    /// <summary>
    /// 自定义枚举
    /// </summary>
    [Header("自定义枚举")]
    public CustomEnum CustonE;

    /// <summary>
    /// Transform列表
    /// </summary>
    [Header("Transform列表")]
    public List<Transform> TransformList;

    /// <summary>
    /// 位置数组
    /// </summary>
    [Header("位置数组")]
    public Vector3[] Positions;

    /// <summary>
    /// 自定义Class
    /// </summary>
    [Header("自定义Class")]
    public CustomBindClass BindClass;
}