﻿/*
 * Description:     BaseGraphView.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/10/04
 */

using System;
/// <summary>
/// BaseGraphView.cs
/// </summary>
/// <typeparam name="T"></typeparam>
public abstract class BaseGraphView<T> : SuperBaseGraphView where T : BaseGraphData
{
    /// <summary>
    /// 初始化的图数据
    /// </summary>
    public T SourceGraphData
    {
        get;
        protected set;
    }

    /// <summary>
    /// 获取图数据类型信息
    /// </summary>
    /// <returns></returns>
    public Type GetGraphDataType()
    {
        return typeof(T);
    }

    /// <summary>
    /// 加载指定图数据
    /// </summary>
    /// <param name="graphData"></param>
    public virtual void LoadGraphData<T>(T graphData)
    {

    }
}