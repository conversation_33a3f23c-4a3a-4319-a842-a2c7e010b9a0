﻿/*
 * Description:     BTGraphElementNames.cs
 * Author:          TonyTang
 * Create Date:     2023/06/19
 */

namespace TCommonGraph
{
    /// <summary>
    /// BTGraphElementNames.cs
    /// 通用节点编辑器组件成员名
    /// </summary>
    public static class BTGraphElementNames
    {
        /// <summary>
        /// 横向内容容器名字
        /// </summary>
        public const string HorizontalContentContainerName = "HorizontalContentContainer";

        /// <summary>
        /// 菜单栏名字
        /// </summary>
        public const string MenuToolBarName = "MenuToolBar";

        /// <summary>
        /// 菜单栏文件名名字
        /// </summary>
        public const string ToolBarFileNameName = "ToolBarFileName";

        /// <summary>
        /// 左侧竖向容器名字
        /// </summary>
        public const string LeftVerticalContentContainerName = "LeftVerticalContentContainer";

        /// <summary>
        /// 中间竖向容器名字
        /// </summary>
        public const string MiddleGraphViewContainerName = "MiddleGraphViewContainer";

        /// <summary>
        /// 右侧竖向容器名字
        /// </summary>
        public const string RightVerticalContentContainerName = "RightVerticalContentContainer";

        /// <summary>
        /// 右侧菜单栏名字
        /// </summary>
        public const string RightToolbarMenuName = "RightToolbarMenu";

        /// <summary>
        /// 右侧节点参数面板按钮名字
        /// 
        /// </summary>
        public const string RightNodeConfigMenuButtonName = "RightNodeConfigMenuButton";

        /// <summary>
        /// 右侧黑板面板按钮名字
        /// </summary>
        public const string RightBlackboardMenuButtonName = "RightBlackboardMenuButton";

        /// <summary>
        /// 右侧面板竖向容器名字
        /// </summary>
        public const string RightPanelVerticalContainerName = "RightPanelVerticalContainer";

        /// <summary>
        /// 右侧节点配置竖向容器名字
        /// </summary>
        public const string RightNodeConfigVerticalContainerName = "RightNodeConfigVerticalContainer";

        /// <summary>
        /// 节点竖向容器名字
        /// </summary>
        public const string NodeVerticalContainerName = "NodeVerticalContainer";

        /// <summary>
        /// 黑板操作横向容器1名字
        /// </summary>
        public const string BlackboardOperationHorizontalContainer1Name = "BlackboardOperationHorizontalContainer1";

        /// <summary>
        /// 黑板操作横向容器2名字
        /// </summary>
        public const string BlackboardOperationHorizontalContainer2Name = "BlackboardOperationHorizontalContainer2";

        /// <summary>
        /// 黑板操作输入变量名文本名字
        /// </summary>
        public const string BlackboardInputVariableTextName = "BlackboardInputVariableText";

        /// <summary>
        /// 黑板变量类型选择下拉框名字
        /// </summary>
        public const string BlackboardVariableTypePopName = "BlackboardVariableTypePop";

        /// <summary>
        /// 黑板数据添加按钮名字
        /// </summary>
        public const string BlackboardAddVariableDataButtonName = "BlackboardAddVariableDataButton";

        /// <summary>
        /// 黑板数据竖向展示容器名字
        /// </summary>
        public const string BlackboardDataVerticalContainerName = "BlackboardDataVerticalContainer";

        /// <summary>
        /// 保存路径文本名
        /// </summary>
        public const string SavePathTextFieldName = "SavePathTextField";

        /// <summary>
        /// 新建按钮名
        /// </summary>
        public const string NewGraphButtonName = "NewGraphButton";

        /// <summary>
        /// 保存按钮名
        /// </summary>
        public const string SaveButtonName = "SaveButton";

        /// <summary>
        /// 背景网格组件名
        /// </summary>
        public const string GridBackgroundName = "GridBackground";

        /// <summary>
        /// Asset选择横向容器名
        /// </summary>
        public const string AssetSelectionHorizontalContainerName = "AssetSelectionHorizontalContainerName";

        /// <summary>
        /// Asset选择名
        /// </summary>
        public const string AssetSelectionName = "AssetSelectionName";

        /// <summary>
        /// 节点竖向UI容器名
        /// </summary>
        public const string NodeUIVerticalContainerName = "NodeUIVerticalContainer";

        /// <summary>
        /// 节点GUID横向UI容器名
        /// </summary>
        public const string NodeGUIDUIHorizontalContainerName = "NodeGUIDUIHorizontalContainerName";

        /// <summary>
        /// 节点状态横向UI容器名
        /// </summary>
        public const string NodeStateUIHorizontalContainerName = "NodeStateUIHorizontalContainerName";

        /// <summary>
        /// 节点状态Label名
        /// </summary>
        public const string NodeStateLabelName = "NodeStateLabelName";

        /// <summary>
        /// 节点描述横向UI容器名
        /// </summary>
        public const string NodeDesUIHorizontalContainerName = "NodeDesUIHorizontalContainerName";
    }
}