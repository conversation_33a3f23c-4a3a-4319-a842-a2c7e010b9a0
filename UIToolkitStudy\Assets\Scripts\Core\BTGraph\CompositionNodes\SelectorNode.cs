﻿/*
 * Description:     SelectorNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/06/28
 */

using System;

namespace TCommonGraph
{
    /// <summary>
    /// SelectorNode.cs
    /// 行为树选择节点(多个几点里选择一个符合条件的执行)
    /// Note:
    /// 暂时不支持带优先级的选择节点策略(默认从左往右优先级越来越低)
    /// </summary>
    [Serializable]
    public class SelectorNode : BaseCompositionNode
    {
        /// <summary>
        /// 当前执行子节点索引
        /// </summary>
        protected int mCurrentIndex = -1;
        
        /// <summary>
        /// 执行开始
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            mCurrentIndex = -1;
            ProcessChildren();
        }

        /// <summary>
        /// 响应打断
        /// </summary>
        protected override void DoAbort()
        {
            base.DoAbort();
            if(mChildNodeList != null)
            {
                mChildNodeList[mCurrentIndex].Abort();

            }
        }

        /// <summary>
        /// 执行子节点停止
        /// </summary>
        /// <param name="child"></param>
        /// <param name="success"></param>
        protected override void DoChildStop(BaseNode child, bool success)
        {
            base.DoChildStop(child, success);
            if (success)
            {
                Stop(true);
            }
            else
            {
                ProcessChildren();
            }
        }

        /// <summary>
        /// 执行子节点
        /// </summary>
        protected void ProcessChildren()
        {
            mCurrentIndex++;
            if(mCurrentIndex < ChildNodeCount)
            {
                if(IsAbort)
                {
                    Stop(false);
                }
                else
                {
                    mChildNodeList[mCurrentIndex].Start();
                }
            }
            else
            {
                Stop(false);
            }
        }

        /// <summary>
        /// 停止比指定子节点优先级低的节点，并决定是否重新开启组合节点
        /// </summary>
        /// <param name="child"></param>
        /// <param name="immediateRestart"></param>
        public override void StopLowerPriorityChildrenForChild(BaseNode child, bool immediateRestart)
        {
            int indexForChild = 0;
            bool found = false;
            foreach (BaseNode currentChild in mChildNodeList)
            {
                if (currentChild == child)
                {
                    found = true;
                }
                else if (!found)
                {
                    indexForChild++;
                }
                else if (found && currentChild.IsRunning)
                {
                    if (immediateRestart)
                    {
                        mCurrentIndex = indexForChild - 1;
                    }
                    else
                    {
                        mCurrentIndex = ChildNodeCount;
                    }
                    currentChild.Abort();
                    break;
                }
            }
        }

        /// <summary>
        /// 字符串表达
        /// </summary>
        public override string ToString()
        {
            return $"{base.ToString()},mCurrentIndex:{mCurrentIndex}";
        }
    }
}