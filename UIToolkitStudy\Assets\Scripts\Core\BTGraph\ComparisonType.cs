﻿/*
 * Description:     ComparisonType.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/12/06
 */

namespace TCommonGraph
{
    /// <summary>
    /// ComparisonType.cs
    /// 比较类型枚举
    /// </summary>
    public enum ComparisonType
    {
        EQUAL,                      // 相等
        LESS,                       // 小于
        GREATER,                    // 大于
        LESS_AND_EQUAL,             // 小于等于
        GREATER_AND_EQUAL,          // 大于等于
    }
}
