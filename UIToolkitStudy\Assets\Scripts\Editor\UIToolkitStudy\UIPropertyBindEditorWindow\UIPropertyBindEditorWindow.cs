﻿/*
 * Description:     UIPropertyBindEditorWindow.cs
 * Author:          TonyTang
 * Create Date:     2023/05/29
 */

using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;
using UnityEditor.UIElements;

/// <summary>
/// 属性绑定Editor Window
/// </summary>
public class UIPropertyBindEditorWindow : EditorWindow
{
    /// <summary>
    /// 选中的ScriptableObject
    /// </summary>
    private ScriptableObject mSelectedScriptableObject;

    [MenuItem("Window/UI Toolkit/UIToolkitStudy/UIPropertyBindEditorWindow")]
    public static void ShowUIPropertyBindEditorWindow()
    {
        UIPropertyBindEditorWindow wnd = GetWindow<UIPropertyBindEditorWindow>();
        wnd.titleContent = new GUIContent("UIPropertyBindEditorWindow");
    }

    public void CreateGUI()
    {
        CreatePropertyBindUI();
        OnSelectionChange();
    }

    /// <summary>
    /// 选中Asset变化回调
    /// </summary>
    private void OnSelectionChange()
    {
        mSelectedScriptableObject = Selection.activeObject as ScriptableObject;
        var propertyBindAssetNameTextField = rootVisualElement.Q<TextField>(PropertyBindElementNames.PropertyBindTextFieldName);
        var propertyBindInfoContentContainer = rootVisualElement.Q<VisualElement>(PropertyBindElementNames.PropertyBindInfoContentContainerName);
        if(mSelectedScriptableObject != null)
        {
            var selectedSerializedObject = new SerializedObject(mSelectedScriptableObject);
            if(propertyBindAssetNameTextField != null)
            {
                var nameProperty = selectedSerializedObject.FindProperty("m_Name");
                propertyBindAssetNameTextField.BindProperty(nameProperty);
            }
            if(propertyBindInfoContentContainer != null)
            {
                var propertyIterator = selectedSerializedObject.GetIterator();
                propertyIterator.NextVisible(true);
                while(propertyIterator.NextVisible(false))
                {
                    var propertyField = new PropertyField();
                    propertyField.name = propertyIterator.name;
                    //propertyField.style.height = 20;
                    propertyField.BindProperty(propertyIterator);
                    propertyBindInfoContentContainer.Add(propertyField);
                }
            }
        }
        else
        {
            if(propertyBindAssetNameTextField != null)
            {
                propertyBindAssetNameTextField.Unbind();
                propertyBindAssetNameTextField.value = string.Empty;
            }
            if(propertyBindInfoContentContainer != null)
            {
                propertyBindInfoContentContainer.Clear();
            }
        }
    }

    /// <summary>
    /// 创建属性绑定UI
    /// </summary>
    private void CreatePropertyBindUI()
    {
        CreatePropertyBindAssetInfoUI();
        CreatePropertyBindPropertyUI();
    }

    /// <summary>
    /// 创建属性绑定Asset信息UI
    /// </summary>
    private void CreatePropertyBindAssetInfoUI()
    {
        var propertyBindAssetInfoHContainer = new VisualElement();
        propertyBindAssetInfoHContainer.style.flexDirection = FlexDirection.Row;
        propertyBindAssetInfoHContainer.style.height = 20;
        rootVisualElement.Add(propertyBindAssetInfoHContainer);

        var propertyBindLabelTitle = new Label();
        propertyBindLabelTitle.text = "绑定Asset名";
        propertyBindLabelTitle.style.width = 80;
        propertyBindAssetInfoHContainer.Add(propertyBindLabelTitle);

        var propertyBindAssetNameTextField = new TextField();
        propertyBindAssetNameTextField.name = PropertyBindElementNames.PropertyBindTextFieldName;
        propertyBindAssetNameTextField.style.flexGrow = 1;
        propertyBindAssetNameTextField.value = string.Empty;
        propertyBindAssetInfoHContainer.Add(propertyBindAssetNameTextField);
    }

    /// <summary>
    /// 创建属性绑定属性UI
    /// </summary>
    private void CreatePropertyBindPropertyUI()
    {
        var propertyBindInfoVContainer = new VisualElement();
        propertyBindInfoVContainer.style.flexDirection = FlexDirection.Column;
        propertyBindInfoVContainer.style.left = 0;
        propertyBindInfoVContainer.style.right = 0;
        propertyBindInfoVContainer.style.top = 0;
        propertyBindInfoVContainer.style.bottom = 0;
        propertyBindInfoVContainer.style.flexGrow = 1;
        propertyBindInfoVContainer.AddToClassList("unity-rect-field");
        rootVisualElement.Add(propertyBindInfoVContainer);

        var propertyBindInfoLabelTitle = new Label();
        propertyBindInfoLabelTitle.text = "绑定Assets绑定属性显示";
        propertyBindInfoLabelTitle.style.alignSelf = Align.Center;
        propertyBindInfoLabelTitle.style.height = 20;
        propertyBindInfoVContainer.Add(propertyBindInfoLabelTitle);

        var propertyBindInfoContentContainer = new VisualElement();
        propertyBindInfoContentContainer.name = PropertyBindElementNames.PropertyBindInfoContentContainerName;
        propertyBindInfoContentContainer.style.flexDirection = FlexDirection.Column;
        propertyBindInfoContentContainer.style.flexGrow = 1;
        propertyBindInfoVContainer.Add(propertyBindInfoContentContainer);
    }
}