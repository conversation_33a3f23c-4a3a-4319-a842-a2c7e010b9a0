﻿/*
 * Description:             BehaviourTreeGraphView.cs
 * Author:                  TONYTANG
 * Create Date:             2023/10/04
 */

using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.Experimental.GraphView;
using UnityEngine;
using UnityEngine.UIElements;

namespace TCommonGraph
{
    /// <summary>
    /// BehaviourTreeGraphView.cs
    /// 行为树GraphView
    /// </summary>
    public class BehaviourTreeGraphView : GraphView
    {
        /// <summary>
        /// 菜单操作类型
        /// </summary>
        public enum MenuOperationType
        {
            CreateNode = 1,             // 创建节点
            DeleteNodeOnly,             // 删除节点自身
            DeleteNodeRecusive,         // 递归删除节点
            DuplicatedNode,             // 复制粘贴节点
        }

        /// <summary>
        /// 菜单自定义数据
        /// </summary>
        public class MenuUserData
        {
            /// <summary>
            /// 菜单操作类型
            /// </summary>
            public MenuOperationType OperationType
            {
                get;
                private set;
            }

            /// <summary>
            /// 菜单操作VisualElement
            /// </summary>
            public VisualElement OperationElement
            {
                get;
                private set;
            }

            /// <summary>
            /// 自定义数据
            /// </summary>
            public System.Object CustomData
            {
                get;
                private set;
            }

            public MenuUserData(MenuOperationType operationType, VisualElement operationElement, System.Object customData)
            {
                OperationType = operationType;
                OperationElement = operationElement;
                CustomData = customData;
            }
        }

        /// <summary>
        /// 根节点类型(子类重写实现自定义不同根节点类型)
        /// </summary>
        protected readonly Type RootNodeType = BTGraphConstEditor.RootNodeType;

        /// <summary>
        /// Style Sheet Asset路径
        /// </summary>
        protected string StyleSheetAssetPath
        {
            get
            {
                return "Assets/Scripts/Editor/UIToolkitStudy/UIBTGraphEditorWindow/DefaultGraphStyleSheet.uss";
            }
        }

        /// <summary>
        /// GraphView朝向
        /// </summary>
        protected Orientation GraphOrientation
        {
            get
            {
                return Orientation.Horizontal;
            }
        }

        /// <summary>
        /// 节点类型基类类型信息Map<节点类型, 节点类型基类类型信息>
        /// </summary>
        protected Dictionary<NodeType, Type> mNodeTypeBaseTypeMap;

        /// <summary>
        /// Port可连接节点类型Map<节点类型, <可连接节点类型, 可连接节点类型>>
        /// </summary>
        protected Dictionary<NodeType, Dictionary<NodeType, NodeType>> mPortAvailableConnectNodeTypeMap;

        /// <summary>
        /// Port不可连接节点类型信息Map<节点类型, <不可连接节点类型信息, 不可连接节点类型信息>>
        /// </summary>
        protected Dictionary<NodeType, Dictionary<Type, Type>> mPortUnavailableConnectTypeMap;

        /// <summary>
        /// 节点类型可用通用菜单操作类型列表Map<节点类型, 可用通用菜单操作类型列表>
        /// </summary>
        protected Dictionary<NodeType, List<MenuOperationType>> mNodeCommonOperationTypesMap;

        /// <summary>
        /// 节点类型可用菜单节点类型信息列表Map<节点类型, <节点类型, 可用节点类型信息列表>>
        /// </summary>
        protected Dictionary<NodeType, Dictionary<NodeType, List<Type>>> mNodeTypeMenuTypesMap;

        /// <summary>
        /// GraphView可用菜单节点类型信息列表Map<节点类型, 可用节点类型信息列表>>
        /// </summary>
        protected Dictionary<NodeType, List<Type>> mGraphViewMenuTypesMap;

        /// <summary>
        /// 可连接的Port列表
        /// </summary>
        protected List<Port> mCompatiblePortList;

        /// <summary>
        /// 选中节点
        /// </summary>
        public NodeView SelectedNode
        {
            get;
            protected set;
        }

        /// <summary>
        /// 删除Elements临时列表
        /// </summary>
        protected List<GraphElement> mDeleteElementTempList;

        /// <summary>
        /// 临时节点输出端口更新Map<节点GUID, 节点输出端口名列表>
        /// </summary>
        protected Dictionary<string, List<string>> mTempNodeOutputPortUpdateMap;

        /// <summary>
        /// 可用的节点类型列表
        /// </summary>
        private List<NodeType> mAvailableNodeTypeList;

        /// <summary>
        /// 选中节点变化委托
        /// </summary>
        protected Action<NodeView> mSelectedNodeChangeDelegate;

        /// <summary>
        /// 初始化的图数据
        /// </summary>
        public BehaviourTreeGraphData SourceGraphData
        {
            get;
            protected set;
        }

        public BehaviourTreeGraphView()
        {
            mAvailableNodeTypeList = new List<NodeType>();
            mSelectedNodeChangeDelegate = null;
            Init();
        }

        /// <summary>
        /// 带参构造函数
        /// </summary>
        /// <param name="availableNodeTypeList"></param>
        /// <param name="selectedNodeChangeCB"></param>
        public BehaviourTreeGraphView(List<NodeType> availableNodeTypeList, Action<NodeView> selectedNodeChangeCB = null)
        {
            mAvailableNodeTypeList = availableNodeTypeList;
            mSelectedNodeChangeDelegate += selectedNodeChangeCB;
            Init();
        }

        /// <summary>
        /// EditorWindow那方驱动过来的OnInspectorUpdate更新
        /// </summary>
        public void OnInspectorUpdate()
        {
        }

        /// <summary>
        /// EditorWindow那方驱动过来的Update更新
        /// </summary>
        public void Update()
        {
            UpdateAllNodeStateView();
            UpdateAllEdgeViewColors();
        }

        /// <summary>
        /// 更新所有节点状态显示
        /// </summary>
        protected void UpdateAllNodeStateView()
        {
            if (SourceGraphData == null)
            {
                return;
            }
            for (int i = 0; i < SourceGraphData.AllNodeList.Count; i++)
            {
                var node = SourceGraphData.AllNodeList[i];
                var nodeView = GetNodeByGuid(node.GUID) as NodeView;
                nodeView?.UpdateNodeStateBackgroundColor();
                nodeView?.UpdateNodeStateLabel();
            }
        }

        /// <summary>
        /// 更新所有显示边颜色
        /// </summary>
        protected void UpdateAllEdgeViewColors()
        {
            if (SourceGraphData == null)
            {
                return;
            }
            for (int i = 0; i < SourceGraphData.AllEdgeDataList.Count; i++)
            {
                var edge = SourceGraphData.AllEdgeDataList[i];
                var edgeView = GetEdgeByGuid(edge.GUID) as EdgeView;
                edgeView?.UpdateEdgeControlStateColor();
            }
        }

        /// <summary>
        /// 响应销毁
        /// </summary>
        public void OnDestroy()
        {
            Debug.Log($"BaseGraphView:OnDestry()");
            RemoveAllEvents();
        }

        /// <summary>
        /// 初始化
        /// </summary>
        protected void Init()
        {
            mDeleteElementTempList = new List<GraphElement>();
            mTempNodeOutputPortUpdateMap = new Dictionary<string, List<string>>();
            LoadStyleSheet();
            InitNodeTypeBaseTypeData();
            InitPortConnectRuleData();
            InitMenuData();
            AddAllManipulator();
            AddGridBackground();
            InitGraphData();
            AddEntryPointNode();
            AddAllEvents();
        }

        /// <summary>
        /// 加载指定图数据
        /// </summary>
        /// <param name="graphData"></param>
        public void LoadGraphData(BehaviourTreeGraphData graphData)
        {
            if (graphData == null)
            {
                Debug.LogError($"请勿加载空GraphData！");
                return;
            }
            // 因为后续清除逻辑需要先停止监听回调，避免重新加载数据导致的不必要数据清理
            RemoveAllEvents();
            // 因为回调后续处理原因，清理选中数据需要在切换GraphData之前处理
            ClearSelectedNodeData();
            DeleteAllElements();
            UpdateSourceGraphData(graphData);
            CreateGraphAllNodes(SourceGraphData, false);
            CreateGraphAllEdges(SourceGraphData, false);
            // 所有节点和边都加载还原后，才能监听事件正常操作
            AddAllEvents();
        }

        /// <summary>
        /// 更新图数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="graphData"></param>
        protected void UpdateSourceGraphData(BehaviourTreeGraphData graphData)
        {
            SourceGraphData = graphData;
        }

        /// <summary>
        /// 指定位置添加图数据
        /// </summary>
        /// <param name="graphData"></param>
        /// <param name="targetPos"></param>
        /// <returns></returns>
        public bool AddGraphData(BehaviourTreeGraphData graphData, Vector2? targetPos = null)
        {
            if (graphData == null)
            {
                Debug.LogError($"不允许添加空图数据，添加图数据失败！");
                return false;
            }
            graphData.RegenerateAllNodeGUID();
            if (targetPos != null)
            {
                graphData.MoveToTargetPosition((Vector2)targetPos);
            }
            CreateGraphAllNodes(graphData, true, false);
            CreateGraphAllEdges(graphData, true, false);
            Debug.Log($"添加指定Graph:{graphData.name}数据到指定位置成功！");
            return true;
        }

        /// <summary>
        /// 加载Style Sheet
        /// </summary>
        protected void LoadStyleSheet()
        {
            var styleSheet = AssetDatabase.LoadAssetAtPath<StyleSheet>(StyleSheetAssetPath);
            styleSheets.Add(styleSheet);
        }

        /// <summary>
        /// 初始化节点类型对应基类类型信息数据
        /// </summary>
        protected void InitNodeTypeBaseTypeData()
        {
            mNodeTypeBaseTypeMap = new Dictionary<NodeType, Type>();
            foreach (var availableNodeType in mAvailableNodeTypeList)
            {
                var baseType = BTGraphUtilitiesEditor.GetBaseTypeByNodeType(availableNodeType);
                AddNodeTypeBaseType(availableNodeType, baseType);
            }
        }

        /// <summary>
        /// 添加指定节点类型的基类类型信息
        /// </summary>
        /// <param name="nodeType"></param>
        /// <param name="baseType"></param>
        /// <returns></returns>
        protected bool AddNodeTypeBaseType(NodeType nodeType, Type baseType)
        {
            if (baseType == null)
            {
                Debug.LogError($"不允许添加节点类型:{nodeType}的空节点基类类型信息！");
                return false;
            }
            if (mNodeTypeBaseTypeMap.ContainsKey(nodeType))
            {
                Debug.LogError($"重复添加节点类型:{nodeType}的基类类型信息:{baseType.Name}");
                return false;
            }
            mNodeTypeBaseTypeMap.Add(nodeType, baseType);
            return true;
        }

        /// <summary>
        /// 初始化Port连接规则数据
        /// </summary>
        protected void InitPortConnectRuleData()
        {
            mCompatiblePortList = new List<Port>();
            InitPortAvalibleConnectNodeTypeData();
            InitPortUnavalibleConnectTypeData();
        }

        /// <summary>
        /// 初始化Port可连接节点类型数据
        /// </summary>
        protected void InitPortAvalibleConnectNodeTypeData()
        {
            mPortAvailableConnectNodeTypeMap = new Dictionary<NodeType, Dictionary<NodeType, NodeType>>();
            AddPortAvalibleConnectNodeType(NodeType.Composition, NodeType.Composition);
            AddPortAvalibleConnectNodeType(NodeType.Composition, NodeType.Decoration);
            AddPortAvalibleConnectNodeType(NodeType.Composition, NodeType.Condition);
            AddPortAvalibleConnectNodeType(NodeType.Composition, NodeType.Action);

            AddPortAvalibleConnectNodeType(NodeType.Decoration, NodeType.Composition);
            AddPortAvalibleConnectNodeType(NodeType.Decoration, NodeType.Decoration);
            AddPortAvalibleConnectNodeType(NodeType.Decoration, NodeType.Condition);
            AddPortAvalibleConnectNodeType(NodeType.Decoration, NodeType.Action);
        }

        /// <summary>
        /// 添加Port指定节点类型可连接节点类型
        /// </summary>
        /// <param name="sourceNodeType"></param>
        /// <param name="targetNodeType"></param>
        /// <returns></returns>
        protected bool AddPortAvalibleConnectNodeType(NodeType sourceNodeType, NodeType targetNodeType)
        {
            Dictionary<NodeType, NodeType> portAvalibleConnectNodeTypeMap;
            if (!mPortAvailableConnectNodeTypeMap.TryGetValue(sourceNodeType, out portAvalibleConnectNodeTypeMap))
            {
                portAvalibleConnectNodeTypeMap = new Dictionary<NodeType, NodeType>();
                mPortAvailableConnectNodeTypeMap.Add(sourceNodeType, portAvalibleConnectNodeTypeMap);
            }
            if (portAvalibleConnectNodeTypeMap.ContainsKey(targetNodeType))
            {
                Debug.LogError($"重复添加节点类型:{sourceNodeType}的Port可连接节点类型:{targetNodeType}");
                return false;
            }
            portAvalibleConnectNodeTypeMap.Add(targetNodeType, targetNodeType);
            return true;
        }

        /// <summary>
        /// 初始化Port不可连接类型信息数据
        /// </summary>
        protected void InitPortUnavalibleConnectTypeData()
        {
            mPortUnavailableConnectTypeMap = new Dictionary<NodeType, Dictionary<Type, Type>>();
        }

        /// <summary>
        /// 添加Port指定节点类型不可连接节点类型信息
        /// </summary>
        /// <param name="sourceNodeType"></param>
        /// <param name="targetType"></param>
        /// <returns></returns>
        protected bool AddPortUnavalibleConnectType(NodeType sourceNodeType, Type targetType)
        {
            Dictionary<Type, Type> portUnavalibleConnectTypeMap;
            if (!mPortUnavailableConnectTypeMap.TryGetValue(sourceNodeType, out portUnavalibleConnectTypeMap))
            {
                portUnavalibleConnectTypeMap = new Dictionary<Type, Type>();
                mPortUnavailableConnectTypeMap.Add(sourceNodeType, portUnavalibleConnectTypeMap);
            }
            if (portUnavalibleConnectTypeMap.ContainsKey(targetType))
            {
                Debug.LogError($"重复添加节点类型:{sourceNodeType}的Port不可连接节点类型信息:{targetType.Name}");
                return false;
            }
            portUnavalibleConnectTypeMap.Add(targetType, targetType);
            return true;
        }

        /// <summary>
        /// 初始化节点菜单数据
        /// </summary>
        protected void InitMenuData()
        {
            InitNodeCommonMenuOperationDatas();
            InitNodeMenuTypesData();
            InitGraphViewMenuTypesData();
        }

        /// <summary>
        /// 初始化节点通用菜单操作数据
        /// </summary>
        protected void InitNodeCommonMenuOperationDatas()
        {
            mNodeCommonOperationTypesMap = new Dictionary<NodeType, List<MenuOperationType>>();
            var nodeTypeValues = Enum.GetValues(typeof(NodeType));
            foreach (var nodeTypeValue in nodeTypeValues)
            {
                var commonMenuOperationTypeList = new List<MenuOperationType>();
                mNodeCommonOperationTypesMap.Add((NodeType)nodeTypeValue, commonMenuOperationTypeList);
                commonMenuOperationTypeList.Add(MenuOperationType.DeleteNodeOnly);
                commonMenuOperationTypeList.Add(MenuOperationType.DeleteNodeRecusive);
                commonMenuOperationTypeList.Add(MenuOperationType.DuplicatedNode);
            }
        }

        /// <summary>
        /// 初始化节点菜单类型信息数据
        /// </summary>
        protected void InitNodeMenuTypesData()
        {
            mNodeTypeMenuTypesMap = new Dictionary<NodeType, Dictionary<NodeType, List<Type>>>();
            foreach (var portAvalibleConnectNodeTypeInfo in mPortAvailableConnectNodeTypeMap)
            {
                if (portAvalibleConnectNodeTypeInfo.Value.Count == 0)
                {
                    continue;
                }
                var nodeTypeMenuTypeMap = new Dictionary<NodeType, List<Type>>();
                mNodeTypeMenuTypesMap.Add(portAvalibleConnectNodeTypeInfo.Key, nodeTypeMenuTypeMap);
                foreach (var portAvalibleConnectNodeTypeMap in portAvalibleConnectNodeTypeInfo.Value)
                {
                    var targetNodeType = portAvalibleConnectNodeTypeMap.Key;
                    var targetNodeBaseType = GetBaseTypeByNodeType(targetNodeType);
                    if (targetNodeBaseType == null)
                    {
                        continue;
                    }
                    var menuTypeList = new List<Type>();
                    nodeTypeMenuTypeMap.Add(targetNodeType, menuTypeList);
                    var allSubTypeList = TypeUtilities.GetAllSubTypes(targetNodeBaseType);
                    foreach (var subType in allSubTypeList)
                    {
                        if (subType.IsAbstract || IsPortUnavalibleConnectType(targetNodeType, subType))
                        {
                            continue;
                        }
                        menuTypeList.Add(subType);
                    }
                    // 移除没有任何一个有效类型的节点类型数据
                    if (menuTypeList.Count == 0)
                    {
                        nodeTypeMenuTypeMap.Remove(targetNodeType);
                    }
                }
            }
        }

        /// <summary>
        /// 初始化GraphView菜单数据
        /// </summary>
        protected void InitGraphViewMenuTypesData()
        {
            mGraphViewMenuTypesMap = new Dictionary<NodeType, List<Type>>();
            foreach (var availableNodeType in mAvailableNodeTypeList)
            {
                var targetNodeBaseType = GetBaseTypeByNodeType(availableNodeType);
                if (targetNodeBaseType == null)
                {
                    continue;
                }
                var menuTypeList = new List<Type>();
                mGraphViewMenuTypesMap.Add(availableNodeType, menuTypeList);
                var allSubTypeList = TypeUtilities.GetAllSubTypes(targetNodeBaseType);
                foreach (var subType in allSubTypeList)
                {
                    if (subType.IsAbstract)
                    {
                        continue;
                    }
                    menuTypeList.Add(subType);
                }
                // 移除没有任何一个有效类型的节点类型数据
                if (menuTypeList.Count == 0)
                {
                    mGraphViewMenuTypesMap.Remove(availableNodeType);
                }
            }
        }

        /// <summary>
        /// 添加所有Manipulator
        /// </summary>
        protected void AddAllManipulator()
        {
            this.AddManipulator(new ContentDragger());
            this.AddManipulator(new SelectionDragger());
            this.AddManipulator(new RectangleSelector());
            this.AddManipulator(new ContentZoomer());
        }

        /// <summary>
        /// 添加网格背景
        /// </summary>
        protected void AddGridBackground()
        {
            var grid = new GridBackground();
            grid.name = BTGraphElementNames.GridBackgroundName;
            Insert(0, grid);
            grid.StretchToParentSize();
        }

        /// <summary>
        /// 初始化图数据
        /// </summary>
        protected void InitGraphData()
        {
            SourceGraphData = CreateGraphDataInstance();
            SourceGraphData.SetGraphOrientation(GraphOrientation);
        }

        /// <summary>
        /// 创建GraphData的实例对象
        /// </summary>
        /// <returns></returns>
        protected BehaviourTreeGraphData CreateGraphDataInstance()
        {
            return ScriptableObject.CreateInstance<BehaviourTreeGraphData>();
        }

        /// <summary>
        /// 添加根节点
        /// </summary>
        protected void AddEntryPointNode()
        {
            AddElement(GenerateEntryPointNode());
        }

        /// <summary>
        /// 添加所有监听
        /// </summary>
        protected void AddAllEvents()
        {
            graphViewChanged += OnGraphViewChanged;
            RegisterCallback<DragExitedEvent>(OnDragExistedEvent);

            AddUndoRedoListener();
        }

        /// <summary>
        /// 移除UndoRedo监听
        /// </summary>
        protected void RemoveUndoRedoLitener()
        {
            Undo.undoRedoPerformed -= OnUndoRedoPerformed;
        }

        /// <summary>
        /// 添加UndoRedo监听
        /// </summary>
        protected void AddUndoRedoListener()
        {
            RemoveUndoRedoLitener();
            Undo.undoRedoPerformed += OnUndoRedoPerformed;
        }

        /// <summary>
        /// 移除所有监听
        /// </summary>
        protected void RemoveAllEvents()
        {
            graphViewChanged -= OnGraphViewChanged;
            UnregisterCallback<DragExitedEvent>(OnDragExistedEvent);

            RemoveUndoRedoLitener();
        }

        /// <summary>
        /// 生成根节点
        /// </summary>
        /// <returns></returns>
        protected NodeView GenerateEntryPointNode()
        {
            var rootNode = CreateNodeByType(RootNodeType, BTGraphConstEditor.NodeDefaultPos);
            return rootNode;
        }

        /// <summary>
        /// 创建指定类型信息和节点名的新节点
        /// </summary>
        /// <param name="graphData">所属图数据</param>
        /// <param name="nodeData">自定义节点数据(传这个时不创建新节点Node)</param>
        /// <param name="addNodeData">是否添加节点数据</param>
        /// <param name="enableUndoSystem">是否开启Undo系统</param>
        /// <returns></returns>
        public NodeView CreateNodeByNodeData(BehaviourTreeGraphData graphData, BaseNode nodeData, bool addNodeData = true, bool enableUndoSystem = true)
        {
            if (graphData == null || nodeData == null)
            {
                Debug.LogError($"不允许传递空图数据或空节点数据创建显示节点！");
                return null;
            }
            BaseNode newNode = null;
            newNode = nodeData;
            nodeData.Init(nodeData.GUID, nodeData.Position, nodeData.NodeState);
            var typeInfo = nodeData.GetType();
            var nodeView = BTGraphUtilitiesEditor.CreateNodeViewInstance(typeInfo);
            nodeView.Init(graphData, newNode, OnNodeSelected, null, GraphOrientation);
            if (addNodeData)
            {
                AddNodeData(nodeData, enableUndoSystem);
            }
            AddElement(nodeView);
            UpdateNodeViewRect(nodeView, nodeData.Position);
            return nodeView;
        }

        /// <summary>
        /// 创建指定节点名的新节点
        /// </summary>
        /// <param name="isEntryNode">是否是根节点</param>
        /// <returns></returns>
        public NodeView CreateNode<T>() where T : BaseNode, new()
        {
            var typeInfo = typeof(T);
            var nodeView = CreateNodeByType(typeInfo, BTGraphConstEditor.NodeDefaultPos);
            return nodeView;
        }

        /// <summary>
        /// 创建指定类型信息和节点名的新节点
        /// </summary>
        /// <param name="typeInfo">类型信息</param>
        /// <param name="position"></param>
        /// <param name="isEntryNode">是否是根节点</param>
        /// <returns></returns>
        public NodeView CreateNodeByType(Type typeInfo, Vector2 position)
        {
            var newNode = BTGraphUtilitiesEditor.CreateNodeInstance(typeInfo);
            var guid = Guid.NewGuid().ToString();
            var nodeRect = BTGraphConstEditor.NodeDefaultRect;
            nodeRect.x = position.x;
            nodeRect.y = position.y;
            newNode.Init(guid, nodeRect);
            var nodeView = BTGraphUtilitiesEditor.CreateNodeViewInstance(typeInfo);
            nodeView.Init(SourceGraphData, newNode, OnNodeSelected, null, GraphOrientation);
            AddNodeData(newNode);
            AddElement(nodeView);
            UpdateNodeViewRect(nodeView, nodeRect);
            return nodeView;
        }

        /// <summary>
        /// 获取所有子节点View
        /// Note:
        /// 不含节点View自身
        /// </summary>
        /// <param name="nodeView"></param>
        /// <param name="recusive"></param>
        /// <returns></returns>
        public List<NodeView> GetAllChildNodeViews(NodeView nodeView, bool recusive = true)
        {
            if (nodeView == null)
            {
                return null;
            }
            var allChildNodeViews = new List<NodeView>();
            var allOuputPortList = nodeView.outputContainer.Query<Port>().ToList();
            foreach (var outputPort in allOuputPortList)
            {
                foreach (var edge in outputPort.connections)
                {
                    var outputNodeView = edge.input.node as NodeView;
                    if (outputNodeView != null && !allChildNodeViews.Contains(outputNodeView))
                    {
                        allChildNodeViews.Add(outputNodeView);
                    }
                }
            }

            if (recusive)
            {
                for (int index = 0; index < allChildNodeViews.Count; index++)
                {
                    var childNodeView = allChildNodeViews[index];
                    var allChildChildNodeView = GetAllChildNodeViews(childNodeView, recusive);
                    allChildNodeViews.AddRange(allChildChildNodeView);
                }
            }
            return allChildNodeViews;
        }

        /// <summary>
        /// 获取可连接的Port
        /// </summary>
        /// <param name="startPort"></param>
        /// <param name="nodeAdapter"></param>
        /// <returns></returns>
        public override List<Port> GetCompatiblePorts(Port startPort, NodeAdapter nodeAdapter)
        {
            mCompatiblePortList.Clear();
            foreach (var port in ports)
            {
                // 不允许连接自身Port || 不允许连接自身节点 || 不允许连接相同In or Out Port
                if (startPort == port || startPort.node == port.node || startPort.direction == port.direction)
                {
                    continue;
                }
                var startNode = startPort.node as NodeView;
                var portNode = port.node as NodeView;
                if (!IsPortAvalibleConnectNodeType(startNode.NodeData.NodeType, portNode.NodeData.NodeType))
                {
                    continue;
                }
                var portNodeType = port.node.GetType();
                if (IsPortUnavalibleConnectType(startNode.NodeData.NodeType, portNodeType))
                {
                    continue;
                }
                // 不允许连接根节点的InputPort
                if (portNode.NodeData.EntryPoint && port.direction == Direction.Input)
                {
                    continue;
                }
                // 根节点的InputPort不允许连接任何OutputPort
                if (startNode.NodeData.EntryPoint && port.direction == Direction.Output)
                {
                    continue;
                }
                mCompatiblePortList.Add(port);
            }
            return mCompatiblePortList;
        }

        /// <summary>
        /// 获取指定节点类型的基类类型信息
        /// </summary>
        /// <param name="nodeType"></param>
        /// <returns></returns>
        protected Type GetBaseTypeByNodeType(NodeType nodeType)
        {
            Type targetType;
            if (!mNodeTypeBaseTypeMap.TryGetValue(nodeType, out targetType))
            {
                Debug.LogError($"未注册节点类型:{nodeType}的基类类型信息，获取对应基类类型信息失败！");
                return null;
            }
            return targetType;
        }

        /// <summary>
        /// 指定原节点类型和目标节点类型是否可连接
        /// </summary>
        /// <param name="sourceNodeType"></param>
        /// <param name="targetNodeType"></param>
        /// <returns></returns>
        protected bool IsPortAvalibleConnectNodeType(NodeType sourceNodeType, NodeType targetNodeType)
        {
            Dictionary<NodeType, NodeType> avalibleConnectNodeTypeMap;
            if (!mPortAvailableConnectNodeTypeMap.TryGetValue(sourceNodeType, out avalibleConnectNodeTypeMap))
            {
                return false;
            }
            return avalibleConnectNodeTypeMap.ContainsKey(targetNodeType);
        }

        /// <summary>
        /// 指定类型信息是否是指定节点类型的不可连接类型
        /// </summary>
        /// <param name="nodeType"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        protected bool IsPortUnavalibleConnectType(NodeType nodeType, Type type)
        {
            Dictionary<Type, Type> unavalibleConnectTypeMap;
            if (!mPortUnavailableConnectTypeMap.TryGetValue(nodeType, out unavalibleConnectTypeMap))
            {
                return false;
            }
            return unavalibleConnectTypeMap.ContainsKey(type);
        }

        /// <summary>
        /// 获取指定节点类型所有通用菜单操作类型列表
        /// </summary>
        /// <param name="nodeType"></param>
        /// <returns></returns>
        protected List<MenuOperationType> GetNodeCommonMenuOperationTypes(NodeType nodeType)
        {
            List<MenuOperationType> menuOperationTypes;
            if (mNodeCommonOperationTypesMap.TryGetValue(nodeType, out menuOperationTypes))
            {
                return menuOperationTypes;
            }
            return null;
        }

        /// <summary>
        /// 获取指定节点类型的可用菜单类型信息Map
        /// </summary>
        /// <param name="nodeType"></param>
        /// <returns></returns>
        protected Dictionary<NodeType, List<Type>> GetNodeTypeMenuTypesMap(NodeType nodeType)
        {
            Dictionary<NodeType, List<Type>> targetTypesMap;
            if (!mNodeTypeMenuTypesMap.TryGetValue(nodeType, out targetTypesMap))
            {
                return null;
            }
            return targetTypesMap;
        }

        /// <summary>
        /// 获取制定通用菜单操作类型的子菜单名
        /// 子菜单名组成 = 菜单操作类型名
        /// </summary>
        /// <param name="menuOperationType"></param>
        /// <returns></returns>
        protected string GetMenuOperationName(MenuOperationType menuOperationType)
        {
            return $"{menuOperationType.ToString()}";
        }

        /// <summary>
        /// 获取指定节点类型信息的子菜单名
        /// 子菜单名组成 = 节点类型/节点类型名
        /// </summary>
        /// <param name="nodeType">节点类型</param>
        /// <param name="type">节点类型信息</param>
        /// <returns></returns>
        protected string GetNodeTypeSubMenuName(NodeType nodeType, Type type)
        {
            if (type == null || type == BTGraphConstEditor.BaseNodeType
                || !type.IsSubclassOf(BTGraphConstEditor.BaseNodeType))
            {
                return null;
            }
            return $"{nodeType.ToString()}/{type.Name}";
        }

        /// <summary>
        /// 转换Grid组件鼠标局部坐标到GraphView本地坐标
        /// </summary>
        /// <param name="localMousePosition"></param>
        /// <returns></returns>
        protected Vector2 TransformGridLocalMousePosToGraphPos(Vector2 localMousePosition)
        {
            var gridElement = ElementAt(0);
            Vector2 worldMousePosition = contentContainer.ChangeCoordinatesTo(gridElement, localMousePosition);
            Vector2 actualGraphPosition = contentViewContainer.WorldToLocal(worldMousePosition);
            return actualGraphPosition;
        }

        /// <summary>
        /// 转换图ContentContainer组件鼠标局部坐标到GraphView本地坐标
        /// </summary>
        /// <param name="localMousePosition"></param>
        /// <returns></returns>
        protected Vector2 TransformContentLocalMousePosToGraphPos(Vector2 localMousePosition)
        {
            Vector2 worldMousePosition = contentContainer.LocalToWorld(localMousePosition);
            Vector2 actualGraphPosition = contentViewContainer.WorldToLocal(worldMousePosition);
            return actualGraphPosition;
        }

        /// <summary>
        /// 响应菜单栏添加
        /// </summary>
        /// <param name="evt"></param>
        public override void BuildContextualMenu(ContextualMenuPopulateEvent evt)
        {
            //base.BuildContextualMenu(evt);
            BuildNodeContextualMenu(evt);
            BuildGraphContextualMenu(evt);
        }

        /// <summary>
        /// 构建节点菜单栏
        /// </summary>
        /// <param name="evt"></param>
        protected void BuildNodeContextualMenu(ContextualMenuPopulateEvent evt)
        {
            var target = evt.target;
            var targetNodeView = target as NodeView;
            if (targetNodeView == null)
            {
                return;
            }
            var nodeType = targetNodeView.NodeData.NodeType;
            var allCommonMenuOperationTypes = GetNodeCommonMenuOperationTypes(nodeType);
            if (allCommonMenuOperationTypes != null)
            {
                foreach (var commonOperationType in allCommonMenuOperationTypes)
                {
                    var menuOperationName = GetMenuOperationName(commonOperationType);
                    if (string.IsNullOrEmpty(menuOperationName))
                    {
                        continue;
                    }
                    var menuUserData = new MenuUserData(commonOperationType, targetNodeView, commonOperationType);
                    evt.menu.AppendAction(menuOperationName, OnNodeViewCommonMenuOperationNode, OnSubMenuStatus, menuUserData);
                    //Debug.Log($"添加子菜单:{menuOperationName}");
                }
            }

            var allSubMenuTypesMap = GetNodeTypeMenuTypesMap(nodeType);
            if (allSubMenuTypesMap != null)
            {
                foreach (var subMenuTypesMap in allSubMenuTypesMap)
                {
                    var targetNodeType = subMenuTypesMap.Key;
                    foreach (var subMenuType in subMenuTypesMap.Value)
                    {
                        if (subMenuType.IsAbstract)
                        {
                            continue;
                        }
                        var subMenuName = GetNodeTypeSubMenuName(targetNodeType, subMenuType);
                        if (string.IsNullOrEmpty(subMenuName))
                        {
                            continue;
                        }
                        var menuUserData = new MenuUserData(MenuOperationType.CreateNode, targetNodeView, subMenuType);
                        evt.menu.AppendAction(subMenuName, OnNodeViewSubMenuCreateNode, OnSubMenuStatus, menuUserData);
                        //Debug.Log($"添加子菜单:{subMenuName}");
                    }
                }
            }
        }

        /// <summary>
        /// 响应NodeView节点通用菜单操作
        /// </summary>
        /// <param name="menuAction"></param>
        protected void OnNodeViewCommonMenuOperationNode(DropdownMenuAction menuAction)
        {
            var menuUserData = menuAction.userData as MenuUserData;
            var targetNodeView = menuUserData.OperationElement as NodeView;
            var menuOperationType = menuUserData.OperationType;
            if (menuOperationType == MenuOperationType.DeleteNodeOnly)
            {
                RemoveNodeView(targetNodeView, false);
            }
            if (menuOperationType == MenuOperationType.DeleteNodeRecusive)
            {
                RemoveNodeView(targetNodeView, true);
            }
            else if (menuOperationType == MenuOperationType.DuplicatedNode)
            {
                Debug.Log($"复制粘贴节点功能待实现！");
            }
        }

        /// <summary>
        /// 响应NodeView子菜单栏创建节点
        /// </summary>
        /// <param name="menuAction"></param>
        protected void OnNodeViewSubMenuCreateNode(DropdownMenuAction menuAction)
        {
            var menuUserData = menuAction.userData as MenuUserData;
            var nodeType = menuUserData.CustomData as Type;
            Vector2 actualGraphPosition = TransformGridLocalMousePosToGraphPos(menuAction.eventInfo.localMousePosition);
            var nodeView = CreateNodeByType(nodeType, actualGraphPosition);
            var nodeViewPosition = nodeView.GetPosition();
            nodeViewPosition.position = actualGraphPosition;
            UpdateNodeViewRect(nodeView, nodeViewPosition);

            //Debug.Log($"CommonGraphView:OnNodeViewSubMenuCreateNode() NodeType:{menuUserData.SourceNodeView.NodeData.NodeType}");
            //Debug.Log($"localMousePosition:{menuAction.eventInfo.localMousePosition.ToString()}");
            //Debug.Log($"mousePosition:{menuAction.eventInfo.mousePosition.ToString()}");
            //Debug.Log($"actualGraphPosition:{actualGraphPosition.ToString()}");
            // TODO: 创建节点Port链接
        }

        /// <summary>
        /// 响应节点子菜单栏Item状态
        /// </summary>
        /// <param name="menuAction"></param>
        /// <returns></returns>
        protected DropdownMenuAction.Status OnSubMenuStatus(DropdownMenuAction menuAction)
        {
            MenuUserData menuUserData = menuAction.userData as MenuUserData;
            NodeView nodeView = menuUserData.OperationElement as NodeView;
            if (nodeView != null)
            {
                if (nodeView.NodeData.EntryPoint)
                {
                    bool isDeleteNodeOnlyType = menuUserData.OperationType == MenuOperationType.DeleteNodeOnly;
                    bool isDeleteNodeRecusiveType = menuUserData.OperationType == MenuOperationType.DeleteNodeRecusive;
                    bool isDuplicatedOperationType = menuUserData.OperationType == MenuOperationType.DuplicatedNode;
                    if (isDeleteNodeOnlyType || isDeleteNodeRecusiveType || isDuplicatedOperationType)
                    {
                        return DropdownMenuAction.Status.Disabled;
                    }
                }
                return DropdownMenuAction.Status.Normal;
            }
            return DropdownMenuAction.Status.Disabled;
        }

        /// <summary>
        /// 构建GraphView菜单栏
        /// </summary>
        /// <param name="evt"></param>
        protected void BuildGraphContextualMenu(ContextualMenuPopulateEvent evt)
        {
            var target = evt.target;
            var targetGraphView = target as GraphView;
            if (targetGraphView == null)
            {
                return;
            }
            foreach (var graphViewTypesMap in mGraphViewMenuTypesMap)
            {
                var nodeType = graphViewTypesMap.Key;
                foreach (var graphViewType in graphViewTypesMap.Value)
                {
                    if (graphViewType.IsAbstract)
                    {
                        continue;
                    }
                    var subMenuName = GetNodeTypeSubMenuName(nodeType, graphViewType);
                    if (string.IsNullOrEmpty(subMenuName))
                    {
                        continue;
                    }
                    var menuUserData = new MenuUserData(MenuOperationType.CreateNode, null, graphViewType);
                    evt.menu.AppendAction(subMenuName, OnGraphViewSubMenuCreateNode, OnGraphSubMenuStatus, menuUserData);
                    //Debug.Log($"添加子菜单:{subMenuName}");
                }
            }
        }

        /// <summary>
        /// 响应GraphView子菜单创建节点
        /// </summary>
        /// <param name="menuAction"></param>
        protected void OnGraphViewSubMenuCreateNode(DropdownMenuAction menuAction)
        {
            var menuUserData = menuAction.userData as MenuUserData;
            var nodeType = menuUserData.CustomData as Type;
            Vector2 actualGraphPosition = TransformGridLocalMousePosToGraphPos(menuAction.eventInfo.localMousePosition);
            var nodeView = CreateNodeByType(nodeType, actualGraphPosition);
            var nodeViewPosition = nodeView.GetPosition();
            nodeViewPosition.position = actualGraphPosition;
            UpdateNodeViewRect(nodeView, nodeViewPosition);

            //Debug.Log($"CommonGraphView:OnGraphViewSubMenuCreateNode()");
            //Debug.Log($"localMousePosition:{menuAction.eventInfo.localMousePosition.ToString()}");
            //Debug.Log($"mousePosition:{menuAction.eventInfo.mousePosition.ToString()}");
            //Debug.Log($"actualGraphPosition:{actualGraphPosition.ToString()}");
        }

        /// <summary>
        /// 响应图子菜单栏Item状态
        /// </summary>
        /// <param name="menuAction"></param>
        /// <returns></returns>
        protected DropdownMenuAction.Status OnGraphSubMenuStatus(DropdownMenuAction menuAction)
        {
            return DropdownMenuAction.Status.Normal;
        }

        /// <summary>
        /// 移除指定节点View
        /// </summary>
        /// <param name="nodeView"></param>
        /// <param name="recusive"></param>
        /// <returns></returns>
        protected bool RemoveNodeView(NodeView nodeView, bool recusive = false)
        {
            if (nodeView == null)
            {
                Debug.LogError($"不允许移除空NodeView，删除节点View失败！");
                return false;
            }
            if (!Contains(nodeView))
            {
                Debug.LogError($"未包含NodeView GUID:{nodeView.NodeData.GUID}的节点View，删除节点View失败！");
                return false;
            }
            if (!recusive)
            {
                RemoveNodeViewWithAllEdge(nodeView);
                return true;
            }

            var allChildNodeViews = GetAllChildNodeViews(nodeView);
            if (allChildNodeViews != null)
            {
                foreach (var childNodeView in allChildNodeViews)
                {
                    RemoveNodeViewWithAllEdge(childNodeView);
                }
            }
            RemoveNodeViewWithAllEdge(nodeView);
            return true;
        }

        /// <summary>
        /// 移除指定节点View和其所有边
        /// </summary>
        /// <param name="nodeView"></param>
        /// <returns></returns>
        protected bool RemoveNodeViewWithAllEdge(NodeView nodeView)
        {
            if (nodeView == null)
            {
                Debug.LogError($"不允许移除空NodeView，删除节点View和其所有边失败！");
                return false;
            }

            mDeleteElementTempList.Clear();
            var allInputPorts = nodeView.inputContainer.Query<Port>().ToList();
            foreach (var inputPort in allInputPorts)
            {
                mDeleteElementTempList.AddRange(inputPort.connections);
            }
            var allOutputPorts = nodeView.outputContainer.Query<Port>().ToList();
            foreach (var outputPort in allOutputPorts)
            {
                mDeleteElementTempList.AddRange(outputPort.connections);
            }
            mDeleteElementTempList.Add(nodeView);
            DeleteElements(mDeleteElementTempList);
            return true;
        }

        /// <summary>
        /// 相应节点选择
        /// </summary>
        /// <param name="selectedNode"></param>
        protected void OnNodeSelected(NodeView selectedNode)
        {
            Debug.Log($"CommonGraphView:OnNodeSelected()");
            UpdateSelectedNode(selectedNode);
        }

        /// <summary>
        /// 更新选择节点
        /// </summary>
        /// <param name="selectedNode"></param>
        protected void UpdateSelectedNode(NodeView selectedNode)
        {
            SelectedNode = selectedNode;
            if (mSelectedNodeChangeDelegate != null)
            {
                mSelectedNodeChangeDelegate(SelectedNode);
            }
        }

        /// <summary>
        /// 响应GraphView变化
        /// </summary>
        /// <param name="graphViewChange"></param>
        /// <returns></returns>
        protected GraphViewChange OnGraphViewChanged(GraphViewChange graphViewChange)
        {
            Debug.Log($"CommonGraphVIew:OnGraphViewChanged()");
            if (graphViewChange.elementsToRemove != null)
            {
                foreach (var elementToRemove in graphViewChange.elementsToRemove)
                {
                    if (elementToRemove is NodeView removeNodeView)
                    {
                        Debug.Log($"删除节点GUID:{removeNodeView.NodeData.GUID}，节点数据类型:{removeNodeView.NodeData.GetType().Name}");
                        RemoveNodeData(removeNodeView.NodeData);
                    }
                    else if (elementToRemove is Edge removeEdge)
                    {
                        var inputNodeView = removeEdge.input.node as NodeView;
                        var outputNodeView = removeEdge.output.node as NodeView;
                        var inputNodeViewName = inputNodeView != null ? inputNodeView.NodeData.GetType().Name : string.Empty;
                        var outputNodeViewName = outputNodeView != null ? outputNodeView.NodeData.GetType().Name : string.Empty;
                        Debug.Log($"删除边，Input节点名:{inputNodeViewName}，Input端口名:{removeEdge.input.portName}，Output节点名:{outputNodeViewName}，Output端口名:{removeEdge.output.portName}");
                        RemoveEdgeData(outputNodeView.NodeData.GUID, removeEdge.output.portName, inputNodeView.NodeData.GUID, removeEdge.input.portName);
                        OnEdgeViewUpdate(removeEdge);
                    }
                    else
                    {
                        Debug.Log($"TODO:删除其他Element类型:{elementToRemove.GetType().Name}！");
                    }
                }
            }
            if (graphViewChange.edgesToCreate != null)
            {
                foreach (var edgeToCreate in graphViewChange.edgesToCreate)
                {
                    var edgeView = edgeToCreate as EdgeView;
                    var inputNodeView = edgeToCreate.input.node as NodeView;
                    var outputNodeView = edgeToCreate.output.node as NodeView;
                    var inputNodeViewName = inputNodeView != null ? inputNodeView.NodeData.GetType().Name : string.Empty;
                    var outputNodeViewName = outputNodeView != null ? outputNodeView.NodeData.GetType().Name : string.Empty;
                    var ouputNodeGUID = outputNodeView.NodeData.GUID;
                    var outputPortName = edgeToCreate.output.portName;
                    var outputPortTypeFullName = edgeToCreate.output.portType.FullName;
                    var inputNodeGUID = inputNodeView.NodeData.GUID;
                    var intputPortName = edgeToCreate.input.portName;
                    var intputPortTypeFullName = edgeToCreate.input.portType.FullName;
                    var guid = Guid.NewGuid().ToString();
                    EdgeData edgeData = new EdgeData(guid, ouputNodeGUID, outputPortName, outputPortTypeFullName, inputNodeGUID, intputPortName, intputPortTypeFullName);
                    Debug.Log($"创建边，Input节点名:{inputNodeViewName}，Input端口名:{edgeToCreate.input.portName}，Output节点名:{outputNodeViewName}，Output端口名:{edgeToCreate.output.portName}");
                    AddEdgeData(edgeData);
                    OnEdgeViewUpdate(edgeView);
                    // 新增边不知道为什么在OnGraphViewChanged里触发时还未添加到connections里
                    // 这里采取手动更新新增显示边的索引显示数据
                    UpdateEdgeViewIndex(edgeView);
                }
            }
            if (graphViewChange.movedElements != null)
            {
                foreach (var moveElement in graphViewChange.movedElements)
                {
                    if (moveElement is NodeView moveNodeView)
                    {
                        Debug.Log($"更新节点GUID:{moveNodeView.NodeData.GUID}，节点数据类型:{moveNodeView.NodeData.GetType().Name}的位置X:{moveNodeView.NodeData.Position.x} Y:{moveNodeView.NodeData.Position.y}");
                        UpdateNodeRectByNodeView(moveNodeView);
                        OnNodeViewMove(moveNodeView);
                    }
                    else
                    {
                        Debug.Log($"TODO:移动Element类型:{moveElement.GetType().Name}！");
                    }
                }
            }
            return graphViewChange;
        }

        /// <summary>
        /// 响应Undo Redo操作触发
        /// </summary>
        private void OnUndoRedoPerformed()
        {
            Debug.Log($"响应Undo Redo操作触发！");
            LoadGraphData(SourceGraphData);
            AssetDatabase.SaveAssets();
        }

        /// <summary>
        /// 响应显示节点移动
        /// </summary>
        /// <param name="nodeView"></param>
        protected void OnNodeViewMove(NodeView nodeView)
        {
            // 更新移动节点作为输出节点连接的节点对应输出端口显示边索引数据显示
            UpdateConnectOutputNodePortEdgeViewIndex(nodeView);
        }

        /// <summary>
        /// 响应拖拽结束事件
        /// </summary>
        /// <param name="dragExitedEvent"></param>
        protected void OnDragExistedEvent(DragExitedEvent dragExitedEvent)
        {
            Debug.Log($"响应拖拽结束！");
            // dragExistEvent.localMousePosition的坐标系貌似就是ContentViewContainer，所以不需要转换
            Debug.Log($"拖拽结束位置:{dragExitedEvent.localMousePosition}");
            Debug.Log($"当前拖拽物体数量:{DragAndDrop.objectReferences.Length}");
            foreach (var objectReference in DragAndDrop.objectReferences)
            {
                Debug.Log($"拖拽物体名:{objectReference.name}");
                var graphData = objectReference as BehaviourTreeGraphData;
                if (graphData != null && AssetDatabase.Contains(graphData))
                {
                    var graphDataAssetPath = AssetDatabase.GetAssetPath(graphData);
                    Debug.Log($"拖拽的图数据Asset路径:{graphDataAssetPath}");
                    var cloneGraphData = graphData.CloneSelf();
                    // 这里Drag响应的目标组件貌似是Graph的ContentContainer,
                    // 所以要采用ContentContainer作为基础坐标系转换
                    Vector2 actualGraphPosition = TransformContentLocalMousePosToGraphPos(dragExitedEvent.localMousePosition);
                    AddGraphData(cloneGraphData, actualGraphPosition);
                }
            }
        }

        /// <summary>
        /// 更新NodeView的Rect信息(同时自动更新Node节点数据的Rect信息)
        /// </summary>
        /// <param name="nodeView"></param>
        /// <param name="position"></param>
        protected bool UpdateNodeViewRect(NodeView nodeView, Rect position)
        {
            if (nodeView == null)
            {
                Debug.LogError($"不允许更新空NodeView的位置数据！");
                return false;
            }
            nodeView.SetPosition(position);
            return true;
        }

        /// <summary>
        /// 添加新节点数据
        /// </summary>
        /// <param name="node"></param>
        /// <param name="enableUndoSystem"></param>
        protected bool AddNodeData(BaseNode node, bool enableUndoSystem = true)
        {
            if (node == null)
            {
                Debug.LogError($"不允许添加空节点数据！");
                return false;
            }
            if (enableUndoSystem)
            {
                Undo.RecordObject(SourceGraphData, "AddNodeData");
            }
            var result = SourceGraphData.AddNode(node);
            if (result)
            {
                OnAddNodeData(node);
                EditorUtility.SetDirty(SourceGraphData);
            }
            return true;
        }

        /// <summary>
        /// 移除指定节点数据
        /// </summary>
        /// <param name="node"></param>
        /// <param name="enableUndoSystem"></param>
        /// <returns></returns>
        protected bool RemoveNodeData(BaseNode node, bool enableUndoSystem = true)
        {
            if (node == null)
            {
                Debug.LogError($"不允许移除空节点数据！");
                return false;
            }
            if (enableUndoSystem)
            {
                Undo.RecordObject(SourceGraphData, "RemoveNodeData");
            }
            var result = SourceGraphData.RemoveNode(node);
            if (result)
            {
                OnRemoveNode(node);
                EditorUtility.SetDirty(SourceGraphData);
            }
            return result;
        }

        /// <summary>
        /// 添加边数据列表
        /// </summary>
        /// <param name="edgeDataList"></param>
        /// <returns></returns>
        protected bool AddEdgeDatas(List<EdgeData> edgeDataList)
        {
            if (edgeDataList == null)
            {
                Debug.LogError($"不允许添加空边数据列表！");
                return false;
            }
            foreach (var edgeData in edgeDataList)
            {
                AddEdgeData(edgeData);
            }
            return true;
        }

        /// <summary>
        /// 添加边数据
        /// </summary>
        /// <param name="edgeData"></param>
        /// <returns></returns>
        protected bool AddEdgeData(EdgeData edgeData, bool enableUndoSystem = true)
        {
            if (edgeData == null)
            {
                Debug.LogError($"不允许添加空边数据！");
                return false;
            }
            if (enableUndoSystem)
            {
                Undo.RecordObject(SourceGraphData, "AddEdgeData");
            }
            var result = SourceGraphData.AddEdge(edgeData);
            if (result)
            {
                OnAddEdgeData(edgeData);
                EditorUtility.SetDirty(SourceGraphData);
            }
            return true;
        }

        /// <summary>
        /// 移除指定边数据
        /// </summary>
        /// <param name="outputNodeGuid"></param>
        /// <param name="outputPortName"></param>
        /// <param name="inputNodeGuid"></param>
        /// <param name="inputPortName"></param>
        /// <returns></returns>
        protected bool RemoveEdgeData(string outputNodeGuid, string outputPortName, string inputNodeGuid, string inputPortName)
        {
            if (string.IsNullOrEmpty(outputNodeGuid) || string.IsNullOrEmpty(outputPortName))
            {
                Debug.LogError($"不允许移除空Output节点GUID或空Output端口名的端口数据！");
                return false;
            }
            EdgeData removeEdgeData;
            Undo.RecordObject(SourceGraphData, "RemoveEdgeData");
            var result = SourceGraphData.RemoveEdgeData(outputNodeGuid, outputPortName, inputNodeGuid, inputPortName, out removeEdgeData);
            if (result)
            {
                OnRemoveEdgeData(removeEdgeData);
                EditorUtility.SetDirty(SourceGraphData);
            }
            return result;
        }

        /// <summary>
        /// 获取指定显示节点所有输入端口相连的节点和输出端口名列表Map<节点GUID, 输出端口名列表>
        /// </summary>
        /// <param name="nodeView"></param>
        /// <param name="nodeOutputPortMap"></param>
        protected void GetNodeViewInputPortConnectMap(NodeView nodeView, ref Dictionary<string, List<string>> nodeOutputPortMap)
        {
            if (nodeView == null)
            {
                Debug.LogError($"不允许获取空显示节点的所有输入端口相连的节点和输出端口名Map，获取失败！");
                return;
            }
            var allInputPorts = nodeView.GetAllInputPorts();
            foreach (var inputPort in allInputPorts)
            {
                foreach (var edge in inputPort.connections)
                {
                    var outputPortNode = edge.output.node as NodeView;
                    var outputPortNodeGUID = outputPortNode.NodeData.GUID;
                    var outputPortName = edge.output.name;
                    List<string> nodeOutputPortList;
                    if (!nodeOutputPortMap.TryGetValue(outputPortNodeGUID, out nodeOutputPortList))
                    {
                        nodeOutputPortList = new List<string>();
                        nodeOutputPortMap.Add(outputPortNodeGUID, nodeOutputPortList);
                    }
                    if (!nodeOutputPortList.Contains(outputPortName))
                    {
                        nodeOutputPortList.Add(outputPortName);
                    }
                }
            }

        }

        /// <summary>
        /// 更新指定GUID显示节点对应数据的Rect信息
        /// </summary>
        /// <param name="nodeView"></param>
        /// <param name="nodeGuid"></param>
        /// <param name="nodePosition"></param>
        /// <returns></returns>
        protected bool UpdateNodeRectByNodeView(NodeView nodeView)
        {
            if (nodeView == null)
            {
                Debug.LogError($"不允许传递空显示节点，更新显示节点对应节点Rect数据失败！");
                return false;
            }
            string nodeGuid = nodeView.NodeData.GUID;
            var node = SourceGraphData.GetNodeByGUID(nodeGuid);
            if (node == null)
            {
                Debug.LogError($"找不到GUID:{nodeGuid}的节点数据，更新节点Rect信息失败！");
                return false;
            }
            Undo.RecordObject(node, "UpdateNodePosition");
            Rect nodePosition = nodeView.GetPosition();
            node.Position = nodePosition;
            EditorUtility.SetDirty(node);
            UpdateConnectOutputNodePortEdgeIndex(nodeView);
            return true;
        }

        /// <summary>
        /// 更新指定显示节点相连的输出端口边数据的边索引
        /// </summary>
        /// <param name="nodeView"></param>
        protected void UpdateConnectOutputNodePortEdgeIndex(NodeView nodeView)
        {
            if (nodeView == null)
            {
                return;
            }
            //Undo.RecordObject(SourceGraphData, "UpdateConnectOutputNodePortEdgeIndex");
            mTempNodeOutputPortUpdateMap.Clear();
            GetNodeViewInputPortConnectMap(nodeView, ref mTempNodeOutputPortUpdateMap);
            foreach (var nodeOutputPutDatas in mTempNodeOutputPortUpdateMap)
            {
                var nodeGUID = nodeOutputPutDatas.Key;
                var outputPortList = nodeOutputPutDatas.Value;
                foreach (var outputPort in outputPortList)
                {
                    SourceGraphData.UpdateNodeOutputPortEdgeIndex(nodeGUID, outputPort);
                }
            }
            EditorUtility.SetDirty(SourceGraphData);
        }

        /// <summary>
        /// 更新指定显示节点相连的输出端口显示边数据的边索引
        /// </summary>
        /// <param name="nodeView"></param>
        protected void UpdateConnectOutputNodePortEdgeViewIndex(NodeView nodeView)
        {
            if (nodeView == null)
            {
                return;
            }
            var allInputPorts = nodeView.GetAllInputPorts();
            foreach (var inputPort in allInputPorts)
            {
                foreach (var edge in inputPort.connections)
                {
                    var edgeView = edge as EdgeView;
                    UpdateNodeOutputPortEdgeViewIndex(edgeView.output);
                }
            }
        }

        /// <summary>
        /// 响应添加新节点数据
        /// </summary>
        /// <param name="node"></param>
        protected void OnAddNodeData(BaseNode node)
        {
            if (IsSourceGraphDataAsset())
            {
                Debug.Log($"图:{SourceGraphData.name}添加节点GUID:{node.GUID}，节点名:{node.name}的子Asset！");
                // Undo.RegisterCreatedObjectUndo结合AssetDAtabase.AddObjectToAsset使用时
                // Undo后Redo会导致逻辑Node对象类型信息丢失且和Asset失去关联
                // 所以这里Asset创建不采用Undo系统，只在保存的时候统一处理因为Undo系统未撤销的Node Asset
                // 如果是本地Asset，新创建的节点数据要实时添加到GraphData
                // 避免异常情况无法正常保存导致节点数据引用丢失
                AssetDatabase.AddObjectToAsset(node, SourceGraphData);
                //Undo.RegisterCreatedObjectUndo(node, "NodeAddObjectToAsset");
                //AssetDatabase.SaveAssets();
            }
        }

        /// <summary>
        /// 响应移除节点数据
        /// </summary>
        /// <param name="node"></param>
        protected void OnRemoveNode(BaseNode node)
        {
            if (IsSourceGraphDataAsset())
            {
                // 如果是本地Asset，新创建的节点数据要实时删除到GraphData
                // 避免异常情况无法正常保存导致节点数据引用丢失
                //AssetDatabase.RemoveObjectFromAsset(node);
                Undo.DestroyObjectImmediate(node);
                AssetDatabase.SaveAssets();
            }
        }

        /// <summary>
        /// 响应添加新边数据
        /// </summary>
        /// <param name="portData"></param>
        protected void OnAddEdgeData(EdgeData portData)
        {

        }

        /// <summary>
        /// 响应移除边数据
        /// </summary>
        /// <param name="portData"></param>
        protected void OnRemoveEdgeData(EdgeData portData)
        {

        }

        /// <summary>
        /// 响应更新节点位置数据
        /// </summary>
        /// <param name="node"></param>
        /// <param name="position"></param>
        protected void OnUpdateNodePosition(BaseNode node, Rect position)
        {

        }

        /// <summary>
        /// 清理选中节点数据
        /// </summary>
        protected void ClearSelectedNodeData()
        {
            UpdateSelectedNode(null);
        }

        /// <summary>
        /// 删除所有Element
        /// </summary>
        protected void DeleteAllElements()
        {
            DeleteElements(graphElements);
        }

        /// <summary>
        /// 创建指定图数据的所有节点
        /// </summary>
        /// <param name="graphData"></param>
        /// <param name="addNodeData">是否添加节点数据</param>
        /// <param name="enableUndoSystem">是否开启Undo系统</param>
        protected void CreateGraphAllNodes(BehaviourTreeGraphData graphData, bool addNodeData = true, bool enableUndoSystem = true)
        {
            if (graphData == null || graphData.AllNodeList == null)
            {
                return;
            }
            foreach (var node in graphData.AllNodeList)
            {
                CreateNodeByNodeData(graphData, node, addNodeData, enableUndoSystem);
            }
        }

        /// <summary>
        /// 创建指定图数据的所有边
        /// </summary>
        /// <param name="graphData"></param>
        /// <param name="addEdgeData">是否添加边数据</param>
        /// <param name="enableUndoSystem">是否开启Undo系统</param>
        protected void CreateGraphAllEdges(BehaviourTreeGraphData graphData, bool addEdgeData = true, bool enableUndoSystem = true)
        {
            if (graphData == null || graphData.AllEdgeDataList == null)
            {
                return;
            }
            foreach (var edgeData in graphData.AllEdgeDataList)
            {
                if (addEdgeData)
                {
                    AddEdgeData(edgeData, enableUndoSystem);
                }
                // 为了支持动态边的创建和加载还原
                // 如果不存在的端口和边数据则动态创建并连接
                var outputNode = GetNodeByGuid(edgeData.OutputNodeGUID) as NodeView;
                var outputPort = GetOrCreateOutputPort(outputNode, edgeData.OutputPortName, edgeData.OutputPortTypeFullName);
                var inputNode = GetNodeByGuid(edgeData.InputNodeGUID) as NodeView;
                var inputPort = GetOrCreateInputPort(inputNode, edgeData.InputPortName, edgeData.InputPortTypeFullName);
                AddEdgeView(edgeData, inputPort, outputPort);
                Debug.Log($"创建输出节点GUID:{edgeData.OutputNodeGUID}的端口名:{edgeData.OutputPortName}和输入节点GUID:{edgeData.InputNodeGUID}的端口名:{edgeData.InputPortName}边！");
            }
        }

        /// <summary>
        /// 添加显示边
        /// </summary>
        /// <param name="edgeData"></param>
        /// <param name="inputPort"></param>
        /// <param name="outputPort"></param>
        /// <returns></returns>
        protected Edge AddEdgeView(EdgeData edgeData, Port inputPort, Port outputPort)
        {
            if (edgeData == null || inputPort == null || outputPort == null)
            {
                Debug.LogError($"不允许创建空边数据或空输入端口或空输出端口的显示边，创建显示边失败！");
                return null;
            }
            var edgeView = CreateEdgeView(edgeData, inputPort, outputPort);
            outputPort.Connect(edgeView);
            inputPort.Connect(edgeView);
            Add(edgeView);
            OnEdgeViewUpdate(edgeView);
            return edgeView;
        }

        /// <summary>
        /// 创建显示边
        /// </summary>
        /// <param name="edgeData"></param>
        /// <param name="inputPort"></param>
        /// <param name="outputPort"></param>
        /// <returns></returns>
        protected EdgeView CreateEdgeView(EdgeData edgeData, Port inputPort, Port outputPort)
        {
            var edgeView = new EdgeView()
            {
                input = inputPort,
                output = outputPort,
            };
            edgeView.Init(SourceGraphData, edgeData);
            edgeView.UpdateEdgeControlStateColor();
            return edgeView;
        }

        /// <summary>
        /// 获取指定显示边的边数据
        /// </summary>
        /// <param name="edgeView"></param>
        /// <returns></returns>
        protected EdgeData GetEdgeDataByEdgeView(EdgeView edgeView)
        {
            if (edgeView == null)
            {
                Debug.LogError($"无法获取空显示边的边数据，获取显示边的边数据失败！");
                return null;
            }
            var outputPort = edgeView.output;
            var outputNodeView = outputPort.node as NodeView;
            var inputPort = edgeView.input;
            var inputNodeView = inputPort.node as NodeView;
            return SourceGraphData.GetEdgeData(outputNodeView.NodeData.GUID, outputPort.portName,
                                               inputNodeView.NodeData.GUID, inputPort.portName);
        }

        /// <summary>
        /// 响应显示边更新
        /// </summary>
        /// <param name="edge"></param>
        protected void OnEdgeViewUpdate(Edge edge)
        {
            if (edge == null)
            {
                return;
            }
            UpdateNodeOutputPortEdgeViewIndex(edge.output);
        }

        /// <summary>
        /// 更新指定显示节点所有输出端口显示边索引
        /// </summary>
        /// <param name="nodeView"></param>
        /// <returns></returns>
        protected bool UpdateNodeAllOutputPortEdgeViewIndex(NodeView nodeView)
        {
            if (nodeView == null)
            {
                Debug.LogError($"不允许更新空显示节点的所所有输出端口显示边索引，更新显示边索引失败！");
                return false;
            }
            var result = true;
            var allOutputPorts = nodeView.GetAllOutputPorts();
            foreach (var outputPort in allOutputPorts)
            {
                if (!UpdateNodeOutputPortEdgeViewIndex(outputPort))
                {
                    result = false;
                }
            }
            return result;
        }

        /// <summary>
        /// 更新指定输出端口的所有显示边索引
        /// </summary>
        /// <param name="port"></param>
        /// <returns></returns>
        protected bool UpdateNodeOutputPortEdgeViewIndex(Port port)
        {
            if (port == null || port.direction == Direction.Input)
            {
                Debug.LogError($"不允许更新空Port或输入Port的显示边索引，更新节点输出Port显示边索引失败！");
                return false;
            }
            // 新增边不知道为什么在OnGraphViewCHanged里触发时还未添加到connections里
            // 新增边采取手动更新新增显示边的索引显示数据不在这里
            foreach (var edge in port.connections)
            {
                var edgeView = edge as EdgeView;
                UpdateEdgeViewIndex(edgeView);
            }
            return true;
        }

        /// <summary>
        /// 更新指定显示边的边索引显示
        /// </summary>
        /// <param name="edgeView"></param>
        protected void UpdateEdgeViewIndex(EdgeView edgeView)
        {
            var edgeData = GetEdgeDataByEdgeView(edgeView);
            // 删除的边不知道为啥这里遍历connections还存在
            // 有可能出现边数据已经删除了找不到的情况
            if (edgeData != null)
            {
                edgeView.UpdateEdgeIndex(edgeData.OutputPortEdgeIndex);
            }
        }

        /// <summary>
        /// 指定节点View获取或创建指定名字和类型的输入端口
        /// </summary>
        /// <param name="nodeView"></param>
        /// <param name="portName"></param>
        /// <param name="portTypeFullName"></param>
        /// <returns></returns>
        protected Port GetOrCreateInputPort(NodeView nodeView, string portName, string portTypeFullName)
        {
            if (nodeView == null)
            {
                Debug.LogError($"不允许给空节点View创建输入端口，创建端口名:{portName}类型:{portTypeFullName}的输入节点端口失败！");
                return null;
            }
            var port = nodeView.inputContainer.Q<Port>(portName);
            if (port == null)
            {
                var portType = TypeCache.GetType(portTypeFullName);
                if (portType == null)
                {
                    Debug.LogError($"找不到类型全名:{portTypeFullName}的类型信息，创建端口名:{portName}的输入节点端口失败！");
                    return null;
                }
                port = nodeView.InstantiateCustomInputPort(portName, portType);
            }
            return port;
        }

        /// <summary>
        /// 指定节点View获取或创建指定名字和类型的输出端口
        /// </summary>
        /// <param name="nodeView"></param>
        /// <param name="portName"></param>
        /// <param name="portTypeFullName"></param>
        /// <returns></returns>
        protected Port GetOrCreateOutputPort(NodeView nodeView, string portName, string portTypeFullName)
        {
            if (nodeView == null)
            {
                Debug.LogError($"不允许给空节点View创建输出端口，创建端口名:{portName}类型:{portTypeFullName}的输出节点端口失败！");
                return null;
            }
            var port = nodeView.outputContainer.Q<Port>(portName);
            if (port == null)
            {
                var portType = TypeCache.GetType(portTypeFullName);
                if (portType == null)
                {
                    Debug.LogError($"找不到类型全名:{portTypeFullName}的类型信息，创建端口名:{portName}的输出节点端口失败！");
                    return null;
                }
                port = nodeView.InstantiateCustomOutputPort(portName, portType);
            }
            return port;
        }

        /// <summary>
        /// 是否本地Asset
        /// </summary>
        /// <returns></returns>
        protected bool IsSourceGraphDataAsset()
        {
            return AssetDatabase.Contains(SourceGraphData);
        }
    }
}