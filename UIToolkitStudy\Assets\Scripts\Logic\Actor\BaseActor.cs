﻿/*
 * Description:     ActorManager.cs
 * Author:          TonyTang
 * Create Date:     2023/12/06
 */

using System;
using TCommonGraph;
using UnityEngine;
using UnityEngine.AI;

/// <summary>
/// BaseActor.cs
/// 角色基类
/// </summary>
public abstract class BaseActor
{
    /// <summary>
    /// 角色UID
    /// </summary>
    public int UID
    {
        get;
        private set;
    }

    /// <summary>
    /// 资源路径
    /// </summary>
    public string ResPath
    {
        get;
        private set;
    }

    /// <summary>
    /// 角色类型
    /// </summary>
    public virtual ActorType ActorType
    {
        get
        {
            return ActorType.NONE;
        }
    }

    /// <summary>
    /// 初始化位置
    /// </summary>
    private Vector3 mInitPosition;

    /// <summary>
    /// 实体对象
    /// </summary>
    public GameObject GO
    {
        get;
        private set;
    }

    /// <summary>
    /// 实体对象Transform
    /// </summary>
    public Transform Trans
    {
        get
        {
            return GO != null ? GO.transform : null;
        }
    }

    /// <summary>
    /// 生命值
    /// </summary>
    public float Health
    {
        get;
        private set;
    }

    /// <summary>
    /// 寻路组件
    /// </summary>
    protected NavMeshAgent mNavMeshAgent;

    /// <summary>
    /// 行为树对象
    /// </summary>
    protected TBehaviourTree mBehaviourTree;

    public BaseActor()
    {

    }

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="uid"></param>
    /// <param name="resPath"></param>
    /// <param name="position"></param>
    public void Init(int uid, string resPath, Vector3 position)
    {
        UID = uid;
        ResPath = resPath;
        mInitPosition = position;
        Health = 100f;
        LoadRes();
    }

    /// <summary>
    /// 销毁自身(统一由ActorManager管理调用，请勿主动调用)
    /// </summary>
    public void Destroy()
    {
        GameObject.Destroy(GO);
        ResetDatas();
    }

    /// <summary>
    /// 重置数据
    /// </summary>
    protected virtual void ResetDatas()
    {
        UID = 0;
        ResPath = null;
        mInitPosition = Vector3.zero;
        GO = null;
        Health = 0f;
        mNavMeshAgent = null;
        mBehaviourTree = null;
    }

    /// <summary>
    /// 加载资源
    /// </summary>
    protected void LoadRes()
    {
        var goAsset = Resources.Load<GameObject>(ResPath);
        GO = GameObject.Instantiate(goAsset);
        OnLoadResComplete();
    }

    /// <summary>
    /// 响应资源加载完成
    /// </summary>
    protected virtual void OnLoadResComplete()
    {
        GO.transform.position = mInitPosition;
        mNavMeshAgent = GO.GetComponent<NavMeshAgent>();
    }

    /// <summary>
    /// 获取当前对象位置
    /// </summary>
    /// <returns></returns>
    public Vector3 GetPosition()
    {
        return Trans == null ? mInitPosition : Trans.position;
    }

    /// <summary>
    /// 移动到目标位置
    /// </summary>
    /// <param name="targetPosition"></param>
    /// <returns></returns>
    public bool GoToPosition(Vector3 targetPosition)
    {
        if (mNavMeshAgent == null)
        {
            return false;
        }
        mNavMeshAgent.SetDestination(targetPosition);
        return true;
    }

    /// <summary>
    /// 停止移动
    /// </summary>
    /// <returns></returns>
    public bool StopMove()
    {
        if(mNavMeshAgent == null)
        {
            return false;
        }
        mNavMeshAgent.ResetPath();
        return true;
    }

    /// <summary>
    /// 增加生命值
    /// </summary>
    /// <param name="value"></param>
    public void AddHealth(float value)
    {
        Health += value;
        Health = Mathf.Max(Health, 0f);
    }

    /// <summary>
    /// 减少生命值
    /// </summary>
    /// <param name="value"></param>
    public void DecreaseHealth(float value)
    {
        Health -= value;
        Health = Mathf.Max(Health, 0f);
    }

    /// <summary>
    /// 加载指定路径AI
    /// </summary>
    /// <param name="aiPath"></param>
    public void LoadAI(string aiPath)
    {
        if(mBehaviourTree == null)
        {
            mBehaviourTree = new TBehaviourTree();
            mBehaviourTree.Init(UID);
            mBehaviourTree.CreateBTDebugger();
        }
        mBehaviourTree.LoadBTGraphData(aiPath);
    }
}
