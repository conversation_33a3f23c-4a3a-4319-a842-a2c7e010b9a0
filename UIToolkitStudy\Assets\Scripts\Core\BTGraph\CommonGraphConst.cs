﻿/*
 * Description:     Blackboard.cs
 * Author:          TonyTang
 * Create Date:     2023/09/14
 */

using System;

namespace TCommonGraph
{
    /// <summary>
    /// CommonGraphConst.cs
    /// 通用节点常量数据
    /// </summary>
    public static class CommonGraphConst
    {
        /// <summary>
        /// Bool类型信息
        /// </summary>
        public static readonly Type BoolType = typeof(bool);

        /// <summary>
        /// Int类型信息
        /// </summary>
        public static readonly Type IntType = typeof(int);

        /// <summary>
        /// Float类型信息
        /// </summary>
        public static readonly Type FloatType = typeof(float);

        /// <summary>
        /// String类型信息
        /// </summary>
        public static readonly Type StringType = typeof(string);

        /// <summary>
        /// 黑板数据Bool类型信息
        /// </summary>
        public static readonly Type BlackboardBoolType = typeof(BlackboardData<bool>);

        /// <summary>
        /// Int类型信息
        /// </summary>
        public static readonly Type BlackboardIntType = typeof(BlackboardData<int>);

        /// <summary>
        /// Float类型信息
        /// </summary>
        public static readonly Type BlackboardFloatType = typeof(BlackboardData<float>);

        /// <summary>
        /// String类型信息
        /// </summary>
        public static readonly Type BlackboardStringType = typeof(BlackboardData<string>);


    }
}
