﻿/*
 * Description:             TestGraphView.cs
 * Author:                  TONYTANG
 * Create Date:             2023/07/06
 */

using UnityEditor;
using UnityEditor.Experimental.GraphView;
using UnityEngine;
using UnityEngine.UIElements;

/// <summary>
/// TestGraphView.cs
/// 测试GraphView
/// </summary>
public class TestGraphView : GraphView
{
    public TestGraphView()
    {
        LoadStyleSheet();
        AddAllManipulator();
        AddGridBackground();
    }

    /// <summary>
    /// 加载Style Sheet
    /// </summary>
    protected virtual void LoadStyleSheet()
    {
        var styleSheet = AssetDatabase.LoadAssetAtPath<StyleSheet>("Assets/Scripts/Editor/UIToolkitStudy/UITestGraphViewEditorWindow/UITestGraphViewEditorWindow.uss");
        styleSheets.Add(styleSheet);
    }

    /// <summary>
    /// 添加所有Manipulator
    /// </summary>
    protected virtual void AddAllManipulator()
    {
        this.AddManipulator(new ContentDragger());
        this.AddManipulator(new SelectionDragger());
        this.AddManipulator(new RectangleSelector());
        this.AddManipulator(new ContentZoomer());
    }

    /// <summary>
    /// 添加网格背景
    /// </summary>
    protected virtual void AddGridBackground()
    {
        var grid = new GridBackground();
        Insert(0, grid);
        grid.StretchToParentSize();
    }

    //public override void BuildContextualMenu(ContextualMenuPopulateEvent evt)
    //{
    //    base.BuildContextualMenu(evt);

    //    if (evt.target is GraphView || evt.target is Node)
    //    {
    //        evt.menu.AppendAction("Testing", (e) => { Debug.Log("Test"); });
    //    }
    //}

    /// <summary>
    /// 响应菜单栏添加
    /// </summary>
    /// <param name="evt"></param>
    public override void BuildContextualMenu(ContextualMenuPopulateEvent evt)
    {
        base.BuildContextualMenu(evt);
        evt.menu.AppendAction("测试子菜单/子菜单节点", null);
        Debug.Log($"添加子菜单:测试子菜单/子菜单节点");
    }
}