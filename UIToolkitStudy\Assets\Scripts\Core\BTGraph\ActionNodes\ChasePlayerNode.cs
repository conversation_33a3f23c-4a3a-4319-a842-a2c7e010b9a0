﻿/*
 * Description:     ChasePlayerNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/12/06
 */

using System;
using System.Collections.Generic;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// ChasePlayerNode.cs
    /// 追逐玩家节点
    /// </summary>
    public class ChasePlayerNode : BaseActionNode
    {
        /// <summary>
        /// 执行开始
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            DoChasePlayer();
        }

        /// <summary>
        /// 执行打断
        /// </summary>
        protected override void DoAbort()
        {
            base.DoAbort();
            Stop(false);
        }

        /// <summary>
        /// 执行运行完毕流程
        /// </summary>
        protected override void DoStop()
        {
            base.DoStop();
            RemoveAllTimers();
        }

        /// <summary>
        /// 移除所有Timer
        /// </summary>
        protected void RemoveAllTimers()
        {
            mOwnerTreeData.Clock.RemoveTimer(ChasePlayerTimer);
        }

        /// <summary>
        /// 执行追逐玩家
        /// </summary>
        private void DoChasePlayer()
        {
            var bindActor = mOwnerTreeData.OwnerGraphData.OwnerBT.GetBindActor();
            if(bindActor == null)
            {
                var bindUID = mOwnerTreeData.OwnerGraphData.OwnerBT.BindUID;
                Debug.LogError($"找不到绑定UID:{bindUID}对象，追逐玩家失败1！");
                Stop(false);
                return;
            }
            var playerActorList = ActorManager.Singleton.GetAllActorsByType(ActorType.PLAYER);
            var playerActor = (playerActorList != null && playerActorList.Count > 0) ? playerActorList[0] : null;
            if(playerActor == null)
            {
                Debug.LogError($"找不到主玩家，追逐玩家失败1！");
                Stop(false);
                return;
            }
            bindActor.GoToPosition(playerActor.GetPosition());
            mOwnerTreeData.Clock.RemoveTimer(ChasePlayerTimer);
            mOwnerTreeData.Clock.AddTimer(1, -1, ChasePlayerTimer);
        }

        /// <summary>
        /// 追逐主玩家定时器
        /// </summary>
        private void ChasePlayerTimer()
        {
            var bindActor = mOwnerTreeData.OwnerGraphData.OwnerBT.GetBindActor();
            if(bindActor == null)
            {
                var bindUID = mOwnerTreeData.OwnerGraphData.OwnerBT.BindUID;
                Debug.LogError($"找不到绑定UID:{bindUID}对象，追逐主玩家失败2！");
                Stop(false);
                return;
            }
            var playerActorList = ActorManager.Singleton.GetAllActorsByType(ActorType.PLAYER);
            var playerActor = (playerActorList != null && playerActorList.Count > 0) ? playerActorList[0] : null;
            if (playerActor == null)
            {
                Debug.LogError($"找不到主玩家，追逐玩家失败2！");
                Stop(false);
                return;
            }
            var playerDistance = Vector3.Distance(bindActor.GetPosition(), playerActor.GetPosition());
            if(playerDistance > 3)
            {
                DoChasePlayer();
            }
            else
            {
                bindActor.StopMove();
                Stop(true);
            }
        }
    }
}
