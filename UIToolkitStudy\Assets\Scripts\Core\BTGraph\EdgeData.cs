﻿/*
 * Description:             EdgeData.cs
 * Author:                  TONYTANG
 * Create Date:             2023/07/05
 */

using System;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// EdgeData.cs
    /// 边数据
    /// </summary>
    [Serializable]
    public class EdgeData
    {
        /// <summary>
        /// 唯一ID
        /// </summary>
        [Header("唯一ID")]
        public string GUID;

        /// <summary>
        /// 输出节点GUID
        /// </summary>
        [Header("输出节点GUID")]
        public string OutputNodeGUID;

        /// <summary>
        /// 输出端口名
        /// </summary>
        [Header("输出端口名")]
        public string OutputPortName;

        /// <summary>
        /// 输出端口类型全名
        /// </summary>
        [Header("输出端口类型全名")]
        public string OutputPortTypeFullName;

        /// <summary>
        /// 输出端口边索引
        /// </summary>
        [Header("输出端口边索引")]
        public int OutputPortEdgeIndex;

        /// <summary>
        /// 输入节点GUID
        /// </summary>
        [Header("输入节点GUID")]
        public string InputNodeGUID;

        /// <summary>
        /// 输入端口名
        /// </summary>
        [Header("输入端口名")]
        public string InputPortName;

        /// <summary>
        /// 输入端口类型全名
        /// </summary>
        [Header("输入端口类型全名")]
        public string InputPortTypeFullName;

        public EdgeData()
        {

        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="guid"></param>
        /// <param name="outputNodeGUID"></param>
        /// <param name="outputPortName"></param>
        /// <param name="outputPortTypeFullName"></param>
        /// <param name="inputNodeGUID"></param>
        /// <param name="inputPortName"></param>
        /// <param name="inputPortTypeFullName"></param>
        public EdgeData(string guid, string outputNodeGUID, string outputPortName, string outputPortTypeFullName, string inputNodeGUID, string inputPortName, string inputPortTypeFullName)
        {
            GUID = guid;
            OutputNodeGUID = outputNodeGUID;
            OutputPortName = outputPortName;
            OutputPortTypeFullName = outputPortTypeFullName;
            OutputPortEdgeIndex = 0;
            InputNodeGUID = inputNodeGUID;
            InputPortName = inputPortName;
            InputPortTypeFullName = inputPortTypeFullName;
        }

        /// <summary>
        /// 更新边GUID
        /// </summary>
        /// <param name="guid"></param>
        public void UpdateEdgeGUID(string guid)
        {
            GUID = guid;
        }

        /// <summary>
        /// 更新输出端口节点GUID
        /// </summary>
        /// <param name="outputGUID"></param>
        public void UpdateOutputNodeGUID(string outputGUID)
        {
            OutputNodeGUID = outputGUID;
        }

        /// <summary>
        /// 更新输入端口节点GUID
        /// </summary>
        /// <param name="inputGUID"></param>
        public void UpdateInputNodeGUID(string inputGUID)
        {
            InputNodeGUID = inputGUID;
        }

        /// <summary>
        /// 更新输出端口边索引
        /// </summary>
        /// <param name="outputPortEdgeIndex"></param>
        public void UpdateEdgeOutputPortIndex(int outputPortEdgeIndex)
        {
            OutputPortEdgeIndex = outputPortEdgeIndex;
        }

        /// <summary>
        /// 是否是指定节点的输出边
        /// </summary>
        /// <param name="nodeGUID"></param>
        /// <returns></returns>
        public bool IsNodeOutputEdge(string nodeGUID)
        {
            return string.Equals(OutputNodeGUID, nodeGUID);
        }

        /// <summary>
        /// 是否是指定节点的输入边
        /// </summary>
        /// <param name="nodeGUID"></param>
        /// <returns></returns>
        public bool IsNodeInputEdge(string nodeGUID)
        {
            return string.Equals(InputNodeGUID, nodeGUID);
        }
    }
}