﻿/*
 * Description:     TreeData.cs
 * Author:          TonyTang
 * Create Date:     2023/07/03
 */

using System;
using System.Collections.Generic;
using UnityEngine;
using NPBehave;

namespace TCommonGraph
{
    /// <summary>
    /// 树数据
    /// </summary>
    [Serializable]
    public class TreeData : IRecycle
    {
        /// <summary>
        /// 所属图数据
        /// </summary>
        public BehaviourTreeGraphData OwnerGraphData
        {
            get;
            private set;
        }

        /// <summary>
        /// 根节点
        /// </summary>
        public BaseNode RootNode
        {
            get;
            private set;
        }

        /// <summary>
        /// 黑板
        /// </summary>
        public Blackboard Blackboard
        {
            get;
            private set;
        }

        /// <summary>
        /// 时钟
        /// </summary>
        public Clock Clock
        {
            get;
            private set;
        }

        /// <summary>
        /// 所有节点数据列表
        /// </summary>
        private List<BaseNode> mAllNodeList;

        /// <summary>
        /// 所有边数据列表
        /// </summary>
        private List<EdgeData> mAllEdgeDataList;

        /// <summary>
        /// 节点数据Map<节点GUID, 节点数据>
        /// </summary>
        private Dictionary<string, BaseNode> mNodeMap;

        /// <summary>
        /// 输出端口边数据Map<输出节点GUID, <输入节点GUID, 边数据>>
        /// </summary>
        private Dictionary<string, Dictionary<string, EdgeData>> mOutputPortEdgeDataMap;

        /// <summary>
        /// 初始化是否完成
        /// </summary>
        protected bool mInitComplete;

        public TreeData()
        {
            mAllNodeList = new List<BaseNode>();
            mAllEdgeDataList = new List<EdgeData>();
            mNodeMap = new Dictionary<string, BaseNode>();
            mOutputPortEdgeDataMap = new Dictionary<string, Dictionary<string, EdgeData>>();
            mInitComplete = false;
        }

        /// <summary>
        /// 出池
        /// </summary>
        public void OnCreate()
        {
            ResetData();
        }

        /// <summary>
        /// 入池
        /// </summary>
        public void OnDispose()
        {
            ResetData();
        }

        /// <summary>
        /// 重置数据
        /// </summary>
        private void ResetData()
        {
            OwnerGraphData = null;
            RootNode = null;
            Blackboard = null;
            Clock = null;
            mAllNodeList.Clear();
            mAllEdgeDataList.Clear();
            mNodeMap.Clear();
            mInitComplete = false;
        }

        /// <summary>
        /// 初始化指定图数据
        /// </summary>
        /// <param name="graphData"></param>
        /// <param name="rootNode"></param>
        /// <param name="blackboard"></param>
        /// <param name="clock"></param>
        public void SetData(BehaviourTreeGraphData graphData, BaseNode rootNode, Blackboard blackboard, Clock clock)
        {
            OwnerGraphData = graphData;
            RootNode = rootNode;
            Blackboard = blackboard;
            Clock = clock;
            InitAllNodeAndEdgeDatas();
            mInitComplete = true;
        }

        /// <summary>
        /// 开始运行树数据
        /// </summary>
        public void Start()
        {
            if(!mInitComplete)
            {
                Debug.LogError($"树数据未初始化完成，开始运行树数据失败！");
                return;
            }
            RootNode?.Start();
        }

        /// <summary>
        /// 停止运行树数据
        /// </summary>
        public void Stop()
        {
            if (!mInitComplete)
            {
                Debug.LogError($"树数据未初始化完成，停止运行树数据失败！");
                return;
            }
            RootNode?.Abort();
        }

        /// <summary>
        /// 初始化所有节点和边数据
        /// </summary>
        private void InitAllNodeAndEdgeDatas()
        {
            mAllNodeList.Clear();
            OwnerGraphData.GetAllChildNodeList(ref mAllNodeList, RootNode, true);
            mAllEdgeDataList.Clear();
            OwnerGraphData.GetAllChildEdgeDataList(ref mAllEdgeDataList, RootNode, true);
            InitNodeMapData();
            InitEdgeMapData();
            InitNodesTreeData();
        }

        /// <summary>
        /// 初始化节点数据Map
        /// </summary>
        private void InitNodeMapData()
        {
            mNodeMap.Clear();
            foreach (var node in mAllNodeList)
            {
                if (node == null)
                {
                    Debug.LogError($"有空节点，请检查代码，跳过此树节点添加！");
                    continue;
                }
                mNodeMap.Add(node.GUID, node);
            }
        }

        /// <summary>
        /// 初始化边数据Map
        /// </summary>
        private void InitEdgeMapData()
        {
            mOutputPortEdgeDataMap.Clear();
            foreach(var edgeData in mAllEdgeDataList)
            {
                if(edgeData == null)
                {
                    Debug.LogError($"有空边数据，请检查代码，调过此边数据添加！");
                    continue;
                }
                var outputNodeGUID = edgeData.OutputNodeGUID;
                var inputNodeGUID = edgeData.InputNodeGUID;
                if(!mOutputPortEdgeDataMap.ContainsKey(outputNodeGUID))
                {
                    mOutputPortEdgeDataMap.Add(outputNodeGUID, new Dictionary<string, EdgeData>());
                }
                if(!mOutputPortEdgeDataMap[outputNodeGUID].ContainsKey(inputNodeGUID))
                {
                    mOutputPortEdgeDataMap[outputNodeGUID].Add(inputNodeGUID, edgeData);
                }
                else
                {
                    Debug.LogError($"不应该出现一个输出节点对多个同一个输入端口节点，请检查配置！");
                }
            }
        }

        /// <summary>
        /// 初始化节点树数据
        /// </summary>
        private void InitNodesTreeData()
        {
            foreach (var node in mAllNodeList)
            {
                if(node == null)
                {
                    continue;
                }
                node.SetOwnerTreeData(this);
                List<BaseNode> childNodeList = new List<BaseNode>();
                OwnerGraphData.GetAllChildNodeList(ref childNodeList, node);
                childNodeList.Remove(node);
                foreach(var childNode in childNodeList)
                {
                    if(childNode == null)
                    {
                        continue;
                    }
                    childNode.SetParentNode(node as BaseParentNode);
                }
                var childCount = childNodeList.Count;
                if (childCount > 0)
                {
                    var parentNode = node as BaseParentNode;
                    parentNode.SetChildNodeList(childNodeList);
                    if(node is BaseDecorationNode && childCount > 1)
                    {
                        Debug.LogError($"修饰节点名:{node.name},GUID:{node.GUID}有超过1个数量的子节点，请检查配置！");
                    }
                }
                node.SetClock(Clock);
            }
        }

        /// <summary>
        /// 获取指定GUID的节点
        /// </summary>
        /// <param name="nodeGUID"></param>
        /// <returns></returns>
        public BaseNode GetNodeByGUID(string nodeGUID)
        {
            BaseNode node;
            if (mNodeMap.TryGetValue(nodeGUID, out node))
            {
                return node;
            }
            return null;
        }

        /// <summary>
        /// 获取指定节点相连的所有子节点列表(含自身)
        /// </summary>
        /// <param name="allNodeList"></param>
        /// <param name="node"></param>
        /// <param name="recusive"></param>
        public void GetAllChildNodeList(ref List<BaseNode> allNodeList, BaseNode node, bool recusive = false)
        {
            if (node == null)
            {
                return;
            }
            if (!allNodeList.Contains(node))
            {
                allNodeList.Add(node);
            }
            List<BaseNode> searchChildNodeList = new List<BaseNode>();
            foreach (var edgeData in mAllEdgeDataList)
            {
                if (edgeData.IsNodeOutputEdge(node.GUID))
                {
                    var connectedChildNode = GetNodeByGUID(edgeData.InputNodeGUID);
                    // 需要搜索的子节点必须是没搜索过且不重复的，避免循环连接后导致死循环
                    if (!allNodeList.Contains(connectedChildNode))
                    {
                        allNodeList.Add(connectedChildNode);
                        if (!searchChildNodeList.Contains(connectedChildNode))
                        {
                            searchChildNodeList.Add(connectedChildNode);
                        }
                    }
                }
            }
            if (recusive)
            {
                foreach (var searchChildNode in searchChildNodeList)
                {
                    GetAllChildNodeList(ref allNodeList, searchChildNode, recusive);
                }
            }
        }

        /// <summary>
        /// 获取指定节点相连的所有节点相关边数据列表
        /// </summary>
        /// <param name="allEdgeDataList"></param>
        /// <param name="node"></param>
        /// <param name="recusive"></param>
        public void GetAllChildEdgeDataList(ref List<EdgeData> allEdgeDataList, BaseNode node, bool recusive = false)
        {
            if (node == null)
            {
                return;
            }
            if (mAllEdgeDataList == null || mAllEdgeDataList.Count == 0)
            {
                return;
            }
            List<BaseNode> searchChildNodeList = new List<BaseNode>();
            foreach (var edgeData in mAllEdgeDataList)
            {
                if (edgeData.IsNodeOutputEdge(node.GUID))
                {
                    var connectedChildNode = GetNodeByGUID(edgeData.InputNodeGUID);
                    // 需要搜索的子节点必须是没搜索过且不重复的，避免循环连接后导致死循环
                    if (!allEdgeDataList.Contains(edgeData))
                    {
                        allEdgeDataList.Add(edgeData);
                        if (!searchChildNodeList.Contains(connectedChildNode))
                        {
                            searchChildNodeList.Add(connectedChildNode);
                        }
                    }
                }
            }
            if (recusive)
            {
                foreach (var searchCHildNode in searchChildNodeList)
                {
                    GetAllChildEdgeDataList(ref allEdgeDataList, searchCHildNode, recusive);
                }
            }
        }
    }
}