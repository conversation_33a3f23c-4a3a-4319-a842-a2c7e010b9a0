﻿/*
 * Description:     PropertyBindCBSO.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/06/05
 */

using System;
using UnityEngine;

/// <summary>
/// PropertyBindCBSO.cs
/// 属性绑定回调ScriptableObject
/// </summary>
[CreateAssetMenu(fileName = "PropertyBindCBSO", menuName = "ScriptableObjects/DIY/PropertyBindCBSO", order = 1)]
public class PropertyBindCBSO : ScriptableObject
{
    /// <summary>
    /// 名字
    /// </summary>
    [Header("名字")]
    public string Name;

    /// <summary>
    /// 位置
    /// </summary>
    [Header("位置")]
    public Vector3 Position;

    /// <summary>
    /// 颜色
    /// </summary>
    [Header("颜色")]
    public Color Color;

    /// <summary>
    /// 绑定GameObject
    /// </summary>
    [Header("绑定GameObject")]
    public GameObject GO;
}