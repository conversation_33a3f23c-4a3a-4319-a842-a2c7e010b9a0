Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.4f1c1 (64682593795a) revision 6580261'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Core' Language: 'zh' Physical Memory: 16302 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
G:\Software\UnityAllVersions\2021.3.4f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
G:/Projects/GitHubProjects/UIToolkitStudy/UIToolkitStudy
-logFile
Logs/AssetImportWorker0.log
-srvPort
56792
Successfully changed project path to: G:/Projects/GitHubProjects/UIToolkitStudy/UIToolkitStudy
G:/Projects/GitHubProjects/UIToolkitStudy/UIToolkitStudy
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 232.24 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.4f1c1 (64682593795a)
[Subsystems] Discovering subsystems at path G:/Software/UnityAllVersions/2021.3.4f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path G:/Projects/GitHubProjects/UIToolkitStudy/UIToolkitStudy/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1050 Ti (ID=0x1c82)
    Vendor:   NVIDIA
    VRAM:     4004 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'G:/Software/UnityAllVersions/2021.3.4f1c1/Editor/Data/Managed'
Mono path[1] = 'G:/Software/UnityAllVersions/2021.3.4f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'G:/Software/UnityAllVersions/2021.3.4f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56584
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: G:/Software/UnityAllVersions/2021.3.4f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: G:/Software/UnityAllVersions/2021.3.4f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.031419 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 51.13 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.850 seconds
Domain Reload Profiling:
	ReloadAssembly (1851ms)
		BeginReloadAssembly (885ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (2ms)
		EndReloadAssembly (773ms)
			LoadAssemblies (879ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (182ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (55ms)
			SetupLoadedEditorAssemblies (450ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (84ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (51ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (185ms)
				ProcessInitializeOnLoadMethodAttributes (126ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.005113 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.29 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.520 seconds
Domain Reload Profiling:
	ReloadAssembly (2522ms)
		BeginReloadAssembly (1145ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (1237ms)
			LoadAssemblies (1604ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (196ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (50ms)
			SetupLoadedEditorAssemblies (349ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (0ms)
				BeforeProcessingInitializeOnLoad (59ms)
				ProcessInitializeOnLoadAttributes (239ms)
				ProcessInitializeOnLoadMethodAttributes (23ms)
				AfterProcessingInitializeOnLoad (9ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
