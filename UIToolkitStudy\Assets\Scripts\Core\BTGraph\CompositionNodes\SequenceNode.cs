﻿/*
 * Description:     SequenceNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/06/28
 */

using System;

namespace TCommonGraph
{
    /// <summary>
    /// SequenceNode.cs
    /// 行为树顺序节点(多个节点按顺序执行直到遇到返回失败的节点)
    /// </summary>
    [Serializable]
    public class SequenceNode : BaseCompositionNode
    {
        /// <summary>
        /// 当前执行节点索引
        /// </summary>
        protected int mCurrentIndex = -1;

        /// <summary>
        /// 响应开始
        /// </summary>
        protected override void DoStart()
        {
            base.DoStart();
            mCurrentIndex = -1;
            ProcessChildren();
        }

        /// <summary>
        /// 响应打断
        /// </summary>
        protected override void DoAbort()
        {
            base.DoAbort();
            if(mChildNodeList != null)
            {
                mChildNodeList[mCurrentIndex].Abort();
            }
        }

        /// <summary>
        /// 响应子节点停止
        /// </summary>
        /// <param name="child"></param>
        /// <param name="success"></param>
        protected override void DoChildStop(BaseNode child, bool success)
        {
            base.DoChildStop(child, success);
            if(success)
            {
                ProcessChildren();
            }
            else
            {
                Stop(false);
            }
        }

        /// <summary>
        /// 执行子节点
        /// </summary>
        protected void ProcessChildren()
        {
            mCurrentIndex++;
            if (mCurrentIndex < ChildNodeCount)
            {
                if (IsAbort)
                {
                    Stop(false);
                }
                else
                {
                    mChildNodeList[mCurrentIndex].Start();
                }
            }
            else
            {
                Stop(true);
            }
        }

        /// <summary>
        /// 停止比指定子节点优先级低的节点，并决定是否重新开启组合节点
        /// </summary>
        /// <param name="child"></param>
        /// <param name="immediateRestart"></param>
        public override void StopLowerPriorityChildrenForChild(BaseNode child, bool immediateRestart)
        {
            int indexForChild = 0;
            bool found = false;
            foreach (BaseNode currentChild in mChildNodeList)
            {
                if (currentChild == child)
                {
                    found = true;
                }
                else if (!found)
                {
                    indexForChild++;
                }
                else if (found && currentChild.IsRunning)
                {
                    if (immediateRestart)
                    {
                        mCurrentIndex = indexForChild - 1;
                    }
                    else
                    {
                        mCurrentIndex = ChildNodeCount;
                    }
                    currentChild.Abort();
                    break;
                }
            }
        }

        /// <summary>
        /// 字符串表达
        /// </summary>
        public override string ToString()
        {
            return $"{base.ToString()},mCurrentIndex:{mCurrentIndex}";
        }
    }
}