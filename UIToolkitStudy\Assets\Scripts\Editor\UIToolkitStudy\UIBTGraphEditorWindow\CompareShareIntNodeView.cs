﻿/*
 * Description:             CompareShareIntNodeView.cs
 * Author:                  TONYTANG
 * Create Date:             2023/12/05
 */

using UnityEngine.UIElements;

namespace TCommonGraph
{
    /// <summary>
    /// CompareShareIntNodeView.cs
    /// 黑板int数据比较显示节点类型
    /// </summary>
    public class CompareShareIntNodeView : BaseCompareShareNodeView
    {
        /// <summary>
        /// 创建节点自定义UI
        /// </summary>
        protected override void CreateCustomUI()
        {
            base.CreateCustomUI();
            var nodeVerticalUIContainer = this.Q<VisualElement>(BTGraphElementNames.NodeUIVerticalContainerName);
            var nodeDesDivider = UIToolkitUtilities.CreateHorizontalDivider(BTGraphConstEditor.DividerColor);
            nodeVerticalUIContainer.Add(nodeDesDivider);

            UIToolkitUtilities.CreateBindSOPropertyField(nodeVerticalUIContainer, NodeData, "TargetValue");
        }
    }
}
