﻿/*
 * Description:     PropertyBindCBElementNames.cs
 * Author:          Tony<PERSON><PERSON>
 * Create Date:     2023/06/05
 */

/// <summary>
/// PropertyBindCBElementNames.cs
/// 属性绑定回调Element名字
/// </summary>
public static class PropertyBindCBElementNames
{
    /// <summary>
    /// 属性绑定回调文本名
    /// </summary>
    public const string PropertyBindCBTextFieldName = "PropertyBindCBTextField";

    /// <summary>
    /// 绑定对象属性变化回调文本名
    /// </summary>
    public const string SerializedBindCBTextFieldName = "SerializedBindCBTextField";
}