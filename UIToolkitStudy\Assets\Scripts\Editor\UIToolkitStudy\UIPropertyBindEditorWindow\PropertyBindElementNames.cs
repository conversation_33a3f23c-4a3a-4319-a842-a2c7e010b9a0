﻿/*
 * Description:     PropertyBindElementNames.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/05/29
 */

using System;
using System.Collections.Generic;

/// <summary>
/// PropertyBindElementNames.cs
/// 属性绑定组件名
/// </summary>
public static class PropertyBindElementNames
{
    /// <summary>
    /// 属性绑定文本组件名
    /// </summary>
    public const string PropertyBindTextFieldName = "PropertyBindTextField";

    /// <summary>
    /// 属性绑定信息显示容器名
    /// </summary>
    public const string PropertyBindInfoContentContainerName = "PropertyBindInfoContentContainer";
}