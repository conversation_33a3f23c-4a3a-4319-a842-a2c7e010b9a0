﻿/*
 * Description:     BaseConditionNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/06/19
 */

namespace TCommonGraph
{
    /// <summary>
    /// BaseConditionNode.cs
    /// 行为树条件节点基类
    /// </summary>
    public abstract class BaseConditionNode : BaseNode
    {
        /// <summary>
        /// 节点类型
        /// </summary>
        public override NodeType NodeType
        {
            get
            {
                return NodeType.Condition;
            }
        }
    }
}