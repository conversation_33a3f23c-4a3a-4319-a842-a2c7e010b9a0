%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9109590619700131618
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 933cdc95477323349bb9646301e5fd42, type: 3}
  m_Name: SequenceNode
  m_EditorClassIdentifier: 
  GUID: f64e1d8a-7475-4df2-9b6e-4173e4fc480a
  Position:
    serializedVersion: 2
    x: 524
    y: 100
    width: 314
    height: 161
  Des: "\u884C\u4E3A\u6811\u6267\u884C\u8BBE\u8BA1\u8BF4\u660E\u987A\u5E8F\u8282\u70B9"
--- !u!114 &-6252684333048061732
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7deee2c12e667b14cac8d1f36f1443d6, type: 3}
  m_Name: WaitTimeNode
  m_EditorClassIdentifier: 
  GUID: 533945d4-960e-483a-9019-82e0adaf24fe
  Position:
    serializedVersion: 2
    x: 991.875
    y: -177.21501
    width: 100
    height: 150
  Des: "\u7B49\u5F855\u79D2"
  WaitTime: 5
--- !u!114 &-1176053334530285623
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d9393f54cde612f45b16a9bdcbe437f1, type: 3}
  m_Name: RootNode
  m_EditorClassIdentifier: 
  GUID: b8cd0fc7-a3d6-44b2-8789-5ff310da425a
  Position:
    serializedVersion: 2
    x: 100
    y: 100
    width: 100
    height: 150
  Des: "\u8282\u70B9\u63CF\u8FF0"
  IsRunningOnce: 0
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06624dd836b59874d896d8cbd4ee9390, type: 3}
  m_Name: BTExecuteFlowIntro
  m_EditorClassIdentifier: 
  GraphOrientation: 0
  AllNodeList:
  - {fileID: -1176053334530285623}
  - {fileID: -9109590619700131618}
  - {fileID: -6252684333048061732}
  - {fileID: 1889985007651318989}
  AllEdgeDataList:
  - GUID: d9e43d63-c9ca-43b0-bc20-76ac38be70a8
    OutputNodeGUID: b8cd0fc7-a3d6-44b2-8789-5ff310da425a
    OutputPortName: Out
    OutputPortTypeFullName: System.Single
    OutputPortEdgeIndex: 0
    InputNodeGUID: f64e1d8a-7475-4df2-9b6e-4173e4fc480a
    InputPortName: In
    InputPortTypeFullName: System.Single
  - GUID: 42db4dbe-7e43-4ab6-927c-34cfb727aa86
    OutputNodeGUID: f64e1d8a-7475-4df2-9b6e-4173e4fc480a
    OutputPortName: Out
    OutputPortTypeFullName: System.Single
    OutputPortEdgeIndex: 0
    InputNodeGUID: 533945d4-960e-483a-9019-82e0adaf24fe
    InputPortName: In
    InputPortTypeFullName: System.Single
  - GUID: 0975c104-7a69-4c01-bad8-a1a88a6ae4bd
    OutputNodeGUID: f64e1d8a-7475-4df2-9b6e-4173e4fc480a
    OutputPortName: Out
    OutputPortTypeFullName: System.Single
    OutputPortEdgeIndex: 1
    InputNodeGUID: 1b1e6771-c74a-42be-8ee1-671f66277666
    InputPortName: In
    InputPortTypeFullName: System.Single
  BlackboardData:
    AllBoolDataList: []
    AllIntDataList: []
    AllFloatDataList: []
    AllStringDataList: []
--- !u!114 &1889985007651318989
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59b7b166315eb194f895a00b916e388d, type: 3}
  m_Name: LogNode
  m_EditorClassIdentifier: 
  GUID: 1b1e6771-c74a-42be-8ee1-671f66277666
  Position:
    serializedVersion: 2
    x: 1016
    y: 321
    width: 311
    height: 187
  Des: "\u6253\u5370\u884C\u4E3A\u6811\u6267\u884C\u8BBE\u8BA1\u8BF4\u660ELog"
  LogContent: "\u6253\u5370\u884C\u4E3A\u6811\u6267\u884C\u8BBE\u8BA1\u8BF4\u660ELog"
