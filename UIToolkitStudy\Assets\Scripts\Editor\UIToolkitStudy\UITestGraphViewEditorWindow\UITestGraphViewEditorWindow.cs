﻿/*
 * Description:             UITestGraphViewEditorWindow.cs
 * Author:                  TONYTANG
 * Create Date:             2023/07/06
 */

using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;

/// <summary>
/// UITestGraphViewEditorWindow.cs
/// 测试GraphView窗口
/// </summary>
public class UITestGraphViewEditorWindow : EditorWindow
{    
    /// <summary>
    /// 测试GraphView
    /// </summary>
    private TestGraphView mTestGraphView;

    /// <summary>
    /// 打开测试GraphView编辑器窗口
    /// </summary>
    [MenuItem("Window/UI Toolkit/UIToolkitStudy/UITestGraphViewEditorWindow")]
    public static void ShowUICoomonGraphEditorWindow()
    {
        UITestGraphViewEditorWindow wnd = GetWindow<UITestGraphViewEditorWindow>();
        wnd.titleContent = new GUIContent("UITestGraphViewEditorWindow");
    }

    private void CreateGUI()
    {
        var graphViewContainer = new VisualElement();
        graphViewContainer.style.left = 0;
        graphViewContainer.style.right = 0;
        graphViewContainer.style.top = 0;
        graphViewContainer.style.bottom = 0;
        graphViewContainer.style.flexDirection = FlexDirection.Column;
        graphViewContainer.style.flexGrow = 1;
        graphViewContainer.AddToClassList("unity-box");
        mTestGraphView = new TestGraphView();
        mTestGraphView.StretchToParentSize();
        graphViewContainer.Add(mTestGraphView);
        rootVisualElement.Add(graphViewContainer);
    }
}