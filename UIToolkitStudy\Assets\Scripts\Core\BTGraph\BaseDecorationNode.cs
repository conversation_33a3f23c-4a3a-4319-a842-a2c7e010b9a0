﻿/*
 * Description:     BaseDecorationNode.cs
 * Author:          <PERSON><PERSON><PERSON>
 * Create Date:     2023/06/19
 */

using System;
using UnityEngine;

namespace TCommonGraph
{
    /// <summary>
    /// BaseDecorationNode.cs
    /// 装饰节点基类
    /// </summary>
    [Serializable]
    public abstract class BaseDecorationNode : BaseParentNode
    {
        /// <summary>
        /// 节点类型
        /// </summary>
        public override NodeType NodeType
        {
            get
            {
                return NodeType.Decoration;
            }
        }

        /// <summary>
        /// 修饰子节点
        /// </summary>
        public BaseNode DecorateNode
        {
            get
            {
                if(mChildNodeList == null || ChildNodeCount == 0)
                {
                    return null;
                }
                else
                {
                    return mChildNodeList[0];
                }
            }
        }

        /// <summary>
        /// 父组合节点停止
        /// </summary>
        /// <param name="parentNode"></param>
        public override void ParentCompositeStop(BaseCompositionNode parentNode)
        {
            base.ParentCompositeStop(parentNode);
            // 支持装饰节点修饰装饰节点的情况
            DecorateNode?.ParentCompositeStop(parentNode);
        }
    }
}