﻿using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;
using UnityEditor.UIElements;

/// <summary>
/// 使用USS的EditorWindow
/// </summary>
public class UIUseUSSEditorWindow : EditorWindow
{
    [MenuItem("Window/UI Toolkit/UIToolkitStudy/UIUseUSSEditorWindow")]
    public static void ShowUIUseUSSEditorWidnow()
    {
        UIUseUSSEditorWindow wnd = GetWindow<UIUseUSSEditorWindow>();
        wnd.titleContent = new GUIContent("UIUseUSSEditorWindow");
    }

    public void CreateGUI()
    {
        CreateUIByUseUSS();
    }

    /// <summary>
    /// 使用USS创建UI
    /// </summary>
    private void CreateUIByUseUSS()
    {
        VisualElement root = rootVisualElement;
        // 加载并指定USS的使用
        var styleSheet = AssetDatabase.LoadAssetAtPath<StyleSheet>("Assets/Scripts/Editor/UIToolkitStudy/UIUseUSSEditorWindow/UIUseUSSEditorWindow.uss");
        root.styleSheets.Add(styleSheet);

        VisualElement smallLabelWithStyle = new Label("USS Using SmallLabel Class Selector!");
        smallLabelWithStyle.AddToClassList("SmallLabel");
        VisualElement bigLabelWithStyle = new Label("USS Using BigLabel Class Selector!");
        smallLabelWithStyle.AddToClassList("BigLabel");
        VisualElement labelWithStyle = new Label("USS Using Label Type Selector!");
        VisualElement boldLabelWithStyle = new Label("USS Using BoldLabel Class Selector!");
        boldLabelWithStyle.AddToClassList("BoldLabel");
        root.Add(smallLabelWithStyle);
        root.Add(bigLabelWithStyle);
        root.Add(labelWithStyle);
        root.Add(boldLabelWithStyle);

        Button ussCenterButton = new Button();
        ussCenterButton.name = "CenterButton";
        ussCenterButton.text = "Center USS Button Name Selector";
        root.Add(ussCenterButton);
    }
}